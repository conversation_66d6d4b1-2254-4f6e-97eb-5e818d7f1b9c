<template>
  <div class="position-relative box-border w-[343px] h-[72px] mx-auto">
    <img
      alt=""
      class="background-image"
      src="../img/<EMAIL>"
    >
    <div class="pl-[80px] pt-[12px]">
      <div>
        <div class="text-[14px] text-[#333333]">
          您的个人网站资料评分
          <span class="text-[#EB4738] text-[18px] font-bold mx-[4px]">{{
            score
          }}</span>
          分
        </div>
        <div class="text-[12px] text-[#999999] mt-[4px]">
          完善更多资料可提高网站曝光排名
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { lawyerDataGetScore } from "@/api/user";

export default {
  name: "ProfileHeaderScore",
  data() {
    return {
      score: 0,
    };
  },
  created() {
    this.getScore();
  },
  methods: {
    /** 获取评分 */
    getScore() {
      lawyerDataGetScore().then((res) => {
        this.score = res.data;
      });
    },
  },
};
</script>
