<template>
  <div>
    <div class="flex items-center justify-between">
      <div class="text-[14px] text-[#666666]">
        第{{ data.numberTime }}期
      </div>
      <div class="flex items-center">
        <div class="text-[12px] text-[#999999]">
          {{ data.totalPv }}人浏览
        </div>
        <div class="text-[12px] text-[#999999] ml-[12px]">
          {{ data.pubTime }}
        </div>
      </div>
    </div>
    <div
      class="flex items-center w-[317px] p-[8px] box-border bg-[#FFFFFF] rounded-[8px] mt-[12px]"
      @click="handleClick"
    >
      <img
        :src="data.coverPic"
        alt=""
        class="w-[80px] h-[80px] rounded-[8px] block shrink-0 mr-[8px]"
      >
      <div>
        <div class="text-[15px] text-[#333333]">
          {{ data.title }}
        </div>
        <div class="text-[12px] text-[#666666] mt-[12px] flex items-center">
          <img
            alt=""
            class="block w-[14px] h-[14px] mr-[2px]"
            src="@/pages/sub/order-successful/img/wbwwsc.png"
          >
          <div v-if="notRedPacket">
            沾沾喜气
          </div>
          <div v-if="canGetRedPacket">
            有<span class="text-[#3887F5]">{{ data.redPacketGetNum }}</span>人沾他喜气，还剩<span class="text-[#3887F5]">{{
              data.redPacketRemainNum
            }}</span>个现金红包
          </div>
          <div v-if="isRedPacketOver">
            有<span class="text-[#3887F5]">{{ data.redPacketGetNum }}</span>人沾他喜气，现金红包已瓜分完
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "OrderInfoCard",
  props: {
    data: {
      type: Object,
      default: () => ({}),
      required: true,
    },
  },
  computed: {
    /** 是否没有红包 */
    notRedPacket() {
      return this.data.redPacketTotalNum === 0;
    },
    /** 红包是否被领完 */
    isRedPacketOver() {
      return (
        this.data.redPacketTotalNum > 0 && this.data.redPacketRemainNum === 0
      );
    },
    /** 是否还可以领取红包 */
    canGetRedPacket() {
      return this.data.redPacketRemainNum > 0;
    },
  },
  methods: {
    handleClick() {
      this.$emit("click");
    },
  },
};
</script>
