export declare const penCache: {
    viewRect: {
        [id: string]: {
            width: number;
            height: number;
            left: number;
            top: number;
            right: number;
            bottom: number;
        } | null;
    };
    textLines: {
        [id: string]: Array<{
            text: string;
            x: number;
            y: number;
            measuredWith: number;
            textDecoration?: {
                moveTo: number[];
                lineTo: number[];
            };
        }> | null;
    };
};
export declare const clearPenCache: (id?: string) => void;
export type CSSProperties = any & Partial<{
    align: string;
    verticalAlign: string;
    mode: "aspectFill" | "scaleToFill" | "auto";
    textStyle: "normal" | "stroke";
    scalable: boolean;
    deletable: boolean;
}>;
export interface IView {
    type: "rect" | "text" | "image" | "qrcode" | "inlineText";
    text?: string;
    url?: string;
    id?: string;
    textList?: Array<{
        text: string;
        css: CSSProperties;
    }>;
    content?: string;
    /** 事实上painter中view的css属性并不完全与CSSProperties一致。 */
    /** 有一些属性painter并不支持，而当你需要开启一些“高级”能力时，属性的使用方式也与css规范不一致。 */
    /** 具体的区别我们将在下方对应的view介绍中详细讲解，在这里使用CSSProperties仅仅是为了让你享受代码提示 */
    css: CSSProperties;
    /** views子元素，只有当type为rect有效 */
    views?: Array<InnerView>;
}
export interface IPalette {
    background: string;
    width: string;
    height: string;
    borderRadius?: string;
    views: Array<IView>;
}
type InnerView = IView & Partial<{
    sWidth: number;
    sHeight: number;
    rect: Partial<{
        left: number;
        top: number;
        right: number;
        bottom: number;
        x: number;
        y: number;
        minWidth: number;
        startX: number;
        startY: number;
        endX: number;
        endY: number;
    }>;
}>;
export declare class Pen {
    private ctx;
    private data;
    style: {
        width: number;
        height: number;
    };
    constructor(ctx: CanvasRenderingContext2D, data: IPalette);
    paint(callback?: () => void): Promise<void>;
    _background(): Promise<void>;
    _drawAbsolute(view: InnerView): Promise<void>;
    _border({ borderRadius, width, height, borderWidth, borderStyle, }: {
        borderRadius?: string | number;
        width: number;
        height: number;
        borderWidth?: string | number;
        borderStyle?: string;
    }): void;
    /**
     * 根据 borderRadius 进行裁减
     */
    _doClip(borderRadius: string | number, width: number, height: number, borderStyle?: string): void;
    /**
     * 画边框
     */
    _doBorder(view: IView, width: number, height: number): void;
    _preProcess(view: InnerView, notClip?: boolean): {
        width: number;
        height: any;
        x: any;
        y: any;
        extra: any;
    };
    _doPaddings(view: IView): number[];
    _doBackground(view: IView): void;
    _drawQRCode(view: IView): void;
    _drawAbsImage(view: InnerView): Promise<void>;
    _fillAbsText(view: InnerView): void;
    _fillAbsInlineText(view: any): void;
    _drawAbsRect(view: InnerView): Promise<void>;
    _doShadow(view: InnerView): void;
    _getAngle(angle: string): number;
}
export {};
