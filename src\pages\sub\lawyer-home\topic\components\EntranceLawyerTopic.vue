<template>
  <div class="px-[12px] py-[16px] position-relative">
    <img
      :src="bgImg"
      alt=""
      class="background-image"
    >
    <div class="flex items-center">
      <img
        alt=""
        class="w-[19px] h-[19px] block"
        src="../../img/top.png"
      >
      <div class="ml-[4px] text-[18px] font-bold text-[#333333]">
        {{ title }}
      </div>
    </div>
    <div class="mt-[16px]">
      <div class="flex items-center justify-center gap-x-[12px]">
        <div
          v-for="(item, index) in topicList"
          :key="item.lawyerId"
          :class="[
            {
              'order-2 !w-[106px]': index === 0,
              'order-1': index === 1,
              'order-3': index === 2,
            },
          ]"
          class="bg-[rgba(255,255,255,0.9)] w-[92px] rounded-[8px] box-border py-[12px] text-center"
          @click="clickLawyerCard(item)"
        >
          <div
            :class="[
              {
                '!w-[52px] !h-[52px]': index === 0,
              },
            ]"
            class="w-[40px] h-[40px] mx-auto relative"
          >
            <img
              :class="[
                {
                  '!w-[52px] !h-[52px]': index === 0,
                },
              ]"
              :src="item.imgUrl"
              mode="aspectFill"
              alt=""
              class="w-[40px] h-[40px] block rounded-full"
            >
            <!-- 排行榜图标 -->
            <img
              :class="[
                {
                  '!w-[43px] !h-[17px] !top-[43px] !left-[4px]': index === 0,
                },
              ]"
              :src="getRankImg(index)"
              alt=""
              class="w-[37px] h-[14px] rounded-[40px] block absolute top-[32px] left-[1px] z-10"
            >
          </div>
          <div
            :class="[
              {
                '!text-[15px] !mt-[16px]': index === 0,
              },
            ]"
            class="mt-[14px] font-bold text-[14px] text-[#333333]"
          >
            {{ item.realName }}律师
          </div>
          <div
            :class="[
              {
                '!text-[13px]': index === 0,
              },
            ]"
            class="mt-[4px] text-[12px] text-[#666666]"
          >
            {{ bgImgIndex === 2 ? "解答" : "发布"
            }}{{ item.contentRankCount }}次
          </div>
        </div>
      </div>
      <div class="mt-[12px] px-[4px]">
        <div
          v-for="(item, index) in lawyerList"
          :key="item.lawyerId"
          class="flex items-center justify-between [&:not(:first-child)]:mt-[16px]"
          @click="clickLawyerCard(item)"
        >
          <div class="flex items-center">
            <div
              class="w-[14px] h-[13px] text-[11px] font-[DIN_Alternate,_DIN_Alternate] font-bold text-[#666666] flex items-center justify-center"
            >
              <div>
                {{ index + 4 }}
              </div>
            </div>
            <img
              :src="item.imgUrl"
              mode="aspectFill"
              alt=""
              class="w-[20px] h-[20px] block ml-[4px] rounded-full"
            >
            <div class="text-[13px] text-[#333333] ml-[4px]">
              {{ item.realName }}律师
            </div>
          </div>
          <div class="text-[12px] text-[#999999]">
            {{ bgImgIndex === 2 ? "解答" : "发布"
            }}{{ item.contentRankCount }}次
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { toLawyerHome } from "@/libs/turnPages";

export default {
  name: "EntranceLawyerTopic",
  props: {
    dataSource: {
      type: Array,
      default: () => [],
      required: true,
    },
    /** 背景图片 */
    bgImgIndex: {
      type: Number,
      default: 0,
      required: true,
      validator: (val) => {
        return val >= 0 && val <= 2;
      },
    },
    title: {
      type: String,
      default: "",
      required: true,
    },
    /** 点击律师卡片埋点 */
    clickLawyerCardBuryPoint: {
      type: String,
      default: "",
    },
  },
  computed: {
    /** 只取前10条数据 */
    rankList() {
      return this.dataSource.slice(0, 10);
    },
    /**
     * 获取排名前三的律师列表
     * @returns {Array} 排名前三的律师列表
     */
    topicList() {
      return this.rankList.slice(0, 3);
    },
    lawyerList() {
      // 前三个律师之外的
      return this.rankList.slice(3);
    },
    bgImg() {
      const img = [
        require("@/pages/sub/lawyer-home/img/Frame1321315338.png"),
        require("@/pages/sub/lawyer-home/img/Frame1321315333.png"),
        require("@/pages/sub/lawyer-home/img/Frame1321315328.png"),
      ];

      return img[this.bgImgIndex];
    },
  },
  methods: {
    /** 排行榜图标 */
    getRankImg(index) {
      const img = [
        require("@/pages/sub/lawyer-home/topic/components/img/1.png"),
        require("@/pages/sub/lawyer-home/topic/components/img/2.png"),
        require("@/pages/sub/lawyer-home/topic/components/img/3.png"),
      ];

      return img[index];
    },
    /** 点击律师卡片 */
    clickLawyerCard(item) {
      console.log(item);

      toLawyerHome({
        id: item.lawyerId,
      });
    },
  },
};
</script>
