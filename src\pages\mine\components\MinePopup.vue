<template>
  <div>
    <app-popup
      :show="show"
      bgColor="transparent"
      closeable
      mode="center"
      @cancel="handleCancel"
    >
      <div class="position-relative w-[311px] h-[397px] box-border">
        <img
          alt=""
          class="block background-image"
          src="@/pages/mine/img/Group8730.png"
        >
        <div
          class="w-[263px] h-[36px] bg-[#3887F5] rounded-[68px] flex items-center justify-center absolute bottom-[16px] absolute-x-center"
          @click="toCollaborationSettings"
        >
          <div class="font-bold text-[14px] text-[#FFFFFF]">
            立即去设置
          </div>
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { getRookie } from "@/libs/rookie";
import { toCollaborationSettings, turnToLawyerAuthResultPageToLogin } from "@/libs/turnPages";

export default {
  name: "MinePopup",
  components: { AppPopup },
  data() {
    return {
      show: false,
    };
  },
  mounted() {
    if (getRookie("minePopup") === 0) {
      this.show = true;
    }

    console.log("MinePopupMinePopup");
  },
  methods: {
    toCollaborationSettings(){
      turnToLawyerAuthResultPageToLogin(() => {
        toCollaborationSettings();
        this.handleCancel();
      });
    },
    handleCancel() {
      this.show = false;
    },
  },
};
</script>
