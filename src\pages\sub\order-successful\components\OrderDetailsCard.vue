<template>
  <div
    class="h-[104px] bg-[#FFFFFF] rounded-[8px] flex items-center p-[12px] box-border"
    @click="handleClick"
  >
    <img
      :src="data.coverPic"
      alt=""
      class="w-[80px] h-[80px] rounded-[8px] shrink-0 mr-[12px]"
    >
    <div>
      <div class="text-[15px] text-[#333333]">
        {{ data.title }}
      </div>
      <div class="flex items-center mt-[13px]">
        <img
          alt=""
          class="block w-[16px] h-[16px] mr-[4px]"
          src="@/pages/sub/order-successful/img/wbwwsc.png"
        >
        <div class="text-[12px] text-[#666666]">
          沾沾喜气 ({{ data.totalPv }}人)
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { toOrderSuccessfulDetails } from "@/libs/turnPages";

export default {
  name: "OrderDetailsCard",
  props: {
    data: {
      type: Object,
      default: () => ({}),
      required: true
    }
  },
  methods: {
    handleClick(){
      toOrderSuccessfulDetails(this.data);
    }
  }
};
</script>
