<template>
  <login-layout>
    <div class="page-wr">
      <div class="content">
        <p class="title">
          {{ articleInfo.title }}
        </p>
        <div class="info flex flex-space-between flex-align-center">
          <p class="date">
            {{ articleInfo.publishTimeDesc }}
          </p>
          <p class="num flex-1">
            {{ articleInfo.pv }}浏览
          </p>
          <p class="type">
            #{{ articleInfo.typeLabel }}
          </p>
        </div>
        <div
          class="author-wr"
          @click="toLawyerHome"
        >
          <div
            class="author flex position-relative flex-space-between flex-align-center"
          >
            <div class="position-relative mg-r-12">
              <img
                :src="articleInfo.lawyerAvatar"
                alt=""
                class="avatar"
              >
              <img
                v-if="lawyerInfo.isVip"
                alt=""
                class="avatar-vip"
                src="img/<EMAIL>"
              >
            </div>
            <div class="flex-1">
              <p class="name">
                {{ articleInfo.lawyerName }}
                <span class="city">{{ lawyerInfo.workCityName }}</span>
              </p>
              <p class="firm">
                <span class="text-tag">擅长{{ articleInfo.typeLabel }}</span>
                <span class="text-tag">官方认证</span>
              </p>
            </div>
          </div>
          <div class="author-2 flex flex-align-center">
            <span class="text-tag2"> 律师答疑 </span>
            如你有法律问题，可直接咨询律师
          </div>
        </div>
        <div class="content-start">
          <img
            alt=""
            class="image"
            mode="widthFix"
            src="img/<EMAIL>"
          >
        </div>
        <div
          :class="{ showAll }"
          class="content-container"
        >
          <div
            v-show="!showAll"
            class="float-mask"
          >
            <img
              alt=""
              class="f-img"
              src="img/<EMAIL>"
            >
            <div
              class="text flex flex-space-center flex-align-center"
              @click="showAll = true"
            >
              点击展开完整知识
              <img
                alt=""
                class="icon"
                src="img/Frame2533@2x(1).png"
              >
            </div>
          </div>

          <div class="text-content">
            <u-parse :content="articleInfo.content" />
          </div>
          <div class="content-law">
            <img
              alt=""
              class="image"
              mode="widthFix"
              src="img/<EMAIL>"
            >
          </div>
          <div class="content-law-line">
            {{ articleInfo.quote }}
          </div>
          <div class="content-other-info">
            本文版权归原作者所有，内容仅代表作者本人观点，不代表法临平台的立场。
          </div>
          <div class="content-end">
            <img
              alt=""
              class="image"
              src="<EMAIL>"
            >
          </div>
        </div>
      </div>
    </div>
  </login-layout>
</template>

<script>
import { articleV2DealPageView, articleV2Detail, oneLawyer, } from "@/api/lawyer.js";
import { isArrNull } from "@/libs/basics-tools.js";
import { toLawyerHome } from "@/libs/turnPages.js";
import { setNavigationBarTitle } from "@/libs/tools.js";
import { amountFilterOne } from "@/libs/filter";
import LoginLayout from "@/components/login/LoginLayout.vue";

export default {
  name: "EssayDetail",
  components: {
    LoginLayout,
  },
  data() {
    return {
      servicePhonePrice: 0,
      articleInfo: {
        pic: "",
      },
      showAll: false,
      lawyerInfo: {},
      serviceData: {}, // 电话咨询数据
    };
  },
  onLoad(query) {
    setNavigationBarTitle({
      title: "普法文章",
      backgroundColor: "#EEF5FF",
    });

    articleV2Detail({ id: query.id }).then(({ data }) => {
      this.articleInfo = data;
      this.getData();
    });
    articleV2DealPageView({ articleV2Id: query.id });
  },
  methods: {
    amountFilterOne,
    isArrNull,
    /** 跳转到律师主页 */
    toLawyerHome() {
      toLawyerHome({
        id: this.articleInfo.lawyerId,
      });
    },
    getData() {
      oneLawyer({ id: this.articleInfo.lawyerId }).then(({ data }) => {
        this.lawyerInfo = data;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.page-wr {
  background: #f5f5f7;
}

.header {
  background: #fff;

  .header-img-1 {
    width: 250px;
    height: 23px;
    margin-left: 16px;
  }
}

.content-container {
  max-height: 420px;
  overflow: hidden;
  position: relative;

  &.showAll {
    max-height: none;
  }

  .float-mask {
    position: absolute;
    width: 100%;
    z-index: 1;
    left: 0;
    bottom: 0;
    font-size: 0;

    .f-img {
      width: 100%;
      height: 40px;
    }

    .text {
      font-size: 14px;
      font-weight: 400;
      color: #3887f5;
      background: #fff;
      padding-bottom: 8px;
      padding-top: 6px;
      .icon {
        width: 16px;
        height: 16px;
        margin-left: 5px;
      }
    }
  }
}

.content {
  min-height: 100vh;
  background: #ffffff;
  padding-top: 16px;
  padding-bottom: 24px;
  border-radius: 0px 0px 16px 16px;

  .title {
    font-size: 18px;
    padding: 0 16px 0;
    font-weight: 500;
    background: white;
    color: #333333;
  }

  .author-wr {
    padding: 14px 16px 13px 12px;
    background: #f5f5f7;
    border-radius: 8px;
    width: 343px;
    margin: 0 auto;
    box-sizing: border-box;
  }

  .author {
    box-sizing: border-box;
    border-bottom: 1px solid #e9e9e9;
    padding-bottom: 10px;

    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 5px;
      display: block;

      &-vip {
        position: absolute;
        display: block;
        width: 12px;
        height: 12px;
        bottom: 0;
        right: 0;
      }
    }

    .name {
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      line-height: 22px;
      margin-bottom: 3px;

      .city {
        font-size: 12px;
        font-weight: 400;
        color: #999999;
        margin-left: 8px;
      }
    }

    .firm {
      .text-tag {
        line-height: 16px;
        height: 16px;
        font-size: 11px;
        font-weight: 400;
        color: #bd934c;
        border: 0.5px solid #bd934c;
        border-radius: 4px 4px 4px 4px;
        background: #fff8ec;
        padding: 0 4px;

        & + .text-tag {
          margin-left: 4px;
        }
      }
    }

    .btn {
      width: 86px;
      text-align: center;
      line-height: 30px;
      background: #3887f5;
      border-radius: 50px 50px 50px 50px;
      font-size: 13px;
      font-weight: 500;
      color: #ffffff;

      .dot {
        display: inline-block;
        width: 6px;
        height: 6px;
        background: #43d99b;
        border-radius: 50%;
        margin-right: 6px;
      }
    }
  }

  .author-2 {
    margin-top: 10px;
    font-size: 12px;
    font-weight: 400;
    color: #999999;

    .text-tag2 {
      display: inline-block;
      width: 56px;
      height: 19px;
      background: #8791a3;
      border-radius: 4px 4px 4px 4px;
      text-align: center;
      line-height: 19px;
      color: #ffffff;
      margin-right: 8px;
    }
  }

  .text-content {
    white-space: pre-wrap;
    word-break: break-all;
    padding: 0 16px 16px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
  }

  .info {
    padding: 12px 16px 20px;

    .type {
      font-size: 12px;
      font-weight: 400;
      color: #3887f5;
    }

    .num {
      font-size: 12px;
      font-weight: 400;
      color: #999999;
      padding-left: 8px;
    }

    .date {
      font-size: 12px;
      font-weight: 400;
      color: #999999;
    }
  }

  .content-law-line {
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    padding: 0 16px;

    & + .content-law-line {
      margin-top: 8px;
    }
  }

  .content-other-info {
    width: 343px;
    margin: 16px auto;
    box-sizing: border-box;
    padding: 8px;
    background: #f5f5f7;
    border-radius: 4px 4px 4px 4px;
    font-size: 12px;
    font-weight: 400;
    color: #999999;
  }

  .color-text {
    color: #3887f5;
  }

  .text-line-other {
    margin: 16px 0;
    font-size: 13px;
    font-weight: 400;
    color: #999999;

    .text {
      margin: 0 5px;
    }

    .img {
      width: 16px;
      height: 16px;
    }
  }

  //.content-ask {
  //  text-align: center;
  //  position: relative;
  //
  //  ._title {
  //    position: absolute;
  //    font-size: 18px;
  //    font-weight: 500;
  //    color: #333333;
  //    top: 16px;
  //    left: 28px;
  //  }
  //
  //  .image {
  //    width: 351px;
  //    height: 163px;
  //  }
  //}
  .content-ask-box {
    .content-ask {
      padding: 70px 12px 0 12px;
      width: 351px;
      height: 175px;
      z-index: 1;
      box-sizing: border-box;
      .info {
        padding: 0;
        &-left {
          color: #eb4738;
          font-size: 12px;
          padding-left: 4px;
          .price {
            font-size: 18px;
            font-weight: bold;
            margin: 0 4px;
            line-height: 16px;
          }
          .unit,
          .origin {
            color: #999999;
            font-size: 12px;
          }
          .origin {
            text-decoration: line-through;
            margin-left: 3px;
          }
        }
        &-right {
          color: #eb4738;
          font-size: 11px;
        }
        //z-index: 1;
      }
      .detail {
        width: 327px;
        height: 56px;
        background: #ffffff;
        border-radius: 8px 8px 8px 8px;
        margin-top: 14px;
        padding: 0 12px;
        box-sizing: border-box;
        &-left {
          text-align: left;
          .name {
            color: #333333;
            font-size: 15px;
            font-weight: bold;
          }
          .desc {
            font-size: 11px;
            color: #999999;
            margin-top: 4px;
          }
        }
        &-right {
          width: 84px;
          height: 30px;
        }
      }
      .image {
        //z-index: 0;
      }
    }
  }

  .content-start,
  .content-end,
  .content-law {
    padding: 16px;

    .image {
      width: 100%;
      height: 28px;
    }
  }

  .content-end {
    padding: 0 16px;
    //padding-bottom: calc(90px + constant(safe-area-inset-bottom, 0));
    //padding-bottom: calc(90px + env(safe-area-inset-bottom, 0));
    .image {
      height: 10px;
    }
  }
}

.recommend {
  padding-bottom: 150px;
  margin-top: 10px;
  .title {
    font-size: 18px;
    font-weight: 600;
    color: #333333;
    padding: 16px;
  }

  .articles-item {
    padding: 0 12px;

    & + .articles-item {
      margin-top: 12px;
    }
  }
}

.btns {
  background: white;
  padding-top: 12px;
  padding-bottom: calc(12px + constant(safe-area-inset-bottom, 0));
  padding-bottom: calc(12px + env(safe-area-inset-bottom, 0));
  padding: 12px 16px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  border-radius: 16px 16px 0 0;
  box-sizing: border-box;
  z-index: 99;
  box-shadow: 0px -3px 10px 0px rgba(0, 0, 0, 0.08);

  .author {
    box-sizing: border-box;
    padding-bottom: 12px;

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 16px;
      margin-right: 8px;
    }

    .name {
      font-size: 15px;
      font-weight: 600;
      color: #333333;
    }

    .firm {
      font-size: 12px;
      font-weight: 400;
      color: #999999;
      margin-top: 4px;

      .color-text {
        color: #333;
        padding: 0 2px;
      }
    }

    .btn {
      width: 128px;
      text-align: center;
      line-height: 40px;
      background: #3887f5;
      border-radius: 50px 50px 50px 50px;
      font-size: 14px;
      font-weight: 500;
      color: #ffffff;

      .dot {
        display: inline-block;
        width: 6px;
        height: 6px;
        background: #43d99b;
        border-radius: 50%;
        margin-right: 6px;
      }
    }
  }
}

.popup-wr,
.popup-wr-btm {
  position: relative;

  .img-main {
    width: 100%;
  }

  .img-btn {
    position: absolute;
    width: 287px;
    height: 54px;
    top: 154px;
    left: 20px;
  }
}

.popup-wr-btm {
  font-size: 0;

  .img-main {
    height: 262px;
  }

  .img-btn {
    width: 311px;
    top: 164px;
    left: 32px;
  }
}
</style>
