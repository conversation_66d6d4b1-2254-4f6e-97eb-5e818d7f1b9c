<template>
  <div
    :class="{ 'card-box--selected': selected }"
    class="card-box flex flex-align-center"
    @click="handleClick"
  >
    <div class="card-box--content flex flex-column flex-space-between">
      <div
        :class="cardBoxItemInfoClass"
        class="service-title van-ellipsis"
      >
        {{ serviceInfo.serviceName }}
      </div>
      <div
        :class="cardBoxItemInfoClass"
        class="service-price van-ellipsis"
      >
        ¥{{ serviceInfo.servicePrice | amountFilter }}/{{
          serviceInfo.serviceNum
        }}{{ serviceInfo.unitLabel }}
      </div>
    </div>
    <img
      :src="serviceInfo.icon"
      alt=""
      class="icon"
    >
  </div>
</template>

<script>
export default {
  name: "PayLawyerGuideAdvisoryCard",
  props: {
    /** 是否被选中 */
    selected: {
      type: Boolean,
      default: false,
    },
    /** 服务信息 */
    serviceInfo: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    /** item数量 */
    itemCount: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    /** 根据item数量展示不同的样式 */
    cardBoxItemInfoClass() {
      // if (this.itemCount === 2) return "service-width";

      return "service-width";
    },
  },
  mounted() {
    // document
    //   .getElementsByClassName('card-box--selected')[0]
    //   .scrollIntoView({ block: 'end', inline: 'nearest' })
  },
  methods: {
    handleClick() {
      this.$emit("click", this.serviceInfo);
    },
  },
};
</script>

<style lang="scss" scoped>
.card-box--selected-icon {
  display: none;
}

.card-box {
  box-sizing: border-box;
  background: #ffffff;
  border: 1px solid #eeeeee;
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
  padding: 12px 16px;
  margin-left: 16px;

  &--selected {
    border: 2px solid $theme-color;
  }

  &--content {

    view {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .icon {
    margin-left: 12px;
    width: 32px;
    height: 32px;
    border-radius: 8px;
  }
}

//.service-width {
//  width: 80px;
//}

.service-title {
  font-size: 15px;
  font-weight: bold;
  color: #333333;
}

.service-price {
  margin-top: 2px;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
}
</style>
