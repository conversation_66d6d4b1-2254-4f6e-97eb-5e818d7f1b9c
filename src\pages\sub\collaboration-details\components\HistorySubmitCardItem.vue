<template>
  <div class="w-[351px] bg-[#FFFFFF] rounded-[12px] px-[12px] pt-[12px] pb-[8px] box-border mx-auto">
    <div class="flex items-center">
      <div class="font-bold text-[16px] text-[#333333]">
        {{ collaborationStatus }}
      </div>
      <div class="px-[6px] py-[2px] bg-[#EBF3FE] rounded-[4px] ml-[8px]">
        <div class="text-[12px] text-[#3887F5]">
          {{ data.bizTypeLabel }}
        </div>
      </div>
    </div>
    <div class="flex mt-[12px]">
      <div class="text-[14px] text-[#666666] shrink-0">
        详细需求
      </div>
      <div class="text-[15px] text-[#333333] ml-[24px]">
        {{ data.info }}
      </div>
    </div>
    <div class="flex mt-[8px]">
      <div class="text-[14px] text-[#6B6B6F] shrink-0">
        预算金额
      </div>
      <div class="font-bold text-[17px] text-[#F78C3E] ml-[24px]">
        ¥{{ amountFilterTwo(data.amount) }}
      </div>
    </div>
    <div class="flex mt-[8px]">
      <div class="text-[14px] text-[#6B6B6F] shrink-0">
        其他特殊要求
      </div>
      <div class="pl-[24px]">
        <collaboration-item-labels :labels="data.labels" />
      </div>
    </div>
    <div class="pt-[8px] border-0 border-solid border-t border-[#EEEEEE] mt-[12px] flex justify-end">
      <div
        class="w-[88px] h-[28px] rounded-[18px] border-[1px] border-solid border-[#3887F5] flex items-center justify-center box-border"
        @click="handleClick"
      >
        <div class="text-[14px] text-[#3887F5]">
          一键填入
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { COOPERATION_TYPE_OBJ } from "@/enum";
import { amountFilterTwo } from "@/libs/filter";
import CollaborationItemLabels from "@/pages/index/components/CollaborationItemLabels.vue";

export default {
  name: "HistorySubmitCardItem",
  components: { CollaborationItemLabels },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    /* 协作状态 文案*/
    collaborationStatus() {
      return ({
        [COOPERATION_TYPE_OBJ.CASE_COLLABORATION.value]: COOPERATION_TYPE_OBJ.CASE_COLLABORATION.label,
        [COOPERATION_TYPE_OBJ.CHECK_FILES_IN_DIFFERENT_PLACES.value]: COOPERATION_TYPE_OBJ.CHECK_FILES_IN_DIFFERENT_PLACES.label,
      }[this.data.type]) || "-";
    },
  },
  methods: {
    amountFilterTwo,
    handleClick() {
      this.$emit("click", this.data);
    },
  },
};
</script>