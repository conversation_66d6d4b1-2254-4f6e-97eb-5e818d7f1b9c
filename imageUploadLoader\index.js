const { verifyFileUpload, uploadFile, getRealPath, isNotUploadImage, setConfig, getConfig } = require("./core");
const babelImagePlugins = require("./babelImagePlugins");
const fs = require("fs");
const request = require("request");
const config = getConfig();

// 设置默认的项目参数
setConfig({
  modPath: "uniapp",
  requestFn
});

function requestFn(filePath) {
  return new Promise((resolve, reject) => {
    const url = "https://testapi.imlaw.cn/cms/upload/file";
    const readerStream = fs.createReadStream(filePath);

    const formData = {
      // 储存在对应的文件夹下
      modPath: config.modPath,
      // 文件
      file: readerStream
    };

    request.post({ url, formData }, function(error, response) {
      if (error) {
        return reject(error);
      }

      const responseData = JSON.parse(response.body);

      const imgUrl = responseData.data;

      if (responseData.code !== 0) {
        return reject(responseData.message);
      }

      resolve(imgUrl);
    });
  });
}

function templateImage(source, resourcePath){
  const dirPath = "src/" + resourcePath.split("/").slice(0, -1).join("/");
  const reg = /src="(.+?)"/g;
  const result = source.match(reg);
  if (result) {
    for (const item of result) {
      const str = item.split("src=")[1];
      const filePath = str.replace(/"/g, "");

      // 如果是链接开头则不处理
      if (!isNotUploadImage(filePath)) {
        continue;
      }

      const allPath = getRealPath(filePath, dirPath);

      const [imageUrl, fileHash] = verifyFileUpload(allPath);

      if (!imageUrl) {
        uploadFile(allPath, fileHash);
      } else {
        source = source.replace(item, `src="${imageUrl}"`);
      }
    }
  }

  return source;
}

/** 处理undefined问题 */
function handleUndefined (str) {
  /**
   * 匹配 {{ }} 并且括号里面不要包含|，因为有些变量是带有过滤器的
   * ! 这里正则有优化空间
   * 第一代正则 {{([\s\S]*?)}} 会把过滤器的内容也匹配到 会导致过滤器失效
   * 第二代正则 {{(?![\s\S]*\|)([\s\S]*?)}} 不会匹配过滤器 但是会少匹配一些内容因为它会从第一个 {{ 开始匹配 直到 | 为止
   * 第三代正则 {{(?![\s\S]{1,50}?\|)([\s\S]{1,100}?)}} 限制了匹配的长度 副作用为 | 前的长度不固定，所以百度多匹配会报错
   * ! 最后回归初心，在后面做判断处理
   */
  const reg = /{{([\s\S]*?)}}/g;

  // 匹配 str 中的 reg 对应的内容
  const match = str?.match(reg);

  if (match) {
    // 提取不包含 | 的内容
    const matchFilter = match?.filter((item) => !item.includes("|"));

    // 将str中matchFilter的内容替换为undefined
    matchFilter.forEach((item) => {
      // 处理空值问题 增加 0 值判断
      const endStr = item.replace(
        reg,
        "{{($1) !== null && ($1) !== undefined ? ($1) : \"\"}}"
      );
      str = str.replace(item, endStr);
    });
  }

  return str;
}

function templateImagePlugin(config = {}) {
  setConfig(config);

  return (options) => {
    const compile = options.compiler.compile;
    options.compiler.compile = (template, ...args) => {
      if (args[0].resourcePath?.match(/^(pages|components)/)) {
        template = templateImage(template, args[0].resourcePath);
      }

      template = handleUndefined(template);

      return compile(template, ...args);
    };

    return options;
  };
}

module.exports = { templateImagePlugin, babelImagePlugins };
