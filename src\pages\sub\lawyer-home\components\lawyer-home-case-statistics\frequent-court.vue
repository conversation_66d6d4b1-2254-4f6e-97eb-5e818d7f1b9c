<template>
  <div>
    <div
      v-for="item in data"
      :key="item.key"
      class="action-item"
    >
      <div>
        <div class="mg-b-6 flex flex-align-center flex-space-between">
          <div class="flex flex-align-center">
            <img
              alt=""
              class="icon"
              src="../../img/right-home-icon.png"
            >
            <p class="title">
              {{ item.label }}
            </p>
            <p class="percentage">
              {{ item.rate }}
            </p>
          </div>
          <p class="nums">
            {{ item.count }}件
          </p>
        </div>
        <u-line-progress
          :percentage="percentage(item.rate)"
          :showText="false"
          activeColor="#A0CFFB"
          height="4"
          inactiveColor="#F5F5F7"
        />
      </div>
    </div>
  </div>
</template>

<script>
import ULineProgress from "@/uview-ui/components/u-line-progress/u-line-progress.vue";

export default {
  name: "FrequentCourt",
  components: { ULineProgress },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    percentage(rate) {
      return Number((rate || "").replace("%", ""));
    },
  },
};
</script>

<style lang="scss" scoped>
.action-item {
  margin-bottom: 20px;
}

.icon {
  width: 14px;
  height: 14px;
}

.title {
  margin-left: 2px;
  font-size: 12px;
  font-weight: 400;
  color: #333333;
}

.percentage {
  margin-left: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
}

.nums {
  font-size: 11px;
  font-weight: 400;
  color: #3887F5;
}
</style>
