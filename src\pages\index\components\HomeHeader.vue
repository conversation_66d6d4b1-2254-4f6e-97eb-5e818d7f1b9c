<template>
  <div class="relative">
    <img
      class="absolute left-0 top-0 bottom-0 right-0 h-full w-full"
      src="@/pages/index/imgs/<EMAIL>"
      alt=""
    >
    <app-header :back="false">
      <div class="h-full pl-[19px] font-bold flex items-center text-[15px] text-[#333333]">
        <img
          class="w-[25px] h-[25px] pr-[8px]"
          src="@/pages/index/imgs/<EMAIL>"
          alt=""
        >
        法临律师助手
      </div>
    </app-header>
  </div>
</template>

<script>
import AppHeader from "@/components/AppHeader/index.vue";

export default {
  name: "HomeHeader",
  components: { AppHeader }
};
</script>

<style scoped lang="scss">

</style>