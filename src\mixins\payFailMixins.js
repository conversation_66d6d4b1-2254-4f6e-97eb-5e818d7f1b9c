import Store from "@/store/index.js";
import { getCurrentPageRoute } from "@/libs/turnPages.js";

export default {
  mounted() {
    // 如果有失败回调，添加失败回调
    if (!this.payFailCallback) return;

    // 生成随机字符串
    const randomStr = Math.random()
      .toString(36)
      .substr(2);

    const currentPageRoute = getCurrentPageRoute().fullPath;

    // 添加失败回调
    Store.commit("payState/ADD_FAIL_CALLBACK", {
      name: randomStr,
      callback: () => {
        // 这里的判断主要是因为支付失败后会触发没有销毁页面的支付失败回调
        if(getCurrentPageRoute().fullPath !== currentPageRoute) return;

        this.payFailCallback?.();
      }
    });

    this.$on("hook:beforeDestroy", () => {
      // 移除失败回调
      Store.commit("payState/REMOVE_FAIL_CALLBACK", randomStr);
    });
  }
};
