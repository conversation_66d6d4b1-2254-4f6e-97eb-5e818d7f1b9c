<template>
  <div>
    <document-detail-of-me-info :lawCaseRes="lawCaseRes" />
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { lawyerLawCaseDetail } from "@/api/lawyer.js";
import DocumentDetailOfMeInfo from "@/pages/sub/lawyer-home/document-detail-of-me/DocumentDetailOfMeInfo.vue";

export default {
  name: "DocumentDetailOfMe",
  components: { DocumentDetailOfMeInfo, USafeBottom },
  data() {
    return {
      caseId: null,
      lawyerId: null,
      info: {
        lawCaseRes: {},
      },
    };
  },
  onLoad({ id, lawyerId }) {
    console.log(id, "0e6704d435a40f2ba7254e2a5386d88a");
    this.caseId = id;
    this.lawyerId = lawyerId;
  },
  onShow() {
    this.getInfo();
  },
  methods: {
    getInfo() {
      lawyerLawCaseDetail({ id: this.caseId, lawyerId: this.lawyerId }).then(
        ({ data }) => {
          this.info = data;
        }
      );
    },
  },
  computed: {
    lawCaseRes() {
      return this.info.lawCaseRes || {};
    },
  },
};
</script>

<style lang="scss" scoped></style>
