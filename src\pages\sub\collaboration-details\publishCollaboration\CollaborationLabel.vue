<template>
  <div
    :class="[
      border?'border-0 border-solid border-b border-[#EEEEEE]':''
    ]"
  >
    <div
      class="flex h-[44px] items-center justify-between CollaborationLabelItem"
    >
      <div class="text-[14px] flex">
        <p
          v-if="required"
          class="text-[#EB4738] mr-[4px]"
        >
          *
        </p>
        <p class="text-[#666666]">
          {{ label }}
        </p>
      </div>
      <slot v-if="position === 'left'" />
    </div>
    <div
      v-if="position !== 'left'"
      :class="[{
        'pb-[12px]':border
      }]"
    >
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: "CollaborationLabel",
  props: {
    label: {
      type: String,
      default: "",
      required: true
    },
    border: {
      type: Boolean,
      default: true
    },
    required: {
      type: Boolean,
      default: true
    },
    position: {
      type: String,
      default: "left"
    }
  }
};
</script>
