<template>
  <div>
    <div
      class="bottom"
      :class="[
        {
          'bottom--shadow': shadow,
        },
      ]"
    >
      <slot />
      <u-safe-bottom />
    </div>
    <div
      :style="{
        height: placeholderHeight * 2 + 'rpx',
      }"
    />
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { getClientRect } from "@/libs/tools";
import { isNull } from "@/libs/basics-tools";

export default {
  name: "AppBottom",
  components: { USafeBottom },
  props: {
    /** 是否有阴影样式 */
    shadow: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      /** 占位的高度 */
      placeholderHeight: 0,
    };
  },
  mounted() {
    this.getComponentHeight();
  },
  methods: {
    /** 获取组件的高度 */
    getComponentHeight() {
      getClientRect.call(this, ".bottom").then((res) => {
        if(!isNull(res)){
          this.placeholderHeight = res.height;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.bottom {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;

  &--shadow {
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -3px 10px 0 rgba(0, 0, 0, 0.08);
    background-color: #fff;
  }
}
</style>
