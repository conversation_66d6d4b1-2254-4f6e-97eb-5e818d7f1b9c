const path = require("path");
const pxtorpx = require("postcss-pxtorpx");
const UnoCSS = require("unocss-webpack-uniapp2").default;
const { templateImagePlugin } = require("./imageUploadLoader");

const resolve = (dir) => {
  return path.join(__dirname, dir);
};

module.exports = {
  configureWebpack: {
    name: "11",
    resolve: {
      alias: {
        "@": resolve("src/"),
      },
    },
    optimization: {
      minimize: true,
    },
    plugins: [UnoCSS()],
  },
  chainWebpack: (config) => {
    // 生产环境去除console debugger 这里用环境变量判断
    // if (process.env.VUE_APP_ENV_TEST !== "true") {
    //   config.optimization.minimizer("terser").tap((args) => {
    //     args[0].terserOptions.compress.drop_console = true;
    //     args[0].terserOptions.compress.drop_debugger = true;
    //     return args;
    //   });
    // }
    config.module
      .rule("vue-inset")
      .test(/\.vue$/)
      .use("vue-inset-loader")
      .loader("vue-inset-loader")
      .end();
    config.module.rule("vue").use("vue-loader").tap(templateImagePlugin()).end();
  },
  css: {
    loaderOptions: {
      postcss: {
        plugins: [
          pxtorpx({
            // 转换倍数
            multiplier: 2,
            // 转换属性 * 全部
            propList: ["*"],
            // 黑名单： 不需要转换的css选择器
            selectorBlackList: [],
          }),
        ],
      },
    },
  },
  transpileDependencies: ["@dcloudio/uni-ui", "uview-ui"],
};
