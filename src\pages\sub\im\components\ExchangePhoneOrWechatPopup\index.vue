<template>
  <app-popup
    round="32rpx"
    :show="getShow"
    :safeAreaInsetBottom="false"
    mode="center"
  >
    <div class="w-[311px] rounded-[16px] overflow-hidden">
      <div class="h-[176px] relative">
        <img
          v-if="isExchangePhone"
          class="w-full h-[176px]"
          src="@/pages/sub/im/imgs/<EMAIL>"
          alt=""
        >
        <img
          v-else
          class="w-full h-[176px]"
          src="@/pages/sub/im/imgs/Frame168@3x_1.png"
          alt=""
        >
        <p class="absolute absolute-x-center top-[120px] font-[500] text-[18px] text-[#666666] tracking-[1px]">
          {{ showText }}
        </p>
      </div>
      <div class="px-[24px] pb-[16px]">
        <p class="font-[500] text-[16px] text-[#333333]">
          确定与对方交换{{ exchangeText }}吗？
        </p>
        <p class="text-[14px] pt-[10px] pb-[24px] text-[#666666]">
          对方确认交换后，双方可以查看彼此的{{ exchangeText }}，您可前往 <span
            class="text-[#3887F5]"
            @click.stop="toManageYourAccount"
          >管理账号</span>
        </p>
        <div class="flex items-center justify-between">
          <div
            class="w-[126px] box-border text-[14px] text-[#333333] center h-[36px] rounded-[68px] border-[1px] border-solid border-[#CCCCCC]"
            @click="handleCancel"
          >
            取消
          </div>
          <div
            class="w-[126px] h-[36px] center bg-[#3887F5] rounded-[68px] font-[500] text-[14px] text-[#FFFFFF]"
            @click.stop="handleConfirm"
          >
            确认
          </div>
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { conversationIdSymbolProps } from "@/pages/sub/im/mixins/case-info-state";
import { lawyerExchangeInfo } from "@/api/im";
import { toCollaborationSettings } from "@/libs/turnPages";
import { scrollToTarget, stringMask } from "@/libs/tools";
import { mapGetters } from "vuex";

export default {
  name: "ExchangePhoneOrWechatPopup",
  components: { AppPopup },
  mixins: [conversationIdSymbolProps],
  props: {
    value: {
      type: Boolean,
      default: false
    },
    // 弹窗配置信息
    data: {
      type: Object,
      default: () => {
        return {};
      }
    },
    //   律师信息
    lawyerInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  computed: {
    ...mapGetters({
      userInfo: "user/getUserInfo",
    }),
    phoneNumber() {
      return this.userInfo.phone;
    },
    wechatId() {
      return this.userInfo.wechatId;
    },
    otherPhone() {
      return this.userInfo.otherPhone;
    },
    showText(){
      const text = this.isExchangePhone ? (this.otherPhone || this.phoneNumber) : this.wechatId;
      return stringMask(text, 3, 2);
    },
    getShow: {
      get(){
        return this.value;
      },
      set(val){
        this.$emit("input", val);
      }
    },
    exchangeText(){
      return this.data.text || "";
    },
    exchangeType(){
      return this.data.type || "";
    },
    /* 是不是手机号弹窗 */
    isExchangePhone(){
      return this.exchangeType === "phone";
    }
  },
  methods: {
    handleCancel(){
      this.getShow = false;
    },
    handleConfirm(){
      /* type	是	int	0 电话 1 微信
imSessionId	是	int	会话ID
lawyerId	是	int	对方律师id */
      lawyerExchangeInfo({
        type: this.isExchangePhone ? 0 : 1,
        imSessionId: this.conversationId,
        lawyerId: this.lawyerInfo.id
      }).then(() => {
        this.handleCancel();
        this.$nextTick(() => {
          scrollToTarget();
        });
      });
    },
    toManageYourAccount(){
      toCollaborationSettings();
    }
  }
};
</script>

<style scoped lang="scss">

</style>