<template>
  <div class="tab-container">
    <div
      :style="[tabsStyle]"
      class="search-bar flex"
    >
      <div
        :class="{ active: show3 }"
        class="search-item"
        @click="handleClick('show3')"
      >
        <p class="text-ellipsis max-w-[80px]">
          {{ cityInfo.name || "全国" }}
        </p>
        <span
          :class="{ active: show3 }"
          class="icon2 iconfont icon-xiala"
        />
      </div>
      <div
        :class="{ active: show4 }"
        class="search-item"
        @click="handleClick('show4')"
      >
        <p class="text-ellipsis max-w-[80px]">
          {{ groupTypesInfo.label }}
        </p>
        <span
          :class="{ active: show4 }"
          class="icon2 iconfont icon-xiala"
        />
      </div>
      <div
        :class="{ active: show2 }"
        class="search-item"
        @click="handleClick('show2')"
      >
        <p class="text-ellipsis max-w-[80px]">
          {{ sortInfo.label }}
        </p>
        <span
          :class="{ active: show2 }"
          class="icon2 iconfont icon-xiala"
        />
      </div>
      <div
        :class="{ active: show5 }"
        class="search-item"
        @click="handleClick('show5')"
      >
        <p class="text-ellipsis max-w-[80px]">
          {{ groupSourceInfo.label }}
        </p>
        <span
          :class="{ active: show5 }"
          class="icon2 iconfont icon-xiala"
        />
      </div>
    </div>
    <!--   案件协作 -->
    <popup-select
      :activeStyle="activeStyle"
      :contentWrapperStyle="contentWStyle"
      :list="sortList"
      :show.sync="show2"
      :value="sortInfo.value"
      @handleSelect="changeSortInfoInfo('sortInfo',$event)"
    />
    <!-- 群类型  -->
    <popup-select
      :activeStyle="activeStyle"
      :contentWrapperStyle="contentWStyle"
      :list="groupTypesList"
      :show.sync="show4"
      :value="groupTypesInfo.value"
      @handleSelect="changeSortInfoInfo('groupTypesInfo',$event)"
    />

    <!-- 群来源  -->
    <popup-select
      :activeStyle="activeStyle"
      :contentWrapperStyle="contentWStyle"
      :list="groupSourceList"
      :show.sync="show5"
      :value="groupSourceInfo.value"
      @handleSelect="changeSortInfoInfo('groupSourceInfo',$event)"
    />
    <popup-location
      :contentWrapperStyle="contentWStyle"
      :show.sync="show3"
      isLocation
      nationwide
      @setCity="setCity"
    />
  </div>
</template>

<script>
import PopupLocation from "@/pages/index/components/popup-location/index.vue";
import PopupSelect from "@/pages/index/components/popupSelect.vue";
import { pxToRpx } from "@/libs/tools";
import { dataDetailList } from "@/api";

export default {
  name: "GroupSearchTabBar",
  components: {  PopupSelect, PopupLocation },
  props: {
    localStatus: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      show2: false,
      show3: false,
      show4: false,
      show5: false,
      /* 群类型 */
      groupTypesList: [],
      /* 群来源( */
      groupSourceList: [ {
        label: "官方群",
        value: "1",
      },
      {
        label: "个人群",
        value: "2",
      }],
      /* 排序 选中信息*/
      sortInfo: {
        label: "排序",
        value: "",
      },
      /* 群类型*/
      groupTypesInfo: {
        label: "全部类型",
        value: "",
      },
      /* 群属性*/
      groupSourceInfo: {
        label: "全部群",
        value: "",
      },
      /* 城市信息*/
      cityInfo: {
        name: "全国",
      },
      /* 排序*/
      sortList: [
        {
          label: "最新",
          value: "1",
        },
        {
          label: "人数最多",
          value: "2",
        }
      ],
      typeValue: "0",
      contentWStyle: {
        top: pxToRpx(41),
      },
      activeStyle: {
        color: "#3887F5",
      },
    };
  },
  computed: {
    /** 是否显示弹窗 */
    showPopup() {
      return this.show2 || this.show3 || this.show4 || this.show5;
    },
    /** 顶部导航的样式 */
    tabsStyle() {
      if (!this.showPopup) return {};

      return uni.$u.addStyle({
        top: 0,
        right: 0,
        left: 0,
        position: "fixed"
      });
    },
  },
  mounted() {
    /* 群类型筛选*/
    dataDetailList({
      groupCode: "COMMUNITY_GROUP_TYPES",
    }).then(({ data = [] }) => {
      this.groupTypesList = data;
    });
  },
  methods: {
    // 案源线索子tab切换
    handleClick(type) {
      if (this[type]) return (this[type] = false);
      this.show2 = false;
      this.show3 = false;
      this.show4 = false;
      this.show5 = false;
      this[type] = true;
    },
    // 城市选择
    setCity(city) {
      console.log(city);
      this.cityInfo = city;
      this.dataChange();
    },
    // 数据变化时重新请求数据
    dataChange() {
      const data = {
        sortType: this.sortInfo.value,
        /* {name: "石家庄", code: 130100, provinceName: "河北省", province: 130000*/
        cityCode: this.cityInfo.code || "",
        proviceCode: this.cityInfo.province || "",
        groupSource: this.groupSourceInfo.value,
        groupTypes: this.groupTypesInfo.value,
      };
      this.$emit("searchChange", data);
    },
    // 案件协作选择
    changeSortInfoInfo(key, item) {
      this[key] = item;
      this.dataChange();
    },
  },
};
</script>

<style lang="scss" scoped>
.tab-container {
  background: #f5f5f7;
  .search-bar {
    padding: 0 12px;
    position: relative;
    z-index: 2;
    background: #f5f5f7;
    .search-item {
      flex: 1;
      height: 41px;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      display: flex;
      align-items: center;
      justify-content: center;
      &.active {
        color: #3887f5;
      }
      .icon2 {
        transition: all 0.3s;
        margin-left: 3px;
        font-size: 12px;
        &.active {
          transform: rotate(180deg);
        }
      }
      .icon3 {
        font-size: 16px;
      }
    }
  }
}
.lawyerTypes-box {
  padding-top: 8px;
  border-radius: 0 0 16px 16px;
  background: #f5f5f7;
}
</style>
