<template>
  <div class="bg-[#FFFFFF] rounded-[12px] px-[16px] pb-[16px]">
    <div
      class="flex items-center justify-between h-[54px] border-0 border-b border-solid border-[#EEEEEE]"
    >
      <div class="flex items-center">
        <p class="font-bold text-[16px] text-[#333333] pr-[8px]">
          {{ data.typeLabel || "-" }}
        </p>
        <p
          class="py-[2px] px-[6px] bg-[#EBF3FE] rounded-[4px] text-[12px] text-[#3887F5]"
        >
          {{ data.bizTypeLabel || "-" }}
        </p>
        <div class="flex items-baseline">
          <i class="iconfont icon-liulan !text-[12px] text-[#999999] ml-[12px]" />
          <div class="text-[12px] text-[#999999] mx-[2px]">
            {{ data.pv }}
          </div>
          <div class="text-[12px] text-[#999999]">
            浏览
          </div>
        </div>
      </div>
      <div class="text-[14px]">
        <p
          :style="[{ color: data.statusLabel.color }]"
          class="text-[#22BF7E] flex items-center"
        >
          <i
            v-if="data.statusLabel.badgeColor"
            :style="[{ backgroundColor: data.statusLabel.badgeColor }]"
            class="w-[5px] h-[5px] rounded-[100%] mr-[4px]"
          />
          {{ data.statusLabel.label }}
        </p>
      </div>
    </div>
    <div
      v-for="i in list"
      :key="i.key"
      class="pt-[16px] flex"
    >
      <p class="text-[14px] flex-shrink-0 text-[#666666]">
        {{ i.label }}
      </p>
      <p
        :class="i.className ? i.className : ''"
        class="pl-[24px] flex-1 text-[15px] text-[#333333] break-all"
      >
        {{ getText(i) }}
      </p>
    </div>
    <div
      v-if="!isArrNull(receiveLawyers)"
      class="pt-[16px] flex"
    >
      <p class="text-[14px] flex-shrink-0 text-[#666666]">
        接单响应
      </p>
      <div class="pl-[24px] flex-1 flex items-center">
        <img
          v-for="i in receiveLawyers"
          :key="i.jdLawyerId"
          :src="i.jdLawyerAvatar"
          alt=""
          class="w-[20px] ml-[-6px] first-of-type:ml-[0] h-[20px] rounded-[10px] border-[1px] border-solid border-[#FFFFFF]"
        >
        <p class="text-[12px] text-[#666666] pl-[8px]">
          已有{{ receiveLawyers.length }}位律师响应
        </p>
      </div>
    </div>
    <div
      v-if="!isArrNull(data.labels)"
      class="pt-[16px] flex items-center"
    >
      <div class="text-[14px] flex-shrink-0 text-[#666666]">
        其他特殊要求
      </div>
      <div class="pl-[24px]">
        <collaboration-item-labels :labels="data.labels" />
      </div>
    </div>
  </div>
</template>

<script>
import { amountFilterTwo } from "@/libs/filter";
import { isArrNull, isNull } from "@/libs/basics-tools";
import CollaborationItemLabels from "@/pages/index/components/CollaborationItemLabels.vue";

export default {
  name: "CollaborationBasics",
  components: { CollaborationItemLabels },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      list: [
        {
          label: "详细需求",
          key: "info",
        },
        {
          label: "发布时间",
          key: "createTime",
        },
        {
          label: "协作地区",
          key: "regionName",
          render: (data) => {
            return `${data.provinceName ? data.provinceName + "-" : ""}${
              data.regionName ? data.regionName : "-"
            }`;
          },
        },
        {
          label: "预算金额",
          key: "amount",
          className: "money",
          render: (data) => {
            return `￥${
              isNull(data.amount) ? "-" : amountFilterTwo(data.amount)
            }`;
          },
        },
      ],
    };
  },
  computed: {
    /* 接单律师*/
    receiveLawyers() {
      return this.data?.jdLawyerList || [];
    },
  },
  methods: {
    isArrNull,
    getText(i) {
      return i.render
        ? i.render(this.data)
        : isNull(this.data[i.key])
          ? "-"
          : this.data[i.key];
    },
  },
};
</script>

<style lang="scss" scoped>
.money {
  font-weight: bold;
  font-size: 17px;
  color: #f78c3e;
}
</style>
