<template>
  <div>
    <slot />
    <login-one-click
      :closeOnClickOverlay="closeOnClickOverlay"
      :needTransformationPath="needTransformationPath"
      :show="show"
      :showSafe="showSafe"
    />
    <wechat-bind-popup />
  </div>
</template>

<script>
import LoginOneClick from "@/components/login/LoginOneClick.vue";
import WechatBindPopup from "@/components/WechatBindPopup.vue";

export default {
  name: "LoginLayout",
  components: { LoginOneClick, WechatBindPopup },
  props: {
    needTransformationPath: {
      type: [Boole<PERSON>, String],
      default: false,
    },
    /** 点击遮罩是否关闭弹窗 */
    closeOnClickOverlay: {
      type: Boolean,
      default: true,
    },
    /** 是否显示安全区域 */
    showSafe: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    show: {
      get() {
        return this.$store.getters["popupState/getLoginPopupState"];
      },
      set(val) {
        this.$store.commit("popupState/SET_LOGIN_POPUP_STATE", val);
      },
    },
  },
  destroyed() {
    this.show = false;
  },
};
</script>

<style scoped></style>
