<template>
  <app-popup
    :show="show"
    mode="center"
    :round="16"
    closeable
    :safeAreaInsetBottom="false"
    @cancel="handleCancel"
  >
    <div class="font-bold text-[16px] text-[#000000] w-[311px] p-[24px] box-border">
      <div class="text-center">
        发布成功！分享到社群更快响应
      </div>
      <div class="flex items-center justify-center">
        <app-button-share>
          <div class="flex flex-col items-center mt-[32px]">
            <img
              class="block w-[48px] h-[48px]"
              alt=""
              src="@/pages/sub/collaboration-details/imgs/rwjy9j.png"
            >
            <div class="text-[12px] text-[#333333] mt-[8px]">
              转发给朋友
            </div>
          </div>
        </app-button-share>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import AppButtonShare from "@/components/AppComponents/AppButtonShare/index.vue";
import { toCollaboration } from "@/libs/turnPages";

export default {
  name: "SubmitSharePopup",
  components: { AppPopup, AppButtonShare },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  methods: {
    handleCancel() {
      toCollaboration();
    },
  },
};
</script>
