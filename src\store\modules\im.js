// import { imLoadUser } from "@/api/user";
import { WS_READY_STATE } from "yoc-im-web";
import { EventBus, WebIM } from "@/plugins/im.js";
import { getUserTokenStorage, setImToken } from "@/libs/token";
import { axiosBaseHeadersConfig } from "@/libs/config.js";
import { imLoadUserCache } from "@/api/user";
import { BURY_POINT_CHANNEL_TYPE, buryPointChannelBasics, IM_STATUS } from "@/libs/burypoint";


let onOpenedCallback = () => {

};
export const state = () => {
  return {
    imUserInfo: {},
    isOpen: false,
  };
};

export const getters = {
  getImUserInfo: (state) => state.imUserInfo,
};

export const mutations = {
  SET_IM_USER_INFO(state, data) {
    state.imUserInfo = data;
  },
  SET_OPEN(state, status = false) {
    state.isOpen = status;
  },
};
export const actions = {
  async userLogin({ commit, dispatch, state }) {

    try {
      commit("SET_OPEN", false);

      if (!getUserTokenStorage()) {
        WebIM.conn.loginOut();
        return;
      }
      buryPointChannelBasics({
        code: "IM_CONNECT_MSG",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
        extra: {
          im_status: IM_STATUS.AUTHENTICATED
        }
      });

      // ! 这里报错会进入下面的 catch
      const { data } = await imLoadUserCache();
      commit("SET_IM_USER_INFO", data);
      console.log("await imLoadUser()", state.imUserInfo?.imToken);
      setImToken(state.imUserInfo?.imToken);

      /** im 打开和连接时需要的参数 */
      const webImConnParams = {
        accessToken: state.imUserInfo?.imToken,
        requestHeaders: {
          clientToken: getUserTokenStorage(),
          osVersion: axiosBaseHeadersConfig.osVersion,
        },
        wsParams: {
          clientToken: getUserTokenStorage(),
          osVersion: axiosBaseHeadersConfig.osVersion,
        },
      };

      if (WebIM.conn.getWsReadystate() === WS_READY_STATE.OPEN) {
        WebIM.conn.login(webImConnParams);
      } else {
        WebIM.conn.open(webImConnParams);
      }
    } catch (error) {
      // 再次调用userLogin函数
      setTimeout(() => {
        dispatch("userLogin");
      }, 1000);

      console.log(error);

      return error;
    }
    return Promise.resolve();
  },
  setOpen({ commit }, status) {
    commit("SET_OPEN", status);
  },
  onOpened({ state, dispatch }, callback) {
    dispatch("unloadOpened");
    return new Promise((resolve) => {
      if (state.isOpen) {
        resolve();
        callback && callback();
      }
      /* 这里记住回调 im重连后 重新刷新页面*/
      onOpenedCallback =  (message) => {
        resolve();
        callback && callback(message);
      };
      EventBus.$on("onOpened", onOpenedCallback);
    });
  },
  unloadOpened({ state }, callback) {
    console.log(111111111);
    EventBus.$off("onOpened", onOpenedCallback);
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
