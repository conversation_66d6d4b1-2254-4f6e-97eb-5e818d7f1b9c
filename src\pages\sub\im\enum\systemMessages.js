import {
  toCollaborationDetails,
  toOrderSuccessful,
  turnToLawyerAuthResultPage,
  turnToProfileInfoPage
} from "@/libs/turnPages.js";
import { lawyerCollaborationDetailById } from "@/api/collaboration";

/* 服务消息枚举 messageType后端这个字段*/
export const SERVICE_MESSAGES_ENUM = {
  "GO_LAWYER_COLLABORATION_SQUARE": {
    text: "协作详情",
    clickHook: (data) => {
      const id = data.extras.collaborationId;

      lawyerCollaborationDetailById({
        id
      }).then(() => {
        toCollaborationDetails({ id });
      });
    }
  },
  "GOOD_NEWS_LIST": {
    text: "去沾沾喜气",
    icon: require("@/pages/sub/im/imgs/kc3yll.png"),
    clickHook: () => toOrderSuccessful()
  },
};

/* 这里注释 是因为功能不全 */
/* /!* 系统消息枚举 messagePageType 这个后端字段*!/
export const SYSTEM_MESSAGES_ENUM = {
  /!* 跳转页面->问答广场*!/
  "GO_QA_CENTER": {
    // text:"去解答"
    // clickHook:turnToQASquare
  },
  /!* 跳转页面->首页*!/
  "GO_HOME_PAGE": {
    text: "去领取",
    clickHook: () => turnToHomePage()
  },
  /!* 跳转页面->去投诉详情界面*!/
  "COMPLAIN_JUMP_PAGE": {
    text: "去查看",
    clickHook: data => turnToDetailsOfTheComplaint({ id: data.extras.complainId })
  },
  /!* 跳转页面->自动抢案源*!/
  "AUTO_GRABBING_CASE_SOURCE": {
    // text:"去查看"
  },
  /!* 行为违规 跳转页面->im*!/
  "GO_CHAT_PAGE": {
    text: "查看详情",
    clickHook: data => turnToImPage({ id: data.extras.conversationId })
  },
  /!* !跳转vip 暂时没有*!/
  /!* 跳转页面->立即领取开通*!/
  "AT_ONCE_RECEIVE_OPEN": {
    // text:"立即领取开通"
  },
  /!* !跳转vip 暂时没有*!/
  /!* 跳转页面->立即续费*!/
  "AT_ONCE_RENEW": {
    // text:"立即续费"
  },
  /!* 跳转页面->案源广场*!/
  "AT_ONCE_GRAB": {
    text: "立即接案",
    clickHook: () => turnToCaseSourceSquare()
  },
  /!* 跳转页面->立即充值*!/
  "AT_ONCE_RECHARGE": {
    text: "立即充值",
    clickHook: () => turnToRechargePage()
  },
  /!* 跳转页面->查看退费详情=》会员退费*!/
  "CHECK_REFUND_DETAIL": {
    text: "查看退费详情"
  },
  /!* 跳转页面->去接洽=>案源详情*!/
  "GO_TO_CONTACT": {
    text: "开始接洽",
    clickHook: (data) => turnToCaseSourceDetails({ id: data.extras.caseSourceId, type: 3 })
  },
  /!* 跳转页面->查看退单详情=》案源包退单*!/
  "CHECK_CHARGEBACK_DETAIL": {
    text: "查看退单详情",
    clickHook: data => turnToRefundDetailPage({ id: data.extras.feedbackId })
  },
  /!* 跳转页面->付费咨询-1v1咨询*!/
  "GO_PAY_CONSULT_DETAIL": {
    // text:"查看详情",
    clickHook: data => turnToImPage({ id: data.extras.conversationId })
  },
  /!* 跳转页面->当事人点击催服务*!/
  "GO_TO_REPLY": {
    text: "去回复",
    clickHook: data => turnToImPage({ id: data.extras.conversationId })
  },
  /!* 跳转页面->活动跳转到线索包充值*!/
  "CASE_SOURCE_CLUE_OPEN": {
    text: "立即开通",
    clickHook: () => turnToRechargePage()
  },
  /!* 完善资料页*!/
  "GO_PERFECT_UPDATE": {
    text: "去修改",
    clickHook: () => turnToProfileInfoPage()
  },
  /!* 完善资料页*!/
  "GO_PERFECT_REVIEW": {
    text: "去完善",
    clickHook: () => turnToProfileInfoPage()
  },
  /!* 案源广场*!/
  "GO_CASE_SOURCE_CENTER": {
    text: "去接案",
    clickHook: () => turnToCaseSourceSquare()
  },
  /!* 去投诉详情界面*!/
  "LAWYER_COMPLAIN_PUNISH": {
    text: "去查看",
    clickHook: data => turnToDetailsOfTheComplaint({ id: data.extras.complainId })
  },
  /!* 去认证*!/
  "GO_AHTH_REVIEW": {
    text: "去认证",
    clickHook: () => turnToLawyerAuthResultPage()
  },
  "AGAIN_AHTH_REVIEW": {
    text: "重新认证",
    clickHook: () => turnToLawyerAuthResultPage()
  }
} */

/* 系统消息枚举 messagePageType 这个后端字段*/
export const SYSTEM_MESSAGES_ENUM = {
  /* 完善资料页*/
  "GO_PERFECT_UPDATE": {
    text: "去修改",
    clickHook: () => turnToProfileInfoPage()
  },
  /* 完善资料页*/
  "GO_PERFECT_REVIEW": {
    text: "去完善",
    clickHook: () => turnToProfileInfoPage()
  },
  /* 去认证*/
  "GO_AHTH_REVIEW": {
    text: "去认证",
    clickHook: () => turnToLawyerAuthResultPage()
  },
  "AGAIN_AHTH_REVIEW": {
    text: "重新认证",
    clickHook: () => turnToLawyerAuthResultPage()
  }
};


/* 互相消息枚举 */
export const CHAT_MESSAGES_ENUM = {
  /* 协作有人接单了 跳转协作详情 */
  "GO_LAWYER_COLLABORATION_DETAIL": {
    text: "查看详情",
    clickHook: data => toCollaborationDetails({ id: data.extras.collaborationId })
  }
};
