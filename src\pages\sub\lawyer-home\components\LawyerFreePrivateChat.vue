<template>
  <app-popup
    :overlay="false"
    :show="show"
    :zIndex="9990"
    showCancelButton
    @cancel="show = false"
  >
    <div
      class="flex flex-col justify-center items-center relative pb-[12px] pt-[46px]"
    >
      <img
        :src="lawyerInfo.imgUrl"
        mode="aspectFill"
        alt=""
        class="w-[54px] h-[54px] block absolute left-[160px] -top-[20px] rounded-full border-4 border-[#FFFFFF] border-solid"
      >
      <div class="font-bold text-[16px] text-[#333333]">
        {{ lawyerInfo.realName }}
      </div>
      <div class="text-[12px] text-[#999999] mt-[5px]">
        {{ lawyerInfo.lawyerOffice || "未填写" }}
      </div>
      <div class="text-[14px] text-[#666666] mt-[8px]">
        “您好，有什么疑问？可以和我聊聊”
      </div>
      <div
        class="w-[343px] h-[43px] bg-[#EBF3FE] rounded-[68px] mt-[12px] font-bold text-[15px] text-[#3887F5] flex items-center justify-center"
        @click="privateChat"
      >
        私聊律师
      </div>
    </div>
    <u-safe-bottom />
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { whetherToLogIn } from "@/libs/tools";
import { privateChatSaveAppoint } from "@/api/user";
import { toImChatPage } from "@/libs/turnPages";

export default {
  name: "LawyerFreePrivateChat",
  components: { USafeBottom, AppPopup },
  props: {
    lawyerInfo: {
      type: Object,
      default: () => ({}),
      required: true,
    },
  },
  data() {
    return {
      show: false,
    };
  },
  mounted() {
    // https://lanhuapp.com/web/#/item/project/product?pid=73951e27-45d6-414e-b896-6729b986fc54&teamId=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=e42e0915-d58d-48f4-baca-6ea986f875dc&docId=af7719f7-36c2-443e-81cb-5bac1430df52&docType=axure&pageId=f50368545da746b2a6fb9ca784505af2&image_id=af7719f7-36c2-443e-81cb-5bac1430df52&parentId=c56bc70a-c303-48f1-9b99-6cf3e2340053&tid=43efda02-ce73-4682-acd1-afc75cbf6b0c
    setTimeout(() => {
      this.show = true;
    }, 3000);
  },
  methods: {
    privateChat() {
      whetherToLogIn(() => {
        privateChatSaveAppoint({
          lawyerId: this.lawyerInfo.id,
        }).then(({ data }) => {
          toImChatPage({
            lawyerId: data.lawyerId,
            conversationId: data.imSessionId,
            caseSourceId: data.caseSourceServerV2Id,
          });
        });
      });
    },
  },
};
</script>
