<template>
  <div>
    <img
      alt=""
      class="w-full h-[146px] absolute top-0 left-0 -z-10"
      src="@/pages/ask-details/search/img/mWU9c3BL.png"
    >
    <div class="h-[106px]" />
    <entrance-ask-lawyer
      clickAskLawyerBuryPoint="LAW_APPLET_LAW_KNOWLEDGE_SECOND_PAGE_ONLINE_ASK_CLICK"
      clickPhoneConsultBuryPoint="LAW_APPLET_LAW_KNOWLEDGE_SECOND_PAGE_PHONE_CONSULT_CLICK"
    />
    <div class="mt-[12px] mb-[20px] mx-[12px]">
      <entrance-lawyer-topic
        :bgImgIndex="1"
        :dataSource="rankList"
        clickLawyerCardBuryPoint="LAW_APPLET_LAW_KNOWLEDGE_SECOND_PAGE_LAWYER_CARD_CLICK"
        title="法律文章·律师排行榜"
      />
    </div>
    <div class="px-[16px]">
      <u-sticky>
        <div class="bg-[#f5f5f7] py-[16px]">
          <div class="flex items-center justify-between">
            <div class="text-[18px] font-bold text-[#333333]">
              全部文章
            </div>
            <div class="flex items-center">
              <div
                v-for="item in tabList"
                :key="item.value"
                :class="[
                  {
                    '!bg-3887F5 !text-[#FFFFFF]': item.value === sortType,
                  },
                ]"
                class="py-[2px] px-[8px] bg-[#FFFFFF] rounded-[4px] text-[13px] text-[#666666] [&:not(:last-child)]:mr-[8px]"
                @click="changeTab(item)"
              >
                <div>
                  {{ item.name }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </u-sticky>
      <div class="min-h-[500px]">
        <div
          v-for="i in list"
          :key="i.id"
          class="[&:not(:last-child)]:mb-[12px]"
        >
          <essay-item :data="i" />
        </div>
      </div>
    </div>
    <u-safe-bottom />
  </div>
</template>

<script>
import EssayItem from "@/components/essay-item/index.vue";
import { articleV2LawyerPage, lawyerNewRank } from "@/api/lawyer.js";
import { isArrNull } from "@/libs/basics-tools.js";
import { toAskLawyer } from "@/libs/turnPages";
import EntranceLawyerTopic from "@/pages/sub/lawyer-home/topic/components/EntranceLawyerTopic.vue";
import EntranceAskLawyer from "@/pages/sub/lawyer-home/topic/components/EntranceAskLawyer.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import USticky from "@/uview-ui/components/u-sticky/u-sticky.vue";

export default {
  name: "AllArticle",
  components: {
    USticky,
    USafeBottom,
    EntranceAskLawyer,
    EntranceLawyerTopic,
    EssayItem,
  },
  data() {
    return {
      requestStatus: false,
      pageTotal: 0,
      /** tab数据 */
      tabList: [
        {
          name: "最新",
          value: 1,
        },
        {
          name: "最热",
          value: 2,
        },
      ],
      /** 排序 */
      sortType: 1,
      list: [],
      pageParams: {
        /** 页码 */
        currentPage: 1,
        /** 每页条数 */
        pageSize: 20,
      },
      /** 排行榜数据 */
      rankList: [],
    };
  },
  mounted() {
    this.getList();
    this.getRankList();
  },
  onReachBottom() {
    this.scrollToLower();
  },
  methods: {
    isArrNull,
    /** 获取排行榜数据 */
    getRankList() {
      lawyerNewRank({
        manType: 5,
        type: 13,
        rankType: 0,
      }).then(({ data = [] }) => {
        this.rankList = data;
      });
    },
    /** 切换tab */
    changeTab(item) {
      this.sortType = item.value;
      this.pageParams.currentPage = 1;
      this.list = [];
      this.getList();
    },
    scrollToLower() {
      if (
        this.pageParams.currentPage * this.pageParams.pageSize >=
        this.pageTotal
      )
        return;

      this.pageParams.currentPage++;
      this.getList();
    },
    getList() {
      this.requestStatus = false;
      articleV2LawyerPage({
        sortType: this.sortType,
        ...this.pageParams,
      })
        .then(({ data = {} }) => {
          const { records = [] } = data;
          this.list = [...this.list, ...records];

          this.pageTotal = data.total || 0;
        })
        .finally(() => {
          this.requestStatus = true;
        });
    },
    toAskLawyer() {
      toAskLawyer();
    },
  },
};
</script>
