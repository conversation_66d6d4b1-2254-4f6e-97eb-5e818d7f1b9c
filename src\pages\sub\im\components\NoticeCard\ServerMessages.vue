<template>
  <div class="w-[351px] p-[12px] box-border bg-[#FFFFFF] rounded-[8px] mt-[12px]">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <img
          v-if="currentEnum.icon"
          :src="currentEnum.icon"
          alt=""
          class="block w-[24px] h-[24px] mr-[4px]"
        >
        <div class="font-bold text-[16px] text-[#333333]">
          {{ data.messageTitle }}
        </div>
      </div>
      <div class="text-[12px] text-[#999999]">
        {{ data.createTime && parseTimeDiffToday(data.createTime) }}
      </div>
    </div>
    <div class="text-[14px] text-[#666666] mt-[8px]">
      {{ data.toText }}
    </div>
    <div
      v-if="currentEnum.text"
      class="text-[12px] text-[#3887F5] flex items-center justify-end mt-[12px]"
      @click="jump"
    >
      {{ currentEnum.text }}
      <i class="iconfont icon-erjiyoujiantou ml-[2px]" />
    </div>
  </div>
</template>

<script>
import { parseTimeDiffToday } from "@/libs/tools";

export default {
  name: "ServerMessages",
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      }
    },
    currentEnum: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  methods: {
    parseTimeDiffToday,
    jump(){
      this.$emit("jump");
    }
  }
};
</script>
