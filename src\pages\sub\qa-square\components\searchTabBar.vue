<template>
  <div class="tab-container">
    <div
      :style="[tabsStyle]"
      class="search-bar flex justify-between items-center"
    >
      <div class="flex">
        <div
          class="search-item"
          :class="{active: show1}"
          @click="handleClick('show1')"
        >
          {{ releaseDate.label }}
          <span
            class="icon2 iconfont icon-xiala"
            :class="{active: show1 }"
          />
        </div>
        <div
          class="search-item"
          :class="{active: show2}"
          @click="handleClick('show2')"
        >
          {{ typeInfo.label }}
          <span
            class="icon2 iconfont icon-xiala"
            :class="{active: show2 }"
          />
        </div>
        <div
          class="search-item"
          :class="{active: show3}"
          @click="handleClick('show3')"
        >
          {{ cityInfo.name || '地区' }}
          <span
            class="icon2 iconfont icon-xiala"
            :class="{active: show3 }"
          />
        </div>
      </div>
      <div
        class="flex items-center"
        @click.stop="changeOnlyThisProvince"
      >
        <i
          v-if="onlyThisProvince===0"
          class="iconfont icon-weigouxuan pr-[4px] !text-[16px] text-[#666666]"
        />
        <i
          v-else
          class="iconfont icon-danxuan pr-[4px] !text-[16px] text-[#3887F5]"
        />
        <p class="text-[12px] text-[#666666]">
          只看本省
        </p>
      </div>
    </div>
    <!--   案件协作 -->
    <popup-select
      :show.sync="show1"
      :activeStyle="activeStyle"
      :value="releaseDate.value"
      :list="releaseDateList"
      :contentWrapperStyle="contentWStyle"
      @handleSelect="changeReleaseDate"
    />
    <!-- 协助状态   -->
    <popup-check-box
      :show.sync="show2"
      :list="typeInfoList"
      :value="typeInfo.value"
      :contentWrapperStyle="contentWStyle"
      @handleSelect="changeTypeInfo"
    />
    <popup-location
      :show.sync="show3"
      nationwide
      :contentWrapperStyle="contentWStyle"
      @setCity="setCity"
    />
  </div>
</template>

<script>
import PopupLocation from "@/pages/index/components/popup-location/index.vue";
import PopupSelect from "@/pages/index/components/popupSelect.vue";
import { pxToRpx } from "@/libs/tools";
import PopupCheckBox from "@/pages/index/components/popupCheckBox.vue";
import { RELEASE_DATE_LIST } from "@/enum/qa";
import { dataDictionary } from "@/api";
import { turnToLawyerAuthResultPageToLogin } from "@/libs/turnPages";
import { isNull } from "@/libs/basics-tools";

export default {
  components: { PopupCheckBox, PopupSelect, PopupLocation },
  props: {
    localStatus: {
      type: Number,
      default: 0
    },
  },
  data() {
    return {
      show1: false,
      show2: false,
      show3: false,
      /* 发布日期 选中信息*/
      releaseDate: RELEASE_DATE_LIST[0],
      /* 发布类型 选中信息*/
      typeInfo: {},
      /* 发布日期 列表*/
      releaseDateList: RELEASE_DATE_LIST,
      /* 发布类型 列表*/
      typeInfoList: [],
      /* 城市信息*/
      cityInfo: {
        name: "全国"
      },
      /* 是否只查看本省份 0 否 1 是 */
      onlyThisProvince: 0,
      contentWStyle: {
        top: pxToRpx(41),
        backgroundColor: "white"
      },
      activeStyle: {
        color: "#3887F5"
      }
    };
  },
  computed: {
    /** 是否显示弹窗 */
    showPopup() {
      return (
        this.show1 ||
          this.show2 ||
          this.show3
      );
    },
    /** 顶部导航的样式 */
    tabsStyle() {
      if (!this.showPopup) return {};

      return uni.$u.addStyle({
        top: 0,
        right: 0,
        left: 0,
        position: "fixed",
        backgroundColor: "#fff"
      });
    }
  },
  mounted() {
    /* 获取一下本地存储的只看本省状态 */
    if(!isNull(uni.getStorageSync("onlyThisProvince"))) this.onlyThisProvince = uni.getStorageSync("onlyThisProvince");
    dataDictionary({ groupCode: "LAWYER_SPECIALITY" }).then(({ data = [] }) => {
      this.typeInfoList = data;
      this.typeInfoList.unshift({
        label: "全部类型",
        value: "",
      });
      this.typeInfo = this.typeInfoList[0];
      this.dataChange();
    });
  },
  methods: {
    // 案源线索子tab切换
    handleClick(type){
      /* 判断是不是城市选择 如果选择了只看本省 就不能选择城市 提示*/
      if(type === "show3" && this.onlyThisProvince === 1) {
        return uni.showToast({
          title: "关闭“只看本省”后可正常筛选地区",
          icon: "none"
        });
      }


      if(this[type]) return this[type] = false;
      this.show1 = false;
      this.show2 = false;
      this.show3 = false;
      this[type] = true;
    },
    // 城市选择
    setCity(city) {
      this.cityInfo = city;
      this.dataChange();
    },
    // 数据变化时重新请求数据
    dataChange(){
      const data =  {
        typeValue: this.typeInfo.value,
        dayType: this.releaseDate.value,
        regionCode: this.cityInfo.code || "",
        provinceCode: this.cityInfo.province || "",
        onlyThisProvince: this.onlyThisProvince
      };
      this.$emit("searchChange", data);
    },
    // 发布日期
    changeReleaseDate(item){
      this.releaseDate = item;
      this.dataChange();
    },
    // 发布类型
    changeTypeInfo(item){
      this.typeInfo = item;
      this.dataChange();
    },
    /* 只看本省  */
    changeOnlyThisProvince(){
      turnToLawyerAuthResultPageToLogin(() => {
        this.onlyThisProvince = this.onlyThisProvince ? 0 : 1;
        /* 选择本省的状态存储下来 */
        uni.setStorageSync("onlyThisProvince", this.onlyThisProvince);
        this.dataChange();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.tab-container{
  height: 41px;
  .search-bar{
    padding: 0 16px;
    position: relative;
    z-index: 2;
    .search-item{
      height: 41px;
      padding-right: 16px;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      display: flex;
      align-items: center;
      justify-content: center;
      &.active{
        color: #3887F5;
      }
      .icon2{
        transition: all 0.3s;
        margin-left: 3px;
        font-size: 12px;
        &.active {
          transform: rotate(180deg);
        }
      }
      .icon3{
        font-size: 16px;
      }
    }
  }
}
</style>
