<template>
  <login-layout>
    <u-sticky>
      <home-header />
    </u-sticky>
    <div class="relative">
      <img
        class="absolute top-0 left-0 w-full h-[189px] z-[-1]"
        src="@/pages/index/imgs/<EMAIL>"
        alt=""
      >
      <p class="pl-[16px] font-[600] text-[22px] text-[#364978] pt-[12px] pb-[16px]">
        律师的协作交流工具平台
      </p>
      <data-overview />
      <common-tools />
      <third-party-service-tools />
      <div class="h-[20px]" />
    </div>
  </login-layout>
</template>

<script>
import LoginLayout from "@/components/login/LoginLayout.vue";
import HomeHeader from "@/pages/index/components/HomeHeader.vue";
import CommonTools from "@/pages/index/components/CommonTools.vue";
import ThirdPartyServiceTools from "@/pages/index/components/ThirdPartyServiceTools.vue";
import DataOverview from "@/pages/index/components/DataOverview.vue";

export default {
  name: "Index",
  components: { DataOverview, ThirdPartyServiceTools, CommonTools, HomeHeader, LoginLayout }
};
</script>

<style scoped lang="scss">

</style>