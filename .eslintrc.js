module.exports = {
  root: true,
  env: {
    browser: true,
    node: true,
    es6: true,
  },
  extends: ["plugin:vue/recommended"],
  globals: {
    uni: "readonly",
    getCurrentPages: "readonly",
  },
  // required to lint *.vue files
  plugins: ["vue"],
  // add your custom rules here
  rules: {
    semi: [2, "always"],
    "no-undef": "error",
    // 没有使用的变量警告
    "no-unused-vars": ["warn"],
    // 箭头函数前后必须要有空格
    "arrow-spacing": [
      "warn",
      {
        before: true,
        after: true,
      },
    ],
    // 要求大括号内必须有空格
    "object-curly-spacing": ["error", "always"],
    // 缩进
    indent: [2, 2],
    // 关键字后加空格
    "space-after-keywords": [0, "always"],
    // 对象字面量中冒号的前后空格
    "key-spacing": [1, { beforeColon: false, afterColon: true }],
    // 逗号前后的空格
    "comma-spacing": 1,
    // 符号后加空格
    "space-infix-ops": ["warn", { int32Hint: false }],
    // 注释 // 或 /* 之后必须有一个空格
    "spaced-comment": ["warn", "always"],
    // js中应使用单引号替代双引号
    quotes: ["warn", "double"],
    // 不适用连字符 因为微信小程序使用连字符传递参数有时候无法正确获取
    "vue/attribute-hyphenation": ["error", "never"],
    "vue/multi-word-component-names": ["off"],
    // 为了和微信小程序保持一致，组件名使用 kebab-case 防止发生意料之外的错误
    "vue/component-name-in-template-casing": ["error", "kebab-case"],
    // vue 组件中属性顺序
    "vue/order-in-components": [
      "warn",
      {
        order: [
          "el",
          "name",
          "key",
          "parent",
          "head",
          "functional",
          ["delimiters", "comments"],
          ["components", "directives", "filters"],
          "extends",
          "mixins",
          ["provide", "inject"],
          "ROUTER_GUARDS",
          "layout",
          "middleware",
          "validate",
          "scrollToTop",
          "transition",
          "loading",
          "inheritAttrs",
          "model",
          ["props", "propsData"],
          "emits",
          "setup",
          "asyncData",
          "data",
          "onLoad",
          "onShow",
          "fetch",
          "computed",
          "watch",
          "watchQuery",
          "LIFECYCLE_HOOKS",
          "methods",
          ["template", "render"],
          "renderError",
        ],
      },
    ],
  },
};
