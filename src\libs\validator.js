import Schema from "@/uview-ui/libs/util/async-validator";

/**
 * 验证器
 * 该验证器是为了某些需求调整，比如不需要界面上的错误提示，需要弹窗提示的情况
 * @param model
 * @param {Object} rules
 */
export function appValidator(model, rules) {
  return new Promise((resolve, reject) => {
    const validator = new Schema(rules);

    validator
      .validate(model)
      .then(() => {
        resolve();
      })
      .catch(({ errors, fields }) => {
        // 只抛出第一个错误
        uni.showToast({
          title: errors[0].message,
          icon: "none",
        });
        reject({
          errors,
          fields
        });
      });
  });
}
