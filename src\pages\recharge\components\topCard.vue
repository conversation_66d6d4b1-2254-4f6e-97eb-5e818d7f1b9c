<template>
  <div class="top-card flex flex-align-center flex-space-between">
    <img class="top-bg" src="@/pages/recharge/img/<EMAIL>" alt="">
    <div class="relative">
      <div class="title">
        {{ info.title }}
      </div>
      <div
        class="num"
        @click="$emit('numberClick')"
      >
        {{ info.type === 'price' ? priceNumber(info.number) : info.number }} <span class="iconfont icon-erjiyoujiantou" />
      </div>
    </div>
    <img
      mode="widthFix"
      class="tip relative"
      src="@/pages/recharge/img/<EMAIL>"
      alt=""
      @click="$emit('tipClick')"
    >
  </div>
</template>

<script>
import { priceNumber } from "@/libs/tool";

export default {
  methods: { priceNumber },
  props: {
    info: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.top-card{
  margin-top: 16px;
  height: 94px;
  border-radius: 12px 12px 12px 12px;
  opacity: 1;
  padding-left: 20px;
  position: relative;
  .title{
    font-size: 12px;
    font-weight: 400;
    color: #999999;
  }
  .num{
    font-size: 28px;
    font-weight: bold;
    color: #333333;
    line-height: 36px;
    margin-top: 4px;
    .iconfont{
      font-size: 16px;
      color: #6E6F70;
      font-weight: normal;
    }
  }
  .tip{
    width:70px;
    height: 25px;
    margin-right: -4px;
  }
  .top-bg{
    position: absolute;
    width: 100%;
    height: 94px;
    left: 0;
    top: 0;
    z-index: 1;
  }
  .relative{
    position: relative;
    z-index: 2;
  }
}
</style>
