<template>
  <view class="upload">
    <view class="form">
      <contact-title-form v-model="submitData.contractName" />
      <contact-type-form v-model="submitData.contractType" />
      <contact-price-form
        v-model="submitData.price"
        :priceInterval="priceInfo"
      />
      <contact-file-form v-model="submitData.contractUrl" />
      <contact-originality v-model="submitData.isOriginal" />
    </view>
    <div
      class="btn"
      @click="submitFun"
    >
      上传
    </div>
    <back-app-popup
      :show.sync="showSuccessPop"
      @reset="resetFun"
    />
  </view>
</template>

<script>
import ContactTitleForm from "@/pages/sub/other/contractUpload/components/contactComponents/contact-title-form.vue";
import ContactTypeForm from "@/pages/sub/other/contractUpload/components/contactComponents/contact-type-form.vue";
import ContactFileForm from "@/pages/sub/other/contractUpload/components/contactComponents/contact-file-form.vue";
import { contactUpload, getCommonConfigKey } from "@/api";
import BackAppPopup from "@/pages/sub/other/contractUpload/components/backAppPopup/index.vue";
import ContactPriceForm from "@/pages/sub/other/contractUpload/components/contactComponents/contact-price-form.vue";
import { appValidator } from "@/libs/validator";
import ContactOriginality from "@/pages/sub/other/contractUpload/components/contactComponents/ContactOriginality.vue";

export default {
  name: "Index",
  components: {
    ContactOriginality,
    ContactPriceForm,
    BackAppPopup,
    ContactTitleForm,
    ContactTypeForm,
    ContactFileForm,
  },
  data() {
    return {
      submitData: {
        // 合同名称
        contractName: "",
        // 案件类型
        contractType: "",
        // 上传地址
        contractUrl: "",
        // 价格
        price: "",
        // 是否原创
        isOriginal: "",
      },
      showSuccessPop: false,
      priceInfo: {},
    };
  },
  computed: {
    /** 是否为原创 */
    isOriginal() {
      return this.submitData.isOriginal === 1;
    },
    /* 验证规则*/
    rules() {
      const rules = {
        contractName: [{ required: true, message: "请输入合同模板标题" }],
        contractType: [{ required: true, message: "请选择合同模板分类" }],
        price: [
          { required: true, message: "请输入合同模版价格" },
          /* 正则判断数字 最多两位小数*/
          {
            pattern: /^\d+(\.\d{1,2})?$/,
            message: "价格只能整数或者最多两位小数",
          },
          // 自定义验证
          {
            validator: (rule, value, callback) => {
              /* 后端接受的是分 转一次*/
              const price = Number(this.submitData.price) * 100;

              if (
                Number(price) > this.priceInfo.maxPrice ||
                Number(price) < this.priceInfo.minPrice
              ) {
                callback(
                  new Error(
                    `请输入${this.priceInfo.min}-${this.priceInfo.max}之间的金额，请重新输入`
                  )
                );
              } else {
                callback();
              }
            },
          },
        ],
        contractUrl: [{ required: true, message: "请上传合同模板" }],
        isOriginal: [{ required: true, message: "请选择是否原创" }],
      };

      return rules;
    },
  },
  created() {
    getCommonConfigKey({
      paramName: "lawyer_contract_download_commission",
    }).then(({ data }) => {
      const val = JSON.parse(data.paramValue);

      this.priceInfo = {
        min: val.minPrice / 100,
        max: val.maxPrice / 100,
        ...val,
      };

      console.log(this.priceInfo);
    });
  },
  methods: {
    /** 重置 */
    resetFun() {
      this.submitData = this.$options.data().submitData;
    },
    submitFun() {
      console.log(this.submitData, "传递的数据");

      appValidator(this.submitData, this.rules)
        .then(() => {
          /* 后端接受的是分 转一次*/
          const price = Number(this.submitData.price) * 100;

          contactUpload({
            ...this.submitData,
            // 原创才有价格
            price,
          }).then((res) => {
            console.log("res:", res);
            this.showSuccessPop = true;
          });
        })
        .catch((err) => {
          console.log(err, "appValidator");
          this.$toast(err.errors[0].message);
        });
    },
  },
};
</script>
<style>
page {
  background: #ffffff;
}
</style>
<style lang="scss" scoped>
.upload {
  .form {
    padding: 0 16px;
  }

  .btn {
    width: 343px;
    height: 44px;
    background: #3887f5;
    border-radius: 22px 22px 22px 22px;
    font-size: 16px;
    color: #fff;
    text-align: center;
    line-height: 44px;
    position: fixed;
    bottom: 27px;
    left: 16px;
  }
}
</style>
