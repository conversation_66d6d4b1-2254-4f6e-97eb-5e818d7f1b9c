<template>
  <div class="header-box">
    <div
      v-show="list.length > 1"
      :class="[{ gd: list.length > 1 }, headerClass]"
      class="header"
    >
      <div
        v-for="(item, index) in list"
        :key="index"
        :class="{ select: index === currentIndex }"
        @click="handleClick(index)"
      >
        <span>{{ item.label }}</span>
      </div>
    </div>
    <app-back-top :scrollTop="scrollTop" />
  </div>
</template>

<script>
import { bindOnHook } from "@/libs/hooks.js";
import { getClientRect, setNavigationBarTitle } from "@/libs/tools.js";
import AppBackTop from "@/components/AppComponents/AppBackTop/index.vue";

const tabList = [
  {
    label: "评价",
    value: "#home-score",
  },
  {
    label: "合同",
    value: "#home-contract",
  },
  {
    label: "案件",
    value: "#home-case",
  },
  {
    label: "文章",
    value: "#home-essay",
  },
];

export default {
  name: "LawyerHomeHeader",
  components: { AppBackTop },
  props: {
    lawyerInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      list: [],
      /** 元素距顶部的高度 */
      rectTop: [],
      /** 当前的索引 */
      currentIndex: 0,
      /** 控制显示隐藏 */
      show: false,
      /** 滚动条的距离 */
      scrollTop: 0,
      /** 是否计算滚动 */
      isScroll: true,
    };
  },
  computed: {
    headerClass() {
      return `grid-cols-${this.list.length}`;
    },
  },
  watch: {
    show(value) {
      if (value) {
        // 更改页面标题
        setNavigationBarTitle({
          title: this.lawyerInfo.realName,
        });
      } else {
        setNavigationBarTitle({
          title: "",
        });
      }
    },
  },
  created() {
    /** 如果界面有滚动，则判断滚动到哪个元素上面 */
    const scroll = (scrollTop) => {
      if (!this.isScroll) return;

      // 倒序遍历
      for (let i = this.rectTop.length - 1; i >= 0; i--) {
        if (!this.rectTop[i]) continue;

        if (scrollTop >= this.rectTop[i]) {
          this.currentIndex = i;
          return;
        }
      }

      this.currentIndex = 0;
    };

    /** 获取元素距顶部的距离 */
    const getRect = () => {
      Promise.all(
        tabList.map((item) => {
          return this.getRect(item.value);
        })
      ).then((res) => {
        // tabList中对应的index res为0的元素
        this.list = tabList.filter((item, index) => res[index] !== 0);
        console.log(this.list, " this.list ");
        // 获取存在的元素距顶部的距离
        this.rectTop = res.filter((item, index) => res[index]);
      });
    };
    const fnCallback = async (e) => {
      this.show = e.scrollTop > 200;
      // 导航栏显示时，改变头部颜色
      if (this.show) {
        setNavigationBarTitle({
          frontColor: "#000000",
          backgroundColor: "#F5F5F7",
          title: this.lawyerInfo.realName,
        });
      } else {
        setNavigationBarTitle({
          frontColor: "#000000",
          backgroundColor: "#E5EFFF",
          title: "",
        });
      }

      this.scrollTop = e.scrollTop || 0;
      scroll(e.scrollTop);
    };
    const parent = bindOnHook.call(this, "onPageScroll", fnCallback);
    this.$on("hook:destroyed", () => {
      parent.$off("hook:onPageScroll", fnCallback);
    });

    this.$store.commit("lawyerHome/SET_GET_TITLE_CALLBACK", () => {
      // 这里是为了让页面渲染后，再获取页面元素的位置
      setTimeout(() => {
        this.$nextTick(() => {
          getRect();
        });
      }, 200);
    });
  },
  methods: {
    /** 点击事件 */
    handleClick(index) {
      const top = this.rectTop[index];

      this.currentIndex = index;

      this.isScroll = false;

      uni.pageScrollTo({
        scrollTop: top,
        duration: 300,
        complete: () => {
          setTimeout(() => {
            this.isScroll = true;
          }, 400);
        },
      });
    },
    /** 计算元素距顶部的距离 */
    getRect(selector) {
      return new Promise(async (resolve) => {
        try {
          const res = await getClientRect(selector);

          const header = await getClientRect.call(this, ".header");

          // 这里使用进度条距顶部的距离 + 元素对视窗的距离 - 头部的高度
          return resolve(res.top + this.scrollTop - header?.height || 0);
        } catch (e) {
          return resolve(0);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.gd {
  display: grid;
}

.header {
  place-items: center;
  height: 44px;
  line-height: 44px;
  padding-top: 4px;
  background-color: #f4f5f7;

  font-size: 16px;
  font-weight: 400;
  color: #666666;
}

.select {
  position: relative;
  height: 100%;
  font-weight: bold;
  font-size: 18px;
  color: #333333;

  &::after {
    content: "";
    position: absolute;
    bottom: 1px;
    left: 50%;
    transform: translateX(-50%);
    width: 14px;
    height: 3px;
    background: #3887f5;
    border-radius: 70px 70px 70px 70px;
  }
}
</style>
