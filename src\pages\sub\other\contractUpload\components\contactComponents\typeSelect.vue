<template>
  <div class="type-select">
    <app-popup
      :show="getShow"
      mode="bottom"
      round="16"
      @close="close"
    >
      <div>
        <div class="header flex flex-space-between flex-align-center">
          <p
            class="close"
            @click="$emit('update:show', false)"
          >
            取消
          </p>
          <p class="title flex-1 text-align-center">
            合同模板分类
          </p>
          <p
            class="determine"
            @click="determine"
          >
            确定
          </p>
        </div>
        <div class="type-wrapper">
          <div
            v-for="item in LAWYER_SPECIALITY"
            :key="item.value"
            :class="{ active: active.value === item.value }"
            class="item"
            @click="handleClick(item)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
import { dataDetailList } from "@/api";
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { isNull } from "@/libs/basics-tools.js";

export default {
  name: "TypeSelect",
  components: { AppPopup },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      type: 1,
      LAWYER_SPECIALITY: [],
      active: {
        label: "全部",
        value: null,
      },
    };
  },
  computed: {
    getShow: {
      get() {
        return this.show;
      },
      set(value) {
        this.$emit("update:show", value);
      },
    },
  },
  created() {
    dataDetailList({
      groupCode: "LAWYER_CONTRACT_TYPE",
    }).then(({ data }) => {
      // this.LAWYER_SPECIALITY = Object.freeze([{ label: "全部", value: null }, ...data]);
      this.LAWYER_SPECIALITY = data;
      this.$emit("getType", data);
    });
  },
  mounted() {},
  methods: {
    handleClick(item) {
      this.active = item;
    },
    // 关闭弹窗
    close() {
      this.getShow = false;
    },
    // 确定
    determine() {
      if (isNull(this.active.value)) return;
      this.$emit("changeType", this.active);
    },
  },
};
</script>

<style lang="scss" scoped>
.type-select {
  position: relative;

  .type-wrapper {
    padding: 8px 16px 16px;
    display: grid;
    grid-template-columns: repeat(3, 96px);
    grid-gap: 24px;

    .item {
      line-height: 38px;
      opacity: 1;
      background: #f5f5f7;
      border-radius: 20px 20px 20px 20px;
      font-size: 12px;
      font-weight: 400;
      color: #333333;
      text-align: center;

      &.active {
        color: #3887f5;
        background: #ebf3fe;
      }
    }
  }
}

.header {
  padding: 0 16px;
  height: 48px;
  box-shadow: inset 0px 0px 0px 0px #eeeeee;
  border-bottom: 0.5px solid #f5f5f5;

  .close {
    font-size: 14px;
    font-weight: 400;
    color: #999999;
  }

  .determine {
    font-size: 14px;
    font-weight: 400;
    color: #3887f5;
  }

  .title {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
  }
}
</style>
