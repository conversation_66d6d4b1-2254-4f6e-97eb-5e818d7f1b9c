<template>
  <theme-layout-card :theme="theme">
    <div class="top">
      <div class="title">
        律师您好，这是我的联系方式，方便的话可以打电话给我
      </div>
      <div
        class="btn"
        @click="callPhone"
      >
        拨打电话
      </div>
    </div>
    <div class="desc">
      为保护隐私号码已加密，拨打电话时间截止到 <span class="date">{{ customExtsinExt.deadline }}</span>
    </div>
  </theme-layout-card>
</template>

<script>
import ThemeLayoutCard from "@/pages/sub/im/components/Cards/ThemeLayoutCard/index.vue";
import cardProps, { customComputed } from "@/pages/sub/im/mixins/card-props.js";
import { getNewServerCall } from "@/api/im.js";
import { caseInfoStateProps } from "@/pages/sub/im/mixins/case-info-state.js";

export default {
  name: "UserPhoneCard",
  components: { ThemeLayoutCard },
  mixins: [cardProps, customComputed, caseInfoStateProps],
  methods: {
    callPhone(){
      getNewServerCall({ caseSourceServerV2Id: this.caseSourceServerV2Id }).then(({ data }) => {
        uni.makePhoneCall({
          phoneNumber: data.realPhone
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.top{
  padding-bottom: 18px;
  border-bottom: 0.5px solid #EEEEEE;
  .title{
    font-size: 14px;
    font-weight: 400;
    color: #333333;
  }
  .btn{
    margin-top: 18px ;
    text-align: center;
    line-height: 32px;
    border-radius: 68px 68px 68px 68px;
    opacity: 1;
    border: 1px solid #3887F5;
    font-size: 14px;
    font-weight: 400;
    color: #3887F5;
  }
}
.desc{
  padding-top: 12px;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  .date{
    color: #EB4738;
  }
}
</style>
