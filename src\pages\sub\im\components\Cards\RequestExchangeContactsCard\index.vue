<template>
  <theme-layout-card :theme="theme">
    <div>
      <div class="flex items-center">
        <img
          v-if="isExchangePhone"
          class="w-[24px] h-[24px]"
          src="@/pages/sub/im/imgs/icon_phone.png"
          alt=""
        >
        <img
          v-else
          class="w-[24px] h-[24px]"
          src="@/pages/sub/im/imgs/icon_wecaht.png"
          alt=""
        >
        <p class="pl-[8px] text-[16px] text-[#333333]">
          请求交换您的{{ exchangeText }}
        </p>
      </div>

      <!-- 对方看到 卡片样式     -->
      <div v-if="!isSelf">
        <!--  待确定      -->
        <div
          v-if="exchangeStatus===0"
          class="flex pt-[13px] min-w-[196px]"
        >
          <div
            class="flex-1 text-center box-border text-[14px] text-[#333333] rounded-[8px] border-[1px] border-solid border-[#CCCCCC] leading-[34px]"
            @click.stop="handleRefused"
          >
            拒绝
          </div>
          <div
            class="flex-1 ml-[12px] bg-[#3887F5] rounded-[8px] text-[14px] text-[#FFFFFF] leading-[34px] text-center"
            @click.stop="handleAgree"
          >
            同意交换
          </div>
        </div>

        <!--  拒绝or同意      -->
        <div
          v-else
          class="pt-[13px]"
        >
          <div
            v-if="exchangeStatus===2"
            class="bg-[#F7F6F6] text-center px-[21px] py-[7px] rounded-[8px] text-[14px] text-[#999999]"
          >
            已拒绝对方交换{{ exchangeText }}请求
          </div>
          <div
            v-if="exchangeStatus===1"
            class="bg-[#F7F6F6] text-center px-[21px] py-[7px] rounded-[8px] text-[14px] text-[#999999]"
          >
            已同意对方交换{{ exchangeText }}请求
          </div>
        </div>
      </div>

      <!--  自己看到的卡片    -->
      <div
        v-else
        class=" pt-[13px]"
      >
        <div
          v-if="exchangeStatus===1"
          class="w-[196px] h-[34px] bg-[#FFFFFF] rounded-[8px] text-[14px] text-[#999999] center"
        >
          对方已同意交换{{ exchangeText }}
        </div>
        <div
          v-else-if="exchangeStatus===2"
          class="w-[196px] h-[34px] bg-[#FFFFFF] rounded-[8px] text-[14px] text-[#999999] center"
        >
          对方已拒绝交换{{ exchangeText }}
        </div>
        <div
          v-else
          class="w-[196px] h-[34px] bg-[#FFFFFF] rounded-[8px] text-[14px] text-[#999999] center"
        >
          等待对方同意
        </div>
      </div>
    </div>
  </theme-layout-card>
</template>

<script>
import ThemeLayoutCard from "@/pages/sub/im/components/Cards/ThemeLayoutCard/index.vue";
import cardProps, { customComputed, messageAndConversationInfoComputed } from "@/pages/sub/im/mixins/card-props";
import { conversationIdSymbolProps, conversationInfoStateProps } from "@/pages/sub/im/mixins/case-info-state";
import { isArrNull } from "@/libs/basics-tools";
import { lawyerAgreeExchangeInfo } from "@/api/im";
import { whetherToBindWechat } from "@/libs/tools";

export default {
  name: "RequestExchangeContactsCard",
  components: { ThemeLayoutCard },
  mixins: [cardProps, customComputed, conversationInfoStateProps, messageAndConversationInfoComputed, conversationIdSymbolProps],
  computed: {
  //   判断是不是交换手机号 type=0 电话 1 微信
    isExchangePhone() {
      return this.customExts.type === 0;
    },
    //   交换文案
    exchangeText() {
      return this.isExchangePhone ? "电话" : "微信";
    },
    //   获取 对方操作状态 0待同意 1 已同意 2 已拒绝
    //   agreeList=已同意交换的imToken，disAgreeList=拒绝的imToken。发送出去 默认就是agreeList里面有一个token了
    exchangeStatus() {
      const { agreeList = [], disAgreeList = [] } = this.customExts;
      // 只要有一个人拒绝 就是拒绝
      if(!isArrNull(disAgreeList)) return 2;
      // 双方同意
      if(!isArrNull(agreeList) && agreeList.length > 1) return 1;
      return 0;
    }
  },
  mounted() {
  },
  methods: {
    /* 同意或者拒绝交换联系方式 */
    handleAgreeOrRefused(type) {
      lawyerAgreeExchangeInfo({
        msgId: this.data.msgId,
        // 0 拒绝 1 同意
        agreeStatus: type,
        imSessionId: this.conversationId,
        type: this.customExts.type
      });
    },
    handleRefused(){
      this.handleAgreeOrRefused(0);
    },
    handleAgree(){
      if(!this.isExchangePhone){
        whetherToBindWechat(() => {
          this.handleAgreeOrRefused(1);
        });
        return;
      }
      this.handleAgreeOrRefused(1);
    }
  }
};
</script>

<style scoped lang="scss">

</style>