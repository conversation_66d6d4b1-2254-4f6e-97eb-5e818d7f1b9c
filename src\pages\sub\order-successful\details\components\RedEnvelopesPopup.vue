<template>
  <app-popup
    :show="show"
    bgColor="transparent"
    mode="center"
  >
    <div class="w-[296px] h-[360px] position-relative box-border">
      <i
        class="iconfont icon-cuowu block !text-[24px] text-[#FFFFFF] z-50 absolute -top-[32px] right-[8px]"
        @click="show = false"
      />
      <button
        v-if="isOpen"
        class="absolute z-10 w-[237px] h-[44px] flex items-center justify-center -bottom-[60px] absolute-x-center"
        open-type="share"
      >
        <img
          alt=""
          class="background-image"
          src="@/pages/sub/order-successful/details/img/<EMAIL>"
        >
        <span class="font-bold text-[16px] text-[#7D1E2A]"> 分享到微信 </span>
      </button>
      <!-- 未打开红包时红包顶部 -->
      <div
        :class="[openClass]"
        class="w-[296px] h-[249px] absolute top-0 position-relative box-border px-[26px] pt-[24px] z-20"
        @animationend="handleOpenEnd"
      >
        <img
          alt=""
          class="background-image"
          src="@/pages/sub/order-successful/details/img/Frame8696.png"
        >
        <div class="flex items-center">
          <img
            :src="lawyerInfo.lawyerAvatar"
            alt=""
            class="w-[40px] h-[40px] rounded-full shrink-0 mr-[6px]"
          >
          <div
            class="text-[18px] text-[#F9FBE8] [text-shadow:0px_0px_6px_rgba(229,47,76,0.6)]"
          >
            沾沾{{ lawyerInfo.lawyerName }}律师成单喜气
          </div>
        </div>
        <div class="font-bold text-[22px] text-[#F5E6BC] mt-[10px]">
          最高瓜分<span class="text-[32px]">100</span>个法临币
        </div>
        <div class="flex items-center justify-end mt-[8px]">
          <img
            alt=""
            class="block w-[107px] h-[26px]"
            src="@/pages/sub/order-successful/details/img/Group8448.png"
          >
        </div>
      </div>
      <!-- 打开红包后红包顶部 -->
      <div
        v-if="isOpen"
        class="absolute w-[296px] h-[324px] bottom-0 z-10 text-center pt-[36px] box-border"
      >
        <img
          alt=""
          class="background-image"
          src="@/pages/sub/order-successful/details/img/Group8697.png"
        >
        <img
          :src="lawyerInfo.lawyerAvatar"
          alt=""
          class="w-[60px] h-[60px] bg-[#D9D9D9] rounded-full absolute absolute-x-center -top-[36px]"
        >
        <div v-if="alreadyReceived">
          <div class="text-[14px] text-[#8F5E64] mt-[12px]">
            <div>恭喜您，沾沾{{ lawyerInfo.lawyerName }}律师的成单喜气</div>
            <div>已成功抢到红包哦</div>
          </div>
          <div class="font-bold text-[24px] text-[#7D1E2A] mt-[24px]">
            获得<span class="text-[32px]">{{ alreadyReceivedAmount }}</span>个法临币
          </div>
          <div class="text-[12px] text-[#8F5E64] mt-[10px]">
            平台已发放至赠送账户，可用于抢案源哦
          </div>
        </div>
        <div v-else>
          <div class="font-bold text-[24px] text-[#7D1E2A]">
            手慢了
          </div>
          <div class="text-[16px] text-[#8F5E64] mt-[8px]">
            {{ data.redPacketTotalNum }}个法临币红包已瓜分完
          </div>
          <img
            alt=""
            class="block w-[140px] h-[108px] mx-auto mt-[8px]"
            src="@/pages/sub/order-successful/details/img/<EMAIL>"
          >
        </div>
      </div>
      <img
        :class="[buttonClass]"
        alt=""
        class="block w-[200px] h-[200px] z-30 top-[145px] absolute left-[50px]"
        src="@/pages/sub/order-successful/details/img/im45ee.png"
        @animationend="handleClickEnd"
        @click="openRedEnvelopes"
      >
      <div class="w-[296px] h-[172px] absolute bottom-0 z-20 box-border">
        <img
          alt=""
          class="background-image"
          src="@/pages/sub/order-successful/details/img/Frame8697.png"
        >
        <div
          class="text-[14px] text-[#FFFFFF] absolute bottom-[24px] absolute-x-center w-full text-center"
        >
          · 已有{{ data.redPacketGetNum }}人沾他喜气，还剩{{
            data.redPacketRemainNum
          }}个现金红包 ·
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { getRedPacket } from "@/api";
import { turnToLawyerAuthResultPageToLogin } from "@/libs/turnPages";
import { flbAmount } from "@/libs/tools";

export default {
  name: "RedEnvelopesPopup",
  components: { AppPopup },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    lawyerInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      buttonClass: "",
      openClass: "",
      /** 红包是否打开 */
      isOpen: false,
      getRedRes: {},
      opening: false
    };
  },
  computed: {
    /** 是否已经领取过红包 */
    alreadyReceived() {
      return this.getRedRes.status === 1 || this.data.alreadyReceived;
    },
    alreadyReceivedAmount() {
      return this.getRedRes.amount
        ? flbAmount(this.getRedRes.amount)
        : flbAmount(this.data.alreadyReceivedAmount);
    },
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  watch: {
    "data.alreadyReceived": {
      handler(val) {
        if (val) {
          this.buttonClass = "hidden";
          this.openClass = "hidden";
          this.isOpen = true;
        }
      },
      immediate: true,
    },
  },
  methods: {
    /** 点击打开红包 */
    openRedEnvelopes() {
      if(this.opening) return;

      this.opening = true;

      turnToLawyerAuthResultPageToLogin(() => {
        getRedPacket({ id: this.data.id }).then((res) => {
          this.getRedRes = res.data;
          this.buttonClass = "scale";
        }).catch(() => {
          this.opening = false;
        });
      });
    },
    handleClickEnd() {
      this.openClass = "open";
      this.isOpen = true;
    },
    /**
     * 红包打开动画完成后会触发的回调
     * 因为该弹窗有两种状态，一种是进入页面时红包已经打开过了，这种情况下就不需要播放打开动画，而是直接展示
     * 所以在动画播放完毕后才触发事件，不然会触发上面 watch 中的事件，导致动画还没有播放完毕相关的dom直接就被隐藏
     */
    handleOpenEnd() {
      this.$emit("getRedPacket", this.getRedRes);
    },
  },
};
</script>

<style lang="scss" scoped>
@keyframes scale-animation {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes open-animation {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
    opacity: 0;
  }
}

.scale {
  animation: scale-animation 2s linear forwards;
}

.open {
  animation: open-animation 1s linear forwards;
}
</style>
