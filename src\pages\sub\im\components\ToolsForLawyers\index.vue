<template>
  <div class="tools-for-lawyers flex ">
    <div
      v-if="!getIsSendLater"
      class="item flex flex-space-center flex-align-center"
      @click.stop="handleServeLater"
    >
      <img
        alt=""
        class="tool-img"
        src="../../imgs/tool-5.png"
      >
      稍后服务
    </div>
    <div
      v-if="isCaseInfoOrderPay&&!submissionsSendState"
      class="item flex flex-space-center flex-align-center"
      @click.stop="toSubmissions"
    >
      <img
        alt=""
        class="tool-img"
        src="../../imgs/tool-4.png"
      >
      咨询意见书
    </div>
    <div
      v-if="!isCaseInfoItTheSourceOfTheCase"
      class="item flex flex-space-center flex-align-center"
      @click.stop="$emit('handleServicePopUp')"
    >
      <img
        alt=""
        class="tool-img"
        src="../../imgs/tool-6.png"
      >
      付费服务
    </div>
    <div
      v-if="!isCaseInfoQuestionClosely"
      class="item flex flex-space-center flex-align-center"
      @click.stop="$emit('handlePhonePopUp')"
    >
      <img
        alt=""
        class="tool-img"
        src="../../imgs/tool-7.png"
      >
      电话号码
    </div>
  </div>
</template>

<script>
import {
  caseInfoStateProps,
  conversationIdSymbolProps
} from "@/pages/sub/im/mixins/case-info-state.js";
import { turnPages } from "@/libs/turnPages.js";
import { getImCacheConfigToId } from "@/libs/token.js";
import { isNull } from "@/libs/basics-tools.js";

export default {
  name: "ToolsForLawyers",
  mixins: [caseInfoStateProps, conversationIdSymbolProps],
  props: {
    submissionsSendState: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      /* 是否发送过稍后服务*/
      isSendLater: false
    };
  },
  computed: {
    /* im缓存的信息*/
    imCacheConfig() {
      return getImCacheConfigToId(this.conversationId);
    },
    /* 获取是否发送过稍后服务 没有缓存的时候判断变量isSendLater 否则走缓存判断*/
    getIsSendLater(){
      return  isNull(this.imCacheConfig.isSendLater) ? this.isSendLater : this.imCacheConfig.isSendLater;
    }
  },
  methods: {
    toSubmissions(){
      turnPages({
        path: "/pages/sub/im/submissions/update",
        query: {
          id: this.caseSourceServerV2Id
        }
      });
    },
    handleServeLater(){
      this.isSendLater = true;
      this.$emit("handleServeLater");
    }
  }
};
</script>

<style lang="scss" scoped>
.tools-for-lawyers {
  width: 100%;
  background: #F5F5F7;
  overflow-x: auto;
  padding: 8px 0;

  .item {
    flex-shrink: 0;
    margin-left: 16px;
    font-size: 12px;
    background: #FFFFFF;
    border-radius: 68px 68px 68px 68px;
    padding: 4px 12px;
    font-weight: 400;
    color: #333333;

    .tool-img {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
  }
}
</style>
