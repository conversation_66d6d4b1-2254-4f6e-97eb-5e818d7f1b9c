<template>
  <theme-layout-card :theme="theme">
    <div>
      <div class="flex items-center">
        <img
          v-if="isExchangePhone"
          class="w-[24px] h-[24px]"
          src="@/pages/sub/im/imgs/icon_phone.png"
          alt=""
        >
        <img
          v-else
          class="w-[24px] h-[24px]"
          src="@/pages/sub/im/imgs/icon_wecaht.png"
          alt=""
        >
        <p class="pl-[8px] text-[16px] text-[#333333]">
          请求交换您的{{ exchangeText }}
        </p>
      </div>
      <p class="pl-[32px] text-ellipsis max-w-[150px] text-[16px] text-[#3887F5] pt-[4px]">
        {{ customExts.content }}
      </p>

      <!-- 联系方式    -->
      <div class="w-[196px]">
        <!--  手机号      -->
        <div
          v-if="isExchangePhone"
          class="flex pt-[13px]"
        >
          <div
            class="flex-1 bg-[#3887F5] rounded-[8px] text-[14px] text-[#FFFFFF] h-[34px] center text-center"
            @click.stop="callPhone"
          >
            拨打
          </div>
          <div
            class="flex-1  ml-[12px]  text-center box-border text-[14px] text-[#333333] rounded-[8px] border-[1px] border-solid border-[#CCCCCC]  h-[34px] center"
            @click.stop="copyValue"
          >
            点击复制
          </div>
        </div>
        <!--  微信      -->
        <div
          v-else
          class=" pt-[13px]"
        >
          <div
            class="text-center text-[14px] text-[#FFFFFF] box-border bg-[linear-gradient(_128deg,_#71B5FF_0%,_#2676E4_100%)] rounded-[8px]  h-[34px] center"
            @click.stop="copyValue"
          >
            点击复制
          </div>
        </div>
      </div>
    </div>
  </theme-layout-card>
</template>

<script>
import ThemeLayoutCard from "@/pages/sub/im/components/Cards/ThemeLayoutCard/index.vue";
import cardProps, { customComputed, messageAndConversationInfoComputed } from "@/pages/sub/im/mixins/card-props";
import { conversationInfoStateProps } from "@/pages/sub/im/mixins/case-info-state";

export default {
  name: "ExchangeContactsCard",
  components: { ThemeLayoutCard },
  mixins: [cardProps, customComputed, conversationInfoStateProps, messageAndConversationInfoComputed],
  computed: {
    //   判断是不是交换手机号 type=0 电话 1 微信
    isExchangePhone() {
      return this.customExts.type === 0;
    },
    //   交换文案
    exchangeText() {
      return this.isExchangePhone ? "电话" : "微信";
    },
  },
  methods: {
    callPhone(){
      uni.makePhoneCall({
        phoneNumber: this.customExts.content
      });
    },
    copyValue(){
      uni.setClipboardData({
        data: this.customExts.content,
        success: () => {
          this.showSharePopup = false;

          uni.showToast({
            title: "复制成功",
            icon: "none",
          });
        },
        fail: (err) => {
          console.log(err);

          uni.showToast({
            title: "复制失败，请检查权限",
            icon: "none",
          });
        },
      });
    }
  }
};
</script>

<style scoped lang="scss">

</style>