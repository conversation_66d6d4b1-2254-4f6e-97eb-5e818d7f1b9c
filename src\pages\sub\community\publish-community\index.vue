<template>
  <div class="pb-[80px]">
    <div class="bg-[#FFFCF8] text-[14px] text-[#FA700D] p-[12px]">
      请确保发布信息的真实性，若涉及虚假信息及违法信息，账号将会被封禁
    </div>
    <div class="bg-white form-top rounded-[12px] px-[16px] mt-[12px] mx-[16px] pb-[8px]">
      <collaboration-label
        position="bottom"
        label="群二维码"
      >
        <div class="flex mt-[4px]">
          <sub-upload
            v-if="!data.groupQrCode"
            isLoading
            type="image"
            :fileSizeLimit="500"
            @uploadSuccess="uploadSuccess"
          >
            <div>
              <img
                class="w-[88px] h-[88px]"
                src="@/pages/sub/community/imgs/<EMAIL>"
                alt=""
              >
            </div>
          </sub-upload>
          <div
            v-else
            class="relative w-[88px] h-[88px] rounded-[8px] overflow-hidden"
          >
            <img
              class="w-[88px] h-[88px]"
              :src="data.groupQrCode"
              alt=""
              @click.stop="previewImage"
            >
            <img
              class="absolute right-0 top-0 w-[20px] h-[20px]"
              src="@/pages/sub/community/imgs/<EMAIL>"
              alt=""
              @click.stop="cancelUpload"
            >
          </div>
        </div>
      </collaboration-label>
      <collaboration-label label="群名称">
        <u--input
          v-model="data.groupName"
          border="none"
          clearable
          fontSize="28rpx"
          inputAlign="right"
          maxlength="10"
          placeholder="请输入"
          type="text"
        />
      </collaboration-label>
      <collaboration-label label="当前群人数">
        <u--input
          v-model="data.currentMemberCount"
          border="none"
          clearable
          fontSize="28rpx"
          inputAlign="right"
          maxlength="3"
          placeholder="请输入"
          type="number"
        />
      </collaboration-label>
      <collaboration-label
        label="过期时间"
        :required="false"
      >
        <p class="text-[14px] py-[12px] text-right text-[#999999]">
          以二维码过期时间为准<br>过期需要重新上传
        </p>
      </collaboration-label>
      <collaboration-label label="群地区">
        <app-area
          :space="16"
          :hasDefault="hasDefault"
          :defaultValue="cityDefaultValue"
          :value="data.workCityInfo.workCity"
          @confirm="areaChange"
        />
      </collaboration-label>
      <collaboration-label
        :border="false"
        label="群类型"
      >
        <popup-select-type
          v-model="data.groupTypeList"
          title="选择群类型"
          multiple
          :max="3"
          :list="groupTypeList"
          :space="16"
        />
      </collaboration-label>
    </div>
    <div class="bg-white rounded-[12px] mt-[12px] mx-[16px] px-[12px] pb-[12px]">
      <p class="text-[14px] text-[#666666] pt-[12px] pb-[8px]">
        群公告
      </p>
      <div class=" bg-[#F5F5F7] rounded-[4px] overflow-hidden relative p-[8px] pb-[37px]">
        <textarea
          v-model="data.groupAnnouncement"
          maxlength="50"
          class="bg-[#F5F5F7] text-[14px] h-[120px]"
          placeholder="请输入"
        />
        <p class="text-[12px] text-[#999999] absolute right-[8px] bottom-[8px]">
          {{ data.groupAnnouncement.length }}/50
        </p>
      </div>
    </div>
    <div class="fixed bottom-[20px] left-0 right-0 z-[99]">
      <div
        class="mx-[24px] bg-[#3887F5] rounded-[20px] font-bold text-[15px] text-[#FFFFFF] center h-[40px]"
        @click.stop="handleSubmit"
      >
        提交审核
      </div>
    </div>
  </div>
</template>

<script>
import PopupSelectType from "@/pages/sub/collaboration-details/components/popupSelectType.vue";
import CollaborationLabel from "@/pages/sub/collaboration-details/publishCollaboration/CollaborationLabel.vue";
import AppArea from "@/components/AppComponents/AppArea/index.vue";
import { dataDetailList } from "@/api";
import { isNull, isString } from "@/libs/basics-tools";
import SubUpload from "@/pages/sub/components/SubUpload/index.vue";
import { appValidator } from "@/libs/validator";
import { communityGroupDetail, communityGroupPublishGroup, communityGroupUpdateGroup } from "@/api/communityGroup";
import { toPublishCompanionGroupRecord } from "@/libs/turnPages";

export default {
  name: "PublishCommunity",
  components: { SubUpload, AppArea, CollaborationLabel, PopupSelectType },
  data() {
    return {
      /* 是否是修改 */
      isEdit: false,
      cityDefaultValue: {},
      hasDefault: false,
      /* 群类型 */
      groupTypeList: [],
      data: {
        // 群二维码
        groupQrCode: "",
        // 群名称
        groupName: "",
        // 群地区
        workCityInfo: {
          cityName: undefined,
          workCity: undefined,
        },
        // 群类型
        groupTypeList: [],
        // 群人数
        currentMemberCount: "", 
        // 群公告
        groupAnnouncement: "",
      },
      rules: {
        groupQrCode: [
          { required: true, message: "请上传群二维码" },
        ],
        groupName: [
          { required: true, message: "请输入群名称" },
        ],
        currentMemberCount: [
          { required: true, message: "请输入群人数" },
          /* 只能输入整数 */
          {
            validator: (rule, value, callback) => {
              if (!/^[0-9]+$/.test(value)) {
                callback(new Error("群人数必须是整数"));
              } else {
                callback();
              }
            },
          },
          /* 数量限制1-999 */
          {
            validator: (rule, value, callback) => {
              if (value < 1 || value > 999) {
                callback(new Error("群人数必须在1-999之间"));
              } else {
                callback();
              }
            },
          },
        ],
        /* 城市*/
        workCityInfo: [
          {
            type: "object",
            required: true,
            fields: {
              workCity: [
                { type: "number", required: true, message: "请选择城市" },
              ],
            },
          },
        ],
        groupTypeList: [
          { required: true, message: "请选择群类型" },
        ]
      }
    };
  },

  onLoad({ id }) {
    if (id) {
      this.getDetail(id);
      this.isEdit = true;
    }else{
      this.isEdit = false;
    }

    /* 获取查档类型列表*/
    dataDetailList({
      groupCode: "COMMUNITY_GROUP_TYPES",
    }).then(({ data = [] }) => {
      this.groupTypeList = data;
    });
  },
  methods: {
    /** 地区选择 */
    areaChange(data) {
      this.data.workCityInfo = data;
      console.log(data);
    },
    /** 如果在携带 id 到该界面的情况下，则获取详情，然后填入 */
    getDetail(id) {
      communityGroupDetail({ groupId: id }).then(({ data }) => {
        this.handleHistorySubmitClick(data);
      });
    },
    /* 图片上传成功 */
    uploadSuccess(data){
      this.data.groupQrCode = data;
    },
    /* 取消上传 */
    cancelUpload(){
      this.data.groupQrCode = "";
    },
    /* 预览图片 */
    previewImage(){
      uni.previewImage({
        urls: [this.data.groupQrCode],
      });
    },
    handleHistorySubmitClick(data) {
      const deepData = { ...data };
      const needData = {};
      /* 统一一下类型 this.data里面声明的什么类型 这里就转换成什么类型 */
      Object.keys(this.data).forEach((key) => {
        const dataItem = this.data[key];
        const deepDataItem = deepData[key];
        if (!isNull(deepDataItem) && isString(dataItem)) {
          needData[key] = String(deepDataItem);
        } else {
          needData[key] = deepDataItem;
        }
      });
      this.data = {
        ...this.data,
        ...needData,
        groupId: deepData.id,
      };
      try{
        this.data.groupTypeList = JSON.parse(deepData.groupTypes || "[]");

      }catch(e){
        console.log(e);
      }
      console.log(this.data, "this.data");

      this.hasDefault = true;
      this.cityDefaultValue = {
        provinceCode: data.provinceCode,
        provinceName: data.provinceName,
        cityCode: data.regionCode,
        cityName: data.regionName,
      };
      this.data.workCityInfo = {
        workCity: data.regionCode,
        cityName: data.regionName,
      };
    },
    handleSubmit(){
      /* 去除空格*/
      this.data = {
        ...this.data,
        groupName: this.data.groupName.trim(),
        groupAnnouncement: this.data.groupAnnouncement.trim(),
      };
      appValidator(this.data, this.rules).then(() => {
        (this.isEdit ? communityGroupUpdateGroup : communityGroupPublishGroup)({
          ...this.data,
          regionCode: this.data.workCityInfo.workCity,
          workCityInfo: undefined
        }).then(() => {
          uni.showToast({
            title: "发布成功",
            icon: "success",
            duration: 2000,
          });
          toPublishCompanionGroupRecord();
        });
      });
    }
  },
};
</script>

<style scoped lang="scss">
.form-top{
  ::v-deep .CollaborationLabelItem{
    height:  auto;
    min-height: 44px;
  }
}
</style>