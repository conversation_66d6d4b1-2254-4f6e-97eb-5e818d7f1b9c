<template>
  <div class="confirm-order-container">
    <div class="wrap">
      <!-- 商品内容 -->
      <div class="product flex">
        <div class="product-img">
          <!-- <img :src="detail.icon || require('@/assets/imgs/pay/icon.png')" alt="图片" class="img"/> -->
          <img
            :src="detail.icon"
            alt="图片"
            class="img"
          >
        </div>
        <div class="product-infos">
          <div class="product-infos-title">
            <p class="p">
              {{ detail.name || '' }}
            </p>
            <p class="p">
              {{ detail.grabbingNum }}次/{{ detail.validity }}天
            </p>
          </div>
          <div class="product-infos-price flex flex-align-center">
            ¥
            <span class="price">{{ priceNumber(detail.price) }}</span>
            <span class="origin-price">原价 ¥{{ priceNumber(detail.originPrice) }}</span>
          </div>
        </div>
      </div>
      <!-- 价格 -->
      <div class="prices">
        <div class="line flex flex-space-between">
          <span>订单价格</span>
          <i>¥ {{ priceNumber(detail.price) }}</i>
        </div>

        <div class="line flex coupon-container flex-space-between">
          <span>现金券</span>
          <div>
            <span
              v-if="!moneyCouponList.length"
              class="members-price-tip"
            >暂无可用现金券</span>
            <p
              v-else
              class="coupon"
              @click="showCoupon"
            >
              -¥{{ priceNumber(couponInfo.spec) }}
              <img
                alt=""
                class="coupon-img"
                src="../../img/cluePackConfirmOrder/arrow-right.png"
              >
            </p>
          </div>
        </div>
        <div class="line flex flex-space-between">
          <span>合计</span>
          <i>¥ {{ priceNumber(payMoney) }}</i>
        </div>
      </div>
    </div>
    <div class="handle-bottom flex-space-between flex-align-center">
      <div
        class="bt"
        @click="pay"
      >
        立即支付 ¥{{ priceNumber(payMoney) }}
      </div>
      <div class="protocol flex">
        <img
          v-if="isAgree"
          alt
          class="icon"
          src="@/pages/pay/img/cluePackConfirmOrder/xiyichecked.png"
          @click="selectService(false)"
        >
        <img
          v-else
          alt
          class="icon"
          src="@/pages/pay/img/cluePackConfirmOrder/zx_nocheck.png"
          @click="selectService(true)"
        >
        <label class="label flex-1">
          支付即代表同意
          <span
            class="a-tag"
            @click="clickXy1"
          >《平台服务购买协议》</span>
          <span
            class="a-tag"
            @click="clickXy2"
          >《律师服务公约》</span>
        </label>
      </div>
    </div>
    <coupon-popup
      :couponInfoProp="couponInfo"
      :list="moneyCouponList"
      :show.sync="show"
      @selectedCoupon="selectedCoupon"
    />
  </div>
</template>
<script>
import { protocols } from "@/libs/config";
import { getCaseSourceGoodsDetail } from "@/api/recharge";
import { isArrNull, isObjNull } from "@/libs/basics-tools";
import { getVipMoneyCoupon } from "@/api/changlvmember";
import { priceNumber } from "@/libs/tool";
import { handleOrderPay } from "@/libs/pay";
import CouponPopup from "@/components/couponPopup/index.vue";
import { turnToWebViewPage } from "@/libs/turnPages";
import { WebAddress } from "@/enum";

export default {
  name: "CluePackConfirmOrder",
  components: {
    CouponPopup
  },
  data () {
    return {
      routerParams: {}, // 路由参数
      show: false,
      payWay: null, // 默认选择第一个
      originMoney: 0, // 原价
      protocols,
      /** 协议同意 */
      isAgree: false,
      /** 详情数据 */
      detail: {},
      /* 优惠卷 */
      moneyCouponList: [],
      /* 优惠卷信息 */
      couponInfo: {
        id: null,
        spec: 0
      }
    };
  },
  computed: {
    // 订单id
    orderId(){
      return this.routerParams.orderId;
    },
    // 商品id
    vipId(){
      return Number(this.routerParams.goodsId);
    },
    // 付钱价格
    payMoney () {
      return this.detail.price - this.couponInfo.spec;
    }
  },
  onLoad(query) {
    this.routerParams = query;
    this.getVipDetail();
    this.getCouponRequest();
  },

  mounted () {
  },

  methods: {
    priceNumber,
    isArrNull,
    isObjNull,
    getVipDetail () {
      getCaseSourceGoodsDetail(this.vipId).then(res => {
        if (!res.code) {
          this.detail = res.data;
        }
      });
    },
    // 获取优惠券
    getCouponRequest(){
      getVipMoneyCoupon({
        caseSourceGoodsId: this.vipId
      }).then(({ data = [] }) => {
        this.moneyCouponList = data;
        if (!isArrNull(data)) {
          const info = data[0];
          if (info) this.couponInfo = info;
        }
      });
    },
    showCoupon(){
      this.show = true;
    },
    selectService (val) {
      this.isAgree = val;
    },
    pay () {
      if (!this.isAgree) {
        uni.showToast({
          title: "请同意平台服务购买协议",
          icon: "none",
          duration: 2000,
        });
        return false;
      }
      handleOrderPay({
        type: 6,
        serviceId: this.vipId,
        couponLawyerRecordId: this.couponInfo.id,
      });

    },
    selectedCoupon (data) {
      this.couponInfo = data;
    },
    clickXy1 () {
      turnToWebViewPage({ src: WebAddress.lawyer_plat_buy_case });
    },
    clickXy2 () {
      turnToWebViewPage({ src: WebAddress.lawyer_service_convention });
    }
  }
};
</script>

<style lang="scss" scoped>
.product {
  background: #ffffff;
  padding: 16px;
  font-size: 14px;
  margin: 0 16px 12px;
  border-radius: 8px 8px 8px 8px;

  &-img {
    width: 80px;
    height: 80px;
    // background: url('../../assets/imgs/pay/Frame1570.png') no-repeat;
    // background-size: 100% 100%;
    margin-right: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    .img{
      width: 40px;
      height: 40px;

    }
  }

  &-infos {
    &-title {
      .p {
        &:first-child {
          font-size: 16px;
          font-weight: 600;
          color: #333333;
          height: 22px;
        }
        &:last-child {
          font-size: 12px;
          font-weight: 400;
          color: #666666;
          margin-top: 4px;
        }
      }
    }
    &-price {
      font-size: 13px;
      font-weight: 400;
      color: #333333;
      margin-top: 15px;

      .price {
        font-style: normal;
        font-size: 18px;
        font-weight: 600;
        color: #333333;
      }

      .origin-price {
        font-size: 12px;
        font-weight: 400;
        color: #999999;
        text-decoration: line-through;
        vertical-align: top;
        margin-left: 4px;
      }
    }
  }
}
.prices {
  padding: 0 16px;
  font-size: 14px;
  margin: 0 16px 12px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  color: #333333;
  > .line {
    line-height: 44px;

    i {
      font-style: normal;
      font-weight: 500;
    }
  }
  .members-price-tip{
    font-size: 14px;
    font-weight: 400;
    color: #CCCCCC;
  }
  .coupon{
    font-size: 14px;
    font-weight: bold;
    color: #F34747;
    display: flex;
    align-items: center;
    &-img{
      margin-right: -4px;
      width: 16px;
      height: 16px;
    }
  }
  .coupon-container{
    box-sizing: border-box;
    border-bottom: 0.5px solid #EEEEEE;
  }
}

.confirm-order-container {
  height: 100vh;
  // overflow-y: auto;
  // height: 100%;
  box-sizing: border-box;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}
.other {
  color: #333;
  line-height: 18px;
  padding: 0 16px;
  margin-top: 16px;
}

.wrap {
  width: 100%;
  box-sizing: border-box;
  padding-top: 16px;
  flex: 1;
  // z-index: 2;

  .title {
    font-weight: 600;
    color: #000000;
    line-height: 19px;
    margin: 16px 0 12px 16px;

  }
}
.payway-box {
  width: 343px;
  background: #fff;
  margin-left: 16px;
  border-radius: 8px;
  padding: 0 16px;
  box-sizing: border-box;
  .payway {
    height: 44px;
    font-size: 14px;
    color: #46474b;
    line-height: 16px;
    .content{
      .wxpayicon {
        width: 18px;
        height: 18px;
        margin-right: 5px;
        // margin-bottom: 3px;
      }
      .title{
        color: #333333;
      }
    }

    .payselect {
      width: 16px;
      height: 16px;
    }
  }
}
.handle-bottom {
  width: 375px;
  position: fixed;
  bottom: 0px;
  bottom: constant(safe-area-inset-bottom, 0);
  bottom: env(safe-area-inset-bottom, 0);
  padding: 0 16px;
  box-sizing: border-box;
  // height: 60px;

  .bt {
    width: 342px;
    height: 44px;
    background: linear-gradient(270deg, #f34747 0%, #ff984b 100%);
    border-radius: 22px 22px 22px 22px;
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    line-height: 44px;
    text-align: center;
    // margin-left: 16px;
  }

  .protocol {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    margin-top: 24px;
    margin-bottom: 16px;

    .icon {
      width: 16px;
      height: 16px;
      vertical-align: middle;
      position: relative;
      top: -1px;
      margin-right: 8px;
      margin-top: 3px;
    }
.label{
    .a-tag {
      color: #3887F5;
    }
}
  }
}
</style>
