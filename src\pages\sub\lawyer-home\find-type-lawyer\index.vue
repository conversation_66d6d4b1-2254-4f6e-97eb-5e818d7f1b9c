<template>
  <div>
    <div
      class="header pd-lt-16 pd-rt-16 flex flex-space-between flex-align-center"
    >
      <div class="title">
        <span
          v-if="typeInfo.typeName"
          class="type-name"
        >{{
          typeInfo.typeName
        }}</span>
        精选律师
      </div>
      <div
        class="to-all flex flex-align-center"
        @click="changePopupState('goodAtTypeState', true)"
      >
        选择其他类型
        <img
          alt=""
          class="arrow"
        >
      </div>
    </div>
    <div
      v-if="lawyerList.length === 0 && reqState"
      class="placeholder"
    >
      <img
        alt=""
        class="placeholder-image"
        src="@/pages/findlawyer/imgs/no-lawyer-new.png"
      >
    </div>
    <div class="lawyer-card">
      <div
        v-for="i in lawyerList"
        :key="i.id"
        class="lawyer-card-item"
      >
        <findlawyer-item-plus :data="i" />
      </div>
    </div>
    <u-popup
      :duration="0"
      :safeAreaInsetBottom="false"
      :show="popupState.goodAtTypeState"
      bgColor="transparent"
      mode="bottom"
      @close="changePopupState('goodAtTypeState', false)"
    >
      <div class="goodat-box">
        <div class="select-button flex flex-space-between flex-align-center">
          <div class="select-button-placeholder" />
          <div class="button-title">
            全部类型
          </div>
          <div>
            <div
              class="button-cancel"
              @click="changePopupState('goodAtTypeState', false)"
            >
              <img
                alt=""
                class="button-cancel-icon"
                src="@/pages/submit-question/imgs/cancellable.svg"
              >
            </div>
          </div>
        </div>
        <div class="lawyer-type-box">
          <LawyerTypes
            :list="goodAtType"
            @getCaseType="getCaseTypeFun"
          />
          <u-safe-bottom />
        </div>
      </div>
    </u-popup>
  </div>
</template>

<script>
import { dataDictionary } from "@/api";
import { lawyerListV3 } from "@/api/findlawyer.js";
import FindlawyerItemPlus from "@/pages/findlawyer/components/findlawyer-item-plus/index.vue";
import LawyerTypes from "@/pages/findlawyer/components/find-lawyer-types/index.vue";
import UPopup from "@/uview-ui/components/u-popup/u-popup.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "Index",
  components: { USafeBottom, UPopup, LawyerTypes, FindlawyerItemPlus },
  data() {
    return {
      popupState: {
        /* 定位 弹窗*/
        locationState: false,
      },
      goodAtType: [],
      typeInfo: {
        typeValue: "",
        typeName: "",
      },

      pageParams: {
        /** 页码 */
        currentPage: 1,
        /** 每页条数 */
        pageSize: 20,
      },
      /* 律师列表*/
      lawyerList: [],
      /* 请求状态*/
      reqState: false,
    };
  },
  onLoad(options) {
    console.log("options:", options);
    this.lawyerList = [];
    this.typeInfo.typeValue = options.typeValue || "";
    dataDictionary({ groupCode: "LAWYER_SPECIALITY" }).then(({ data }) => {
      this.goodAtType = data;
      const item = this.goodAtType.find(
        (item) => item.value === this.typeInfo.typeValue
      );
      this.typeInfo.typeName = item ? item.label : "";
      this.reqList();
    });
  },
  onReachBottom() {
    this.scrollToLower();
  },
  methods: {
    // 底部案件类型选择事件
    getCaseTypeFun(item) {
      this.handleGoodAtType(item);
    },
    changePopupState(key, state = true) {
      this.popupState = this.$options.data().popupState;
      this.popupState[key] = state;
    },
    /* 擅长类型选择*/
    handleGoodAtType({ value, label }) {
      this.typeInfo.typeValue = value;
      this.typeInfo.typeName = label;
      this.popupState.goodAtTypeState = false;
      this.pageParams = this.$options.data().pageParams;
      this.lawyerList = [];
      this.reqList();
    },
    scrollToLower() {
      if (
        this.pageParams.currentPage * this.pageParams.pageSize >=
        this.pageTotal
      )
        return;

      this.pageParams.currentPage++;
      this.reqList();
    },
    reqList() {
      this.reqState = false;
      lawyerListV3({
        ...this.pageParams,
        specialityCode: this.typeInfo.typeValue,
        speciality: this.typeInfo.typeName,
        sort: 6,
      })
        .then((res) => {
          this.lawyerList = [...this.lawyerList, ...(res.data.records || [])];
          this.pageTotal = res.data.total || 0;
        })
        .finally(() => {
          this.reqState = true;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  padding-top: 8px;
  padding-bottom: 16px;
  .title {
    font-size: 16px;
    font-weight: 400;
    color: #222222;
    .type-name {
      color: #3887f5;
      padding-right: 4px;
    }
  }
  .to-all {
    font-size: 14px;
    font-weight: 400;
    color: #999999;
    .arrow {
      width: 16px;
      height: 16px;
    }
  }
}
.placeholder {
  min-height: 100vh;
  padding-top: 40px;
  display: flex;
  justify-content: center;

  .placeholder-image {
    width: 255px;
    height: 256px;
  }
}

.lawyer-card {
  padding-left: 12px;
  padding-right: 12px;
  padding-bottom: 20px;

  &-item {
    border-radius: 8px;
    overflow: hidden;

    &:not(:last-child) {
      margin-bottom: 12px;
    }
  }
}

.goodat-box {
  background: #f5f5f7;
  border-radius: 16px 16px 0 0;

  .select-button {
    height: 48px;
    padding: 0 16px;
    border-bottom: 0.5px solid #eeeeee;

    &-placeholder {
      width: 24px;
    }

    .button {
      &-cancel {
        font-size: 14px;
        font-weight: 400;
        color: #999999;

        &-icon {
          display: block;
          width: 24px;
          height: 24px;
        }
      }

      &-title {
        font-size: 16px;
        font-weight: bold;
        color: #000000;
      }

      &-define {
        font-size: 14px;
        font-weight: 400;
        color: $theme-color;
      }
    }
  }
}
</style>
