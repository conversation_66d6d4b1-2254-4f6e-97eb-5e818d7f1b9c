<template>
  <div>
    <app-popup
      :show="show"
      mode="bottom"
      round="16"
      @close="close"
    >
      <div class="h-[48px] flex items-center mx-[16px] border-0 border-b border-solid border-[#EEEEEE]">
        <p
          class="text-[14px] text-[#999999] flex-shrink-0"
          @click="close"
        >
          取消
        </p>
        <div class="font-bold flex-1 text-center text-[16px] text-[#000000]">
          {{ title }}
        </div>
        <p
          class="text-[14px] text-[#3887F5] flex-shrink-0"
          @click="confirm"
        >
          确定
        </p>
      </div>
      <div class=" flex flex-wrap  px-[19px]">
        <p
          v-for="i in list"
          :key="i.value"
          :class="[{active:selectData.includes(i.value)}]"
          class="py-[10px] my-[10px] item mr-[24px] text-ellipsis w-[96px] text-center bg-[#F5F5F7] rounded-[20px] text-[13px] text-[#333333]"
          @click="handleChange(i)"
        >
          {{ i.label }}
        </p>
      </div>
    </app-popup>
    <div @click="show = true">
      <slot :text="text">
        <app-field
          :value="text"
          align="left"
          arrow
          :space="space"
          color="#333333"
          placeholder="请选择"
          placeholderColor="#CCCCCC"
        />
      </slot>
    </div>
  </div>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import AppField from "@/components/AppComponents/AppField/index.vue";
import { isNull } from "@/libs/basics-tools";

export default {
  name: "PopupSelectType",
  components: { AppField, AppPopup },
  props: {
    title: {
      type: String,
      default: "请选择"
    },
    value: {
      type: [String, Number, Array],
      default: ""
    },
    list: {
      type: Array,
      default: () => []
    },
    space: {
      type: Number,
      default: 0,
    },
    /* 是否多选 */
    multiple: {
      type: Boolean,
      default: false,
    },
    /* 最大选择数量 */
    max: {
      type: Number,
      default: 0,
    }
  },
  data() {
    return {
      show: false,
      /* 选中的数据 */
      selectData: [],
    };
  },
  computed: {
    /** 当前选中的数据  */
    currentData(){
      if(isNull(this.value))return [];
      const value = this.multiple ? this.value : [this.value];
      return this.list.filter(i => value.includes(i.value));
    },
    /** 如果有值，则显示 */
    text() {
      return  this.currentData.map(i => i.label).join(",");
    },
  },
  watch: {
    value: {
      handler(val){
        if(!val)return;
        if(this.multiple){
          this.selectData = [...(val || [])];
        }else{
          this.selectData = [val];
        }
      },
      immediate: true
    },

  },
  methods: {
    close(){
      this.show = false;
    },
    confirm(){
      const getSelectData = this.getSelectData();
      const currentData = this.multiple ? getSelectData : getSelectData[0] || {};
      const inputValue = currentData.map(i => i.value || "");
      this.$emit("input", inputValue);
      this.$emit("handleChange", currentData);
      this.close();
    },
    /* 点击选择 */
    handleChange(data){
      const { value } = data;
      if(this.multiple){
        /* 如果时已经选中的就删除 */
        let index = this.selectData.findIndex(i => i === data.value);
        if(index !== -1){
          this.selectData.splice(index, 1);
          return;
        }
        /* 最大限制 */
        if(this.max >= 1 && this.selectData.length >= this.max)return;
        this.selectData.push(value);
      }else{
        this.selectData = [value];
      }
    },
    /* 获取选中的数据完整信息 */
    getSelectData(){
      return this.selectData.map(i => this.list.find(j => j.value === i));
    },

  }
};
</script>

<style scoped lang="scss">
.item{
  &:nth-of-type(3n){
    margin-right: 0;
  }
}
.active{
  background: #EBF3FE;
  color: #3887F5;
}
</style>