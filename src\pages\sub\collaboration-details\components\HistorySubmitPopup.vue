<template>
  <app-popup
    :show="show"
    :round="16"
    closeable
    mode="bottom"
    bgColor="transparent"
    :safeAreaInsetBottom="false"
    @cancel="handleClose"
  >
    <div class="flex items-center justify-between p-[16px] bg-[#EBF3FE] rounded-t-[16px]">
      <div class="font-bold text-[18px] text-[#333333]">
        历史发布
      </div>
      <div class="text-[13px] text-[#999999] ml-[8px]">
        近5条发布历史
      </div>
    </div>
    <div class="w-[375px] bg-[linear-gradient(_180deg,_#EBF3FE_0%,_#EEF5FE_79%,_#FFFFFF_95%)]">
      <scroll-view
        scrollY
        class="max-h-[412px]"
      >
        <div
          v-for="i in list"
          :key="i.id"
          class="mt-[12px] first:mt-0"
        >
          <history-submit-card-item
            :data="i"
            @click="handleClick"
          />
        </div>
      </scroll-view>
      <u-safe-bottom />
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import HistorySubmitCardItem from "@/pages/sub/collaboration-details/components/HistorySubmitCardItem.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { lawyerCollaborationMyPublishPage } from "@/api/collaboration";

export default {
  name: "HistorySubmitPopup",
  components: { AppPopup, HistorySubmitCardItem, USafeBottom },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      list: []
    };
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  watch: {
    show: {
      immediate: true,
      handler(val){
        if(val){
          lawyerCollaborationMyPublishPage({
            currentPage: 1,
            pageSize: 5
          }).then(({ data = {} }) => {
            const { records = [] } = data;
            this.list = records;
          });
        }
      }
    }
  },
  methods: {
    handleClick(data) {
      this.$emit("handleClick", data);
      this.handleClose();
    },
    handleClose() {
      this.show = false;
    },
  }
};
</script>
