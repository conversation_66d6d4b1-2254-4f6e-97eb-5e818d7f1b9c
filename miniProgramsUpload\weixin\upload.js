/**
 * 微信小程序自动上传脚本
 */
const getMiniConfig = require("./config");
const ci = require("miniprogram-ci");


/**
 * 上传文件到微信小程序
 * @param appid
 * @param projectPath
 * @param privateKeyPath
 * @param isDev
 * @param version
 * @return {Promise<void>}
 */
async function wxUploadFile({ appid, projectPath, privateKeyPath, isDev, version }) {

  const project = new ci.Project({
    appid,
    type: "miniProgram",
    projectPath,
    privateKeyPath,
  });

  const params = {
    project,
    version,
    desc: isDev ? `${version} - 测试版请不要提审` : `${version} - 版本更新`,
    // 1 号机器人上传保证 上传人唯一
    robot: 1,
    setting: {
      es6: true,
      minifyJS: true,
      minify: true,
      minifyWXML: true,
      minifyWXSS: true,
    },
    onProgressUpdate: console.log,
  };

  const uploadResult = await ci.upload(params);

  console.log(uploadResult);
}

module.exports = ({ PROJECT_PATH, FILE_NAME, isDev, version }) => {
  wxUploadFile({
    ...getMiniConfig[FILE_NAME],
    projectPath: PROJECT_PATH,
    isDev,
    version
  });
};
