<template>
  <div
    :class="{ 'box-container': !value }"
    class="lawyer-more flex flex-align-center flex-space-between"
    @click="handleClick"
  >
    <div>律师档案</div>
    <div class="flex flex-align-center more">
      {{ clickText }}
      <u-icon
        v-if="value"
        name="arrow-up"
      />
      <u-icon
        v-else
        name="arrow-down"
      />
    </div>
  </div>
</template>

<script>
import UIcon from "@/uview-ui/components/u-icon/u-icon.vue";

export default {
  name: "LawyerHomeMore",
  components: { UIcon },
  props: {
    /** 是否展开 */
    value: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    /** 点击文案 */
    clickText() {
      return this.value ? "收起更多" : "查看更多";
    },
  },
  methods: {
    /** 点击事件 */
    handleClick() {
      this.$emit("input", !this.value);
    },
  },
};
</script>

<style lang="scss" scoped>
.lawyer-more {
  height: 46px;
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  line-height: 46px;
  padding: 0 16px;
}

.more {
  font-size: 13px;
  font-weight: 400;
  color: #333333;
}

.box-container {
  width: 343px;
  margin: 0 auto 12px auto;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
  padding: 16px;
}
</style>
