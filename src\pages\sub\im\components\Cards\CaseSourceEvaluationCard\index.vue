<template>
  <div class="case-source-evaluation-card">
    <div class="title text-center">
      请对本次案源的准确性做出评价
    </div>
    <div class="fraction text-center">
      <img
        v-for="i in maxGrade"
        :key="i"
        class="fractionIcon"
        :src="i<currentGrade?fractionIcon:unFractionIcon"
        alt=""
        @click="changeMaxGrade(i+1)"
      >
    </div>
    <div class="desc text-center">
      {{ gradeDesc[currentGrade] }}
    </div>
    <div
      v-if="!isCaseSourceEvaluateScore&&currentGrade!==0&&currentGrade<3"
      class="bad-reviews-container"
    >
      <div class="bad-reviews-type flex flex-wrap">
        <div
          v-for="i in badReviewsType"
          :key="i.value"
          class="type"
          :class="{active:badReviews.indexOf(i.value)>-1}"
          @click="handleBadReview(i.value)"
        >
          {{ i.label }}
        </div>
      </div>
      <div class="bad-reviews-content">
        <u--textarea
          v-model="info"
          :showConfirmBar="false"
          :customBoxStyle="{color:'#333333'}"
          :customStyle="textareaStyle"
          placeholder="请输入您的改进建议"
        />
      </div>
      <p
        v-if="errorTip"
        class="error-tip"
      >
        请选择或填写差评原因
      </p>
    </div>
    <div
      class="btn"
      :class="{disable:isCaseSourceEvaluateScore}"
      @click.stop="submitAReview"
    >
      {{ isCaseSourceEvaluateScore?'已提交':'提交评价' }}
    </div>
  </div>
</template>

<script>
import UTextarea from "@/uview-ui/components/u-textarea/u-textarea.vue";
import { dataDetailList } from "@/api";
import { isArrNull, isNull } from "@/libs/basics-tools.js";
import { caseSourceServerV2SaveScoreNew } from "@/api/im.js";
import { caseInfoStateProps, updateCaseInfoSymbol } from "@/pages/sub/im/mixins/case-info-state.js";

export default {
  name: "CaseSourceEvaluationCard",
  mixins: [caseInfoStateProps],
  inject: [updateCaseInfoSymbol],
  components: {
    "u--textarea": UTextarea,
  },
  data() {
    return {
      /* 最大评分数*/
      maxGrade: 5,
      /* 当前分数*/
      currentGrade: 0,
      fractionIcon: require("@/pages/sub/im/imgs/fraction.png"),
      unFractionIcon: require("@/pages/sub/im/imgs/unfraction.png"),
      info: "",
      textareaStyle: {
        border: "none",
        backgroundColor: "#F5F5F5"
      },
      /* 评分对应的文案*/
      gradeDesc: ["您的评价是我们进步的动力", "案源信息非常不准确", "案源信息大致准确，仍有不足", "案源信息大致准确", "案源信息基本上都很准确", "各项信息都非常准确"],
      /* 低分级 原因类型*/
      badReviewsType: [],
      /* 低分选择的原因*/
      badReviews: [],
      errorTip: false
    };
  },
  computed: {
    /* 律师是否评价过*/
    isCaseSourceEvaluateScore() {
      return !isNull(this.caseInfo.caseSourceEvaluateScore);
    }
  },
  methods: {
    getBadReviewsType(){
      dataDetailList({ groupCode: "CASE_SOURCE_EVALUATE_LABEL_TYPE" }).then(({ data = [] }) => {
        this.badReviewsType = data;
      });
    },
    changeMaxGrade(grade) {
      if(this.isCaseSourceEvaluateScore) return;
      this.currentGrade = grade;
      if(this.currentGrade > 2){
        this.badReviews = [];
        this.info = "";
      }
    },
    /* 提交*/
    submitAReview() {
      if(this.isCaseSourceEvaluateScore) return;
      if (this.currentGrade === 0) {
        this.$toast("非常抱歉，至少一颗星哦");
        return;
      }
      /* 低评分 分级小于3并且低评分类型或描述没有填写*/
      if (this.currentGrade < 3 && (isArrNull(this.badReviews) && isNull(this.info))) {
        this.errorTip = true;
        return;
      }
      this.errorTip = false;
      caseSourceServerV2SaveScoreNew({
        caseSourceServerV2Id: this.caseSourceServerV2Id,
        score: this.currentGrade,
        caseSourceV2EvaluateDesc: this.info,
        caseSourceV2EvaluateIds: this.badReviews
      }).then(() => {
        this.$toast("评价成功");
        this.currentGrade = 0;

        this[updateCaseInfoSymbol]();
      });
    },
    /* 低分类型选择*/
    handleBadReview(value) {
      /* 有重复的  取消选中的*/
      if (this.badReviews.indexOf(value) > -1) {
        this.badReviews.splice(this.badReviews.indexOf(value), 1);
      } else {
        this.badReviews.push(value);
      }
    }
  },
  watch: {
    isCaseSourceEvaluateScore: {
      immediate: true,
      handler(val) {
        if (!val) {
          this.getBadReviewsType();
        }
        this.currentGrade = isNull(this.caseInfo.caseSourceEvaluateScore) ? 0 : this.caseInfo.caseSourceEvaluateScore;
      }
    }
  }
};
</script>

<style scoped lang="scss">
.case-source-evaluation-card {
  background: white;
  padding: 24px 10px 32px;
  border-radius: 8px 8px 8px 8px;

  .title {
    font-size: 16px;
    font-weight: 500;
    color: #222222;
  }

  .fraction {
    padding: 16px 0;
    margin: 0 auto;
    font-size: 0;

    .fractionIcon {
      width: 30px;
      height: 30px;
      margin-right: 8px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .desc {
    font-size: 14px;
    font-weight: 400;
    color: #F2AF30;
  }

  .btn {
    margin-top: 24px;
    text-align: center;
    line-height: 44px;
    background: #3887F5;
    border-radius: 22px 22px 22px 22px;
    font-size: 16px;
    font-weight: 400;
    color: #FFFFFF;
    &.disable {

      background: #A0CFFB;
    }
  }

  .bad-reviews-container {
    padding-top: 16px;

    .bad-reviews-type {
      justify-content: space-evenly;

      .type {
        margin-bottom: 12px;
        overflow: hidden;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        padding: 6px 12px;
        border-radius: 17px 17px 17px 17px;
        border: 1px solid #EEEEEE;

        &.active {
          color: #3887F5;
          border-color: #A0CFFB;
        }
      }
    }

    .error-tip {
      font-size: 12px;
      font-weight: 400;
      color: #EB4738;
      padding-top: 12px;
    }
  }
}
</style>
