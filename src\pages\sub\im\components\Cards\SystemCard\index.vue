<template>
  <div class="d-flex flex-align-center flex-space-center">
    <div class="system-card">
      {{ data.msg.title }}
    </div>
  </div>
</template>

<script>
import cardProps from "@/pages/sub/im/mixins/card-props.js";

export default {
  name: "SystemCard",
  mixins: [cardProps]
};
</script>

<style scoped lang="scss">

.system-card{
  background: #EEEEEE;
  border-radius: 4px 4px 4px 4px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 400;
  color: #666666;
}
</style>
