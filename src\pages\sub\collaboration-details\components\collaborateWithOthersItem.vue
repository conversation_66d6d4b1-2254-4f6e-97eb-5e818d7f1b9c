<template>
  <div
    class="p-[16px] bg-[#FFFFFF] rounded-[8px] flex"
    @click="toLawyerHome"
  >
    <img
      :src="data.fbLawyerAvatar"
      alt=""
      class="flex-shrink-0 w-[48px] h-[48px] rounded-[29px]"
    >
    <div class="flex-1 flex justify-between items-center pl-[8px]">
      <div>
        <p class="font-bold text-[16px] text-[#333333] flex items-center">
          {{ data.fbLawyerName }} <span class="text-[12px] font-normal text-[#999999] pl-[8px]">发单律师</span>
        </p>
        <div class="flex items-center pt-[8px]">
          <img
            alt=""
            class="w-[12px] h-[12px]"
            src="../imgs/icon.png"
          >
          <p class="pl-[2px] text-[12px] text-[#9F6310]">
            已通过法临官方认证
          </p>
        </div>
      </div>
      <div class="flex items-center text-[14px] text-[#999999]">
        主页 <i class="text-[16px] iconfont icon-erjiyoujiantou" />
      </div>
    </div>
  </div>
</template>

<script>
import { toLawyerHome } from "@/libs/turnPages";

export default {
  name: "CollaborateWithOthersItem",
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    toLawyerHome(){
      toLawyerHome({
        id: this.data.fbLawyerId
      });
    }
  },
};
</script>

<style lang="scss" scoped>

</style>
