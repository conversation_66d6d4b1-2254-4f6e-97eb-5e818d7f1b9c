<template>
  <div>
    <div
      v-for="(item,index) in props"
      :key="index"
      class="h-[36px] flex items-center justify-between"
    >
      <div class="text-[14px] text-[#666666]">
        {{ item.label }}
      </div>
      <div class="text-[14px] text-[#333333]">
        {{ dataSource[item.value] }}{{ item.suffix }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "DetailsInfo",
  props: {
    dataSource: {
      type: Object,
      default: () => ({}),
      required: true
    },
    props: {
      type: Array,
      default: () => [],
      required: true
    }
  }
};
</script>
