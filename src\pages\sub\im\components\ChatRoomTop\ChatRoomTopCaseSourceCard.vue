<template>
  <div class="case-source-card">
    <div class="top flex flex-space-between">
      <div class="flex">
        <div
          class="score"
          :style="'background-color:'+geCaseGradeConfig.bg"
        >
          {{ geCaseGradeConfig.label }}
        </div>

        <div class="type">
          {{ caseInfo.typeLabel }}
        </div>
        <div class="order-grab-type">
          {{ (IM_CONVERSATION_TAG_MAP[IM_CASE_SOURCE_TYPE.CASE_SOURCE])[caseInfo.serverWay] }}
        </div>
      </div>
      <div class="follow-up-status">
        {{ FOLLOW_STATUS[caseInfo.status]&&FOLLOW_STATUS[caseInfo.status].text }}
      </div>
    </div>
    <div class="desc text-ellipsis-2">
      {{ caseInfo.info }}
    </div>
    <div class="case-source-type flex">
      <div class="type-item">
        {{ caseInfo.amountGradeStr }}
      </div>
    </div>
    <div class="date-info flex flex-space-between flex-align-center">
      <p class="date">
        {{ caseSourceV2Info.createTime }}
      </p>
      <p class="city flex-1">

        {{ caseInfo.regionName }}
      </p>
      <p
        class="to-detail flex flex-align-center"
        @click.stop="turnToCaseSourceDetails({id:caseSourceServerV2Id,type:3})"
      >
        案源详情 <i class="iconfont icon-erjiyoujiantou" />
      </p>
    </div>
  </div>
</template>

<script>
import { caseInfoStateProps } from "@/pages/sub/im/mixins/case-info-state.js";
import { filterEnum } from "@/libs/tools.js";
import { CASE_GRADE } from "@/enum";
import { FOLLOW_STATUS, IM_CASE_SOURCE_TYPE, IM_CONVERSATION_TAG_MAP } from "@/enum/imCardEnum.js";
import { turnToCaseSourceDetails } from "@/libs/turnPages.js";

export default {
  name: "ChatRoomTopCaseSourceCard",
  methods: { turnToCaseSourceDetails },
  mixins: [caseInfoStateProps],
  computed: {
    FOLLOW_STATUS() {
      return FOLLOW_STATUS;
    },
    IM_CONVERSATION_TAG_MAP() {
      return IM_CONVERSATION_TAG_MAP;
    },
    IM_CASE_SOURCE_TYPE() {
      return IM_CASE_SOURCE_TYPE;
    },
    /* 获取案源配置*/
    geCaseGradeConfig() {
      return filterEnum(CASE_GRADE, this.caseInfo.caseGrade);
    }
  },
};
</script>

<style scoped lang="scss">

.case-source-card{
  background: #FFFFFF;
  border-radius: 12px 12px 12px 12px;
  padding: 17px 16px 0;
  .top{
    .score{
      font-size: 12px;
      padding: 2px 4px;
      font-weight: 400;
      color: #FFFFFF;
      background: #867DE8;
      border-radius: 4px 4px 4px 4px;
    }
    .type{
      font-size: 16px;
      font-weight: 500;
      padding: 0 8px;
      color: #333333;
    }
    .order-grab-type{
      padding: 2px 6px;
      background: #EBF3FE;
      border-radius: 4px 4px 4px 4px;
      font-size: 12px;
      font-weight: 400;
      color: #3887F5;
    }
    .follow-up-status{
      font-size: 14px;
      font-weight: 400;
      color: #999999;
    }
  }
  .desc{
    font-size: 15px;
    font-weight: 400;
    color: #333333;
    margin: 13px 0 12px;
    word-break: break-all;
  }
  .case-source-type{
    padding-bottom: 16px;
    .type-item{
      font-size: 12px;
      font-weight: 400;
      color: #333333;
      padding: 2px;
      background: #F5F5F7;
      opacity: 1;
      border: 1px solid #CCCCCC;
      border-radius: 4px 4px 4px 4px;
    }
  }
  .date-info{
    height: 44px;
    border-top: 1px solid #EEEEEE;
    .date{
      font-size: 12px;
      font-weight: 400;
      color: #8A8D94;
    }
    .city{
      font-size: 12px;
      font-weight: 400;
      padding-left: 16px;
      color: #8A8D94;
    }
    .to-detail{
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      .iconfont{
        font-size: 13px;
        color: #999999;
        padding-left: 4px;
      }
    }
  }
}
</style>
