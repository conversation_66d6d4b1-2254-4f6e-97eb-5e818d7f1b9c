<template>
  <div class="phrases-list">
    <div
      v-for="i in list"
      :key="i.id"
      class="item"
    >
      {{ i.text }}
      <div
        class="btn flex flex-align-center flex-space-center"
        @click="toUpdate(i)"
      >
        <i class="iconfont icon-pinglun" />修改
      </div>
    </div>
  </div>
</template>

<script>
import { quickReplyGet } from "@/api/im.js";
import { turnPages } from "@/libs/turnPages.js";

export default {
  name: "List",
  data() {
    return {
      list: []
    };
  },
  onShow() {
    quickReplyGet().then(({ data = [] }) => {
      this.list = data;
    });
  },
  methods: {
    toUpdate({ id }){
      turnPages({
        path: "/pages/sub/im/phrases/index",
        query: {
          id
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.phrases-list{
  background: white;
  min-height: 100vh;
  padding: 0 16px;
  .item{
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    border-bottom: 0.5px solid #EEEEEE;
    padding: 16px 10px ;
    .btn{
      padding-top: 12px;
      font-size: 14px;
      font-weight: 400;
      color: #3887F5;
      .iconfont{
        font-size: 16px;
        padding-right: 7px;
      }
    }
  }
}
</style>
