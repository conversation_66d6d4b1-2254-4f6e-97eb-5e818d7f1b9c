<template>
  <div class="date-select">
    <float-popup
      :show="show"
      :contentWrapperStyle="contentWrapperStyle"
      @maskClick="$emit('update:show', false)"
    >
      <div class="flex flex-wrap pb-[8px]">
        <div
          v-for="item in list"
          :key="item.value"
          class="py-[8px] pl-[16px]"
        >
          <div
            :class="[{ active: item.value === value}]"
            class="rounded-[14px]  text-[#333333] border-[1px] border-solid border-[#DDDDDD] px-[13px] py-[5px] flex flex-space-between flex-align-center"
            @click="handleClick(item)"
          >
            <p class="text-[12px]">
              {{ item.label }}
            </p>
          </div>
        </div>
      </div>
    </float-popup>
  </div>
</template>

<script>

import FloatPopup from "@/pages/index/components/floatPopup.vue";

export default {
  name: "PopupCheckBox",
  components: { FloatPopup },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => ([])
    },
    value: {
      type: [Number, String],
      default: ""
    },
    contentWrapperStyle: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    handleClick(item){
      this.$emit("input", item.value);
      this.$emit("handleSelect", item);
      this.$emit("update:show", false);
    },
  },
};
</script>

<style lang="scss" scoped>
.active{
  color: #3887F5;
  border-color: #3887F5;
}
</style>
