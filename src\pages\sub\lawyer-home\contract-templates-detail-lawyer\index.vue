<template>
  <login-layout>
    <div class="pb-[40px] grid grid-cols-2 gap-x-[20px] gap-y-[24px] mt-[16px] mx-[14px]">
      <div
        v-for="item in contractList"
        :key="item.id"
      >
        <contract-templates-item
          :data="item"
          :whetherToNeedLawyerInfo="false"
        />
      </div>
    </div>
  </login-layout>
</template>

<script>
import LoginLayout from "@/components/login/login-layout.vue";
import { contractTemplatesPage } from "@/api/my-consultation-new";
import ContractTemplatesItem from "@/pages/rapid-consultation-confirm-order/components/contract-templates-item.vue";

export default {
  name: "ContractTemplatesDetail",
  components: { ContractTemplatesItem, LoginLayout },
  data() {
    return {
      contractList: []
    };
  },
  onLoad({ id }) {
    this.getContractList(id);
  },
  methods: {
    getContractList(lawyerId) {
      contractTemplatesPage({
        lawyerId,
        currentPage: 1,
        pageSize: 99999
      }).then(res => {
        this.contractList = res.data?.records;
      });
    }
  }
};
</script>

<style>
page {
  background-color: #fff;
}
</style>
