<template>
  <div class="flex">
    <p
      v-for="i in list"
      :key="i.value"
      class="px-[12px] mr-[12px] last-of-type:mr-[0] py-[4px] bg-[#F5F5F7] rounded-[46px] text-[12px] text-[#333333]"
      :class="[{active: i.value === value}]"
      @click="handleClick(i)"
    >
      {{ i.label }}
    </p>
  </div>
</template>

<script>
export default {
  name: "AppRadio",
  props: {
    list: {
      type: Array,
      default: () => []
    },
    value: {
      type: [String, Number],
      default: ""
    }
  },
  methods: {
    handleClick(data){
      this.$emit("input", data.value);
      this.$emit("handleChange", data);
    }
  }
};
</script>

<style scoped lang="scss">
.active{
  background: #EBF3FE;
  color: #3887F5;
}
</style>