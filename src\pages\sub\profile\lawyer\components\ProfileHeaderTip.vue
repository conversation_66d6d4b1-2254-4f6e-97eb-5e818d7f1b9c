<template>
  <div
    v-if="show"
    class="tip flex flex-space-between flex-align-center"
  >
    <div>
      完善个人信息可以免费创建个人网站，打造个人IP，有助于提高咨询用户曝光，帮助您获取更多的案源
    </div>
    <p
      class="iconfont icon-a-cuowuguanbi"
      @click="show = false"
    />
  </div>
</template>

<script>
export default {
  name: "ProfileHeaderTip",
  data() {
    return {
      show: true,
    };
  },
};
</script>

<style lang="scss" scoped>
.tip {
  padding: 10px 16px;
  width: 375px;
  height: 54px;
  background: #fff7eb;
  box-sizing: border-box;
  opacity: 1;
  font-size: 12px;
  font-weight: 400;
  color: #f78c3e;
}

.iconfont {
  width: 16px;
  height: 16px;
  margin-left: 12px;
}
</style>
