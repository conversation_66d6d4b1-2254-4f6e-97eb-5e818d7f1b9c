<template>
  <login-layout>
    <div>
      <template v-if="!selfLawyerFlag">
        <div
          class="px-[16px] pt-[31px] bg-[linear-gradient(180deg,_#E5EFFF_0%,_rgba(229,239,255,0)_100%)]"
        >
          <lawyer-home-card
            :data="lawyerInfo"
            :isSelf="isSelf"
          />
        </div>
      </template>
      <lawyer-home-self-card
        v-if="selfLawyerFlag"
        :lawyerInfo="lawyerInfo"
      />
      <!-- 选择服务 -->
      <div
        v-if="!selfLawyerFlag && isSelf"
        id="home-servers"
        class="mg-tp-12 mx-[16px]"
      >
        <lawyer-home-servers
          :lawyerInfo="lawyerInfo"
          :serviceManageV2VoList="lawyerInfo.serviceManageV2VoList || []"
        />
      </div>
      <div class="mt-[12px]">
        <lawyer-home-other-info :lawyerInfo="lawyerInfo" />
      </div>
      <div
        v-if="isSelf"
        class="fixed absolute-x-center bottom-[16px] z-10"
      >
        <home-profile-score v-if="isSelf" />
        <u-safe-bottom />
      </div>

      <template v-if="!selfLawyerFlag">
        <lawyer-home-dynamic :lawyerInfo="lawyerInfo" />
        <u-sticky :zIndex="99">
          <lawyer-home-header :lawyerInfo="lawyerInfo" />
        </u-sticky>
      </template>
      <template v-if="!selfLawyerFlag">
        <!-- 律师评价 -->
        <div
          v-if="hasEvaluate"
          id="home-score"
          class="mt-[16px]"
        >
          <lawyer-home-score :lawyerInfo="lawyerInfo" />
        </div>
        <div class="mg-tp-12">
          <!-- 案件总数 -->
          <div
            v-if="hasCase"
            class="mg-b-12"
          >
            <lawyer-home-case
              :caseStatistics="caseStatistics"
              :lawyerInfo="lawyerInfo"
              :total="caseTotal"
            />
          </div>
          <!-- 近期案例 -->
          <div
            v-if="hasCase"
            id="home-case"
            class="mg-b-12"
          >
            <lawyer-home-recent-cases :lawyerInfo="lawyerInfo" />
          </div>
          <!-- 案件统计 -->
          <div class="mg-b-12">
            <lawyer-home-case-statistics :lawyerInfo="lawyerInfo" />
          </div>
        </div>
        <div
          v-if="hasArticle"
          id="home-essay"
        >
          <lawyer-home-essay :lawyerInfo="lawyerInfo" />
        </div>
      </template>
      <u-safe-bottom v-if="!selfLawyerFlag" />
    </div>
  </login-layout>
</template>

<script>
import LawyerHomeCard from "@/pages/sub/lawyer-home/components/lawyer-home-card.vue";
import LawyerHomeRecentCases from "@/pages/sub/lawyer-home/components/lawyer-home-recent-cases.vue";
import LawyerHomeScore from "@/pages/sub/lawyer-home/components/lawyer-home-score/index.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import LawyerHomeServers from "@/pages/sub/lawyer-home/components/lawyer-home-servers.vue";
import LawyerHomeCaseStatistics from "@/pages/sub/lawyer-home/components/lawyer-home-case-statistics/index.vue";
import LawyerHomeCase from "@/pages/sub/lawyer-home/components/lawyer-home-case.vue";
import getLawyerInfo from "@/pages/sub/lawyer-home/mixins/getLawyerInfo.js";
import LawyerHomeHeader from "@/pages/sub/lawyer-home/components/lawyer-home-header.vue";
import { mapGetters } from "vuex";
import store from "@/store";
import { bigdataLawyerIndex, lawyerLawCase } from "@/api/lawyer.js";
import { INDICATOR_NAME } from "@/libs/config.js";
import { isArrNull, isObjNull } from "@/libs/basics-tools.js";
import LawyerHomeSelfCard from "@/pages/sub/lawyer-home/components/lawyer-home-self-card.vue";
import LawyerHomeEssay from "@/pages/sub/lawyer-home/components/lawyer-home-essay.vue";
import USticky from "@/uview-ui/components/u-sticky/u-sticky.vue";
import LawyerHomeDynamic from "@/pages/sub/lawyer-home/components/lawyer-home-dynamic.vue";
import LoginLayout from "@/components/login/LoginLayout.vue";
import HomeProfileScore from "@/pages/sub/lawyer-home/components/HomeProfileScore.vue";
import LawyerHomeOtherInfo from "@/pages/sub/lawyer-home/components/LawyerHomeOtherInfo.vue";

export default {
  name: "LawyerHome",
  components: {
    LawyerHomeOtherInfo,
    HomeProfileScore,
    LawyerHomeDynamic,
    USticky,
    LawyerHomeEssay,
    LawyerHomeSelfCard,
    LoginLayout,
    LawyerHomeHeader,
    LawyerHomeCase,
    LawyerHomeCaseStatistics,
    LawyerHomeServers,
    USafeBottom,
    LawyerHomeScore,
    LawyerHomeRecentCases,
    LawyerHomeCard,
  },
  mixins: [getLawyerInfo],
  onPageScroll() {},
  data() {
    return {
      /** 案件总数图标数据 */
      caseStatistics: [],
      /** 案件总数 */
      caseTotal: 0,
    };
  },
  computed: {
    ...mapGetters({
      hasEvaluate: "lawyerHome/hasEvaluate",
      hasCase: "lawyerHome/hasCase",
      hasArticle: "lawyerHome/hasArticle",
    }),
    /**
     * ! 是否是自营律师
     * 不写成两个组件的原因是因为如果放在组件中
     * 头部导航栏通过id获取元素位置会失效
     */
    selfLawyerFlag() {
      return this.lawyerInfo.selfLawyerFlag;
    },
  },
  watch: {
    lawyerInfo: {
      handler(value) {
        if (isObjNull(value)) return;

        this.getCaseStatistics();
      },
      deep: true,
      immediate: true,
    },
  },
  destroyed() {
    store.commit("lawyerHome/INIT_STATE");
  },
  methods: {
    isArrNull,
    isObjNull,
    /** 获取律师手动上传案例件 */
    getCaseListOfMe() {
      this.isOfMeState = true;
      if (!this.lawyerInfo.id) return;
      /* lawyerId	是	string	律师ID
       currentPage	否	Integer	当前页码
       pageSize	否	Integer	每页大小*/
      lawyerLawCase({
        lawyerId: this.lawyerInfo.id,
        currentPage: 1,
        pageSize: 3,
      }).then(({ data }) => {
        this.caseTotal = data.total || 0;
        store.commit("lawyerHome/SET_CASE_COUNT", this.caseTotal);
      });
    },
    /** 获取案件统计数据 */
    getCaseStatistics() {
      if (!this.lawyerInfo.realName || !this.lawyerInfo.lawyerOffice) {
        store.commit("lawyerHome/SET_CASE_COUNT", 0);
        return false;
      }

      bigdataLawyerIndex({
        lawyerName: this.lawyerInfo.realName,
        lawfirmName: this.lawyerInfo.lawyerOffice,
        indexKey: INDICATOR_NAME.caseTotal,
      }).then((res) => {
        this.caseStatistics = res.data.indexElements || [];
        this.caseTotal = res.data.total || 0;
        store.commit("lawyerHome/SET_CASE_COUNT", this.caseTotal);
        if (this.caseTotal < 1) {
          this.getCaseListOfMe();
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.text-img {
  width: 375px;
  margin-top: 43px;
}
</style>
