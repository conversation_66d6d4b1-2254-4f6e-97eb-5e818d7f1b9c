<template>
  <div class="px-[16px]">
    <swiper
      :autoplay="true"
      :interval="2000"
      circular
      vertical
      class=" h-[44px]"
    >
      <swiper-item
        v-for="(item, index) in swiperList"
        :key="index"
      >
        <div class="flex items-center h-[44px]">
          <i class="iconfont icon-tongzhi text-[#EB4738] text-[16px]" />
          <p class="pl-[8px] text-[14px] text-[#666666] flex-1">
            {{ item.text }}
          </p>
          <p class="text-[14px] text-[#999999]">
            {{ item.date }}
          </p>
        </div>
      </swiper-item>
    </swiper>
  </div>
</template>

<script>
export default {
  name: "ProfileSwiperTips",
  data(){
    return {
      swiperList: [
        {
          text: "恭喜成律师在厦门成案1万元",
          date: "3分钟前"
        }, {
          text: "北京王律佣金提现10389元",
          date: "4分钟前"
        }, {
          text: "成都李律获得10法临币悬赏金",
          date: "5分钟前"
        }, {
          text: "湖南杨律获得1V1咨询5星好评",
          date: "8分钟前"
        }, {
          text: "广州付律通过认证，开始接单",
          date: "10分钟前"
        }, {
          text: "河南王律师佣金提现736元",
          date: "12分钟前"
        }, {
          text: "王律师今日抢2单付费咨询",
          date: "15分钟前"
        }, {
          text: "甘肃刘律上传合同被下载6次",
          date: "18分钟前"
        }, {
          text: "恭喜胡律师在上海成案5万元",
          date: "19分钟前"
        }, {
          text: "青海王律佣金提现138元",
          date: "21分钟前"
        }, {
          text: "新疆李律获得10法临币悬赏金",
          date: "23分钟前"
        }, {
          text: "江西杨律获得在线咨询5星好评",
          date: "25分钟前"
        }, {
          text: "天津宋律通过认证，开始接单",
          date: "28分钟前"
        }, {
          text: "恭喜宋律师在吉林成案3万元",
          date: "30分钟前"
        }
      ]
    };
  }
};
</script>

<style scoped lang="scss">

</style>