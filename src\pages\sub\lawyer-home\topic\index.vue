<template>
  <div class="bg-[#F5F5F7] relative z-10">
    <img
      alt=""
      class="w-[375px] h-[253px] block absolute left-0 top-0 -z-10"
      src="img/SWZAXYnO.png"
    >
    <div class="h-[174px]" />
    <div>
      <entrance-ask-lawyer
        clickAskLawyerBuryPoint="LAW_APPLET_CONSULT_TOPIC_PAGE_ONLINE_ASK_CLICK"
        clickPhoneConsultBuryPoint="LAW_APPLET_CONSULT_TOPIC_PAGE_PHONE_CONSULT_CLICK"
      />
    </div>
    <div
      class="mt-[12px] w-[351px] bg-[#FFFFFF] rounded-[8px] box-border mx-auto"
    >
      <img
        alt=""
        class="pt-[16px] pb-[12px] w-[140px] h-[25px] block mx-auto"
        src="img/Frame1321315161.png"
      >
      <HomeHotTopicItem :list="homeHotTopicCommon" />
      <div
        class="w-[351px] px-[16px] py-[20px] box-border flex items-center justify-between"
      >
        <div class="flex items-center">
          <img
            :src="special.supplementDesc.icon"
            alt=""
            class="w-[40px] h-[40px] rounded-[8px] mr-[8px] block"
          >
          <div>
            <div class="font-bold text-[14px] text-[#333333]">
              没有找到匹配类型？
            </div>
            <div class="text-[12px] text-[#999999] mt-[2px]">
              可以直接选择{{ special.remark }}
            </div>
          </div>
        </div>
        <div
          class="w-[88px] h-[28px] rounded-[15px] border-[1px] border-solid border-[#3887F5] flex items-center justify-center text-[14px] text-[#3887F5]"
          @click="toSpecialSession({ typeValue: special.label })"
        >
          {{ special.remark }}
        </div>
      </div>
    </div>
    <u-safe-bottom />
  </div>
</template>

<script>
import HomeHotTopicItem from "@/pages/sub/lawyer-home/topic/components/HomeHotTopicItem.vue";
import EntranceAskLawyer from "@/pages/sub/lawyer-home/topic/components/EntranceAskLawyer.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { toSpecialSession } from "@/libs/turnPages";
import HomeHotTopicMixins from "@/pages/index/component/homeHotTopic/HomeHotTopicMixins";

export default {
  name: "OtherTopic",
  components: { USafeBottom, EntranceAskLawyer, HomeHotTopicItem },
  mixins: [HomeHotTopicMixins],
  mounted() {},
  methods: {
    toSpecialSession,
  },
};
</script>

<style>
page {
  background-color: #fff;
}
</style>
