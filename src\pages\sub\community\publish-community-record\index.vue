<template>
  <div>
    <div class="bg-[#FFFCF8] text-[13px] text-[#FA700D] px-[12px] py-[8px] mb-[3px]">
      请确保发布信息的真实性，若涉及虚假信息及违法信息，账号将会被封禁
    </div>
    <div
      v-for="i in list"
      :key="i.id"
      class="px-[11px] pt-[12px]"
    >
      <community-record-item :data="i" />
    </div>
    <div class="fixed bottom-0 left-0 right-0 bg-white px-[16px] py-[8px]">
      <div class="flex justify-between items-center">
        <div
          class="w-[96px] center h-[44px] rounded-[46px] border-[1px] border-solid border-[#CCCCCC] text-[16px] text-[#666666]"
          @click.stop="PublishingRulesPopupState = true"
        >
          发布规则
        </div>
        <div
          class="flex-1 ml-[12px] h-[44px] relative bg-[#3887F5] rounded-[68px] font-bold text-[16px] text-[#FFFFFF] center"
          @click.stop="toPublishCompanionGroup"
        >
          <img
            class="w-[228px] h-[28px] absolute absolute-x-center top-[-25px]"
            src="@/pages/sub/community/imgs/<EMAIL>"
            alt=""
          >
          发布新群
        </div>
      </div>
      <u-safe-bottom />
    </div>
    <publishing-rules-popup v-model="PublishingRulesPopupState" />
  </div>
</template>

<script>
import CommunityRecordItem from "@/pages/sub/community/components/CommunityRecordItem/index.vue";
import PublishingRulesPopup from "@/pages/sub/community/components/PublishingRulesPopup/index.vue";
import { isArrNull } from "@/libs/basics-tools";
import { communityGroupMyPublishedGroups } from "@/api/communityGroup";
import { toPublishCompanionGroup } from "@/libs/turnPages";

export default {
  name: "PublishCommunityRecord",
  components: { PublishingRulesPopup, CommunityRecordItem },
  data() {
    return {
      PublishingRulesPopupState: false,
      query: {
        currentPage: 1,
        pageSize: 10,
      },
      isRequest: false,
      isLastPage: false,
      list: []
    };
  },
  onReachBottom() {
    this.query.currentPage++;
    this.getList();
  },
  onPullDownRefresh() {
    console.log("refresh");
    this.resetData();
    this.getList().finally(() => {
      uni.stopPullDownRefresh();
    });
  },
  onShow() {
    this.handleSearch();
  },
  methods: {
    toPublishCompanionGroup(){
      toPublishCompanionGroup({});
    },
    isArrNull,
    /* 搜索 */
    handleSearch(data = {}) {
      this.query = {
        ...this.query,
        ...data
      };
      this.resetData();
      this.getList();
    },
    /** 重置数据 */
    resetData() {
      this.list = [];
      this.query.currentPage = 1;
      this.isLastPage = false;
      this.isRequest = false;
    },
    getList() {
      if (this.isLastPage) {
        return Promise.resolve();
      }
      return communityGroupMyPublishedGroups(this.query).then(res => {
        this.list = [...this.list, ...res.data.records];
        if (this.list.length >= res.data.total) {
          this.isLastPage = true;
        }
        return this.list;
      })
        .finally(() => {
          this.isRequest = true;
        });
    }
  }
};
</script>

<style scoped lang="scss">

</style>