<template>
  <div>
    <img
      alt=""
      class="w-full h-[146px] absolute top-0 left-0 -z-10"
      src="../topic/img/Bx28YWUe.png"
    >
    <div class="h-[106px]" />
    <entrance-ask-lawyer
      clickAskLawyerBuryPoint="LAW_APPLET_HIGH_QUALITY_ANSWER_SECOND_PAGE_ONLINE_ASK_CLICK"
      clickPhoneConsultBuryPoint="LAW_APPLET_HIGH_QUALITY_ANSWER_SECOND_PAGE_PHONE_CONSULT_CLICK"
    />
    <div class="mt-[12px] mx-[12px]">
      <entrance-lawyer-topic
        :bgImgIndex="0"
        :dataSource="rankList"
        clickLawyerCardBuryPoint="LAW_APPLET_HIGH_QUALITY_ANSWER_SECOND_PAGE_LAWYER_CARD_CLICK"
        title="法律指南·律师排行榜"
      />
    </div>
    <u-sticky>
      <div class="bg-[#f5f5f7] pt-[20px]">
        <div class="text-[18px] font-bold text-[#333333] px-[16px]">
          全部指南
        </div>
        <app-tabs-other
          v-model="typeValue"
          :list="tabList"
          bgClassName="bg-FFFFFF"
          @change="changeTab"
        />
      </div>
    </u-sticky>
    <div class="px-[16px] min-h-[500px]">
      <div
        v-for="i in list"
        :key="i.id"
        class="[&:not(:last-child)]:mb-[12px]"
      >
        <legal-guide-item :data="i" />
      </div>
    </div>
    <u-safe-bottom />
  </div>
</template>

<script>
import LegalGuideItem from "@/components/legal-guide-item/index.vue";
import { dataDictionary, lawGuideServerPage } from "@/api";
import { toAskLawyer } from "@/libs/turnPages";
import EntranceLawyerTopic from "@/pages/sub/lawyer-home/topic/components/EntranceLawyerTopic.vue";
import EntranceAskLawyer from "@/pages/sub/lawyer-home/topic/components/EntranceAskLawyer.vue";
import { lawyerNewRank } from "@/api/lawyer";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import USticky from "@/uview-ui/components/u-sticky/u-sticky.vue";
import AppTabsOther from "@/components/app-tabs/other.vue";

export default {
  name: "LawyerHomeAsk",
  components: {
    AppTabsOther,
    USticky,
    USafeBottom,
    EntranceAskLawyer,
    EntranceLawyerTopic,
    LegalGuideItem,
  },
  data() {
    return {
      pageTotal: 0,
      typeValue: null,
      /** tab数据 */
      tabList: [],
      list: [],
      pageParams: {
        /** 页码 */
        currentPage: 1,
        /** 每页条数 */
        pageSize: 20,
      },
      /** 排行榜数据 */
      rankList: [],
    };
  },
  onLoad(query) {
    this.typeValue = query.typeValue && query.typeValue === "null" && null;

    this.getTabData();
    this.getRankList();
  },
  onHide() {
    clearTimeout(this.timer);
  },
  onReachBottom() {
    this.scrollToLower();
  },
  methods: {
    /** 获取排行榜数据 */
    getRankList() {
      lawyerNewRank({
        manType: 5,
        type: 14,
        rankType: 0,
      }).then(({ data = [] }) => {
        this.rankList = data;
      });
    },
    /** 获取tab栏数据 */
    getTabData() {
      dataDictionary({ groupCode: "LAWYER_SPECIALITY" }).then(
        ({ data = [] }) => {
          this.tabList = data;

          this.typeValue = this.typeValue || this.tabList[0].value;

          console.log(this.typeValue, "this.typeValue");
          console.log(this.tabList[0].value, "this.typeValue");
          this.getList();
        }
      );
    },
    toAskLawyer() {
      toAskLawyer();
    },
    /** 更改标签后重新获取数据 */
    changeTab(value) {
      this.pageParams = this.$options.data().pageParams;
      this.typeValue = value;
      this.list = [];
      this.getList();
    },

    scrollToLower() {
      this.pageParams.currentPage++;
      this.getList();
    },
    getList() {
      lawGuideServerPage({
        typeValue: this.typeValue === null ? "" : Number(this.typeValue),
        ...this.pageParams,
      }).then(({ data = {} }) => {
        const { records = [] } = data;
        this.list = [...this.list, ...records];
        this.pageTotal = data.total || 0;
      });
    },
  },
};
</script>
