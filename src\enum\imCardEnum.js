

/* im 案源类型*/
/* type 0 案源 1咨询（一对一） 2问答*/
export const IM_CASE_SOURCE_TYPE = {
  /* 咨询*/
  CONSULTATION: 1,
  /* 问答*/
  QUESTION_AND_ANSWER: 2,
  /* 案源*/
  CASE_SOURCE: 0,
  /* 协作 */
  COLLABORATION: 5
};


/* im会话列表 tag配置*/
/* 先查看type 是啥子类型 然后查询serverWay或者createFrom 对应的文案*/
/* type 0 案源 1咨询（一对一） 2问答*/
/* serverWay（type===0） 1平台派单  2自主抢单 8独享抢单 9案源包派发*/
/* createFrom（type===1）1 1v1咨询 2派单咨询*/
/* type===2 直接是问答*/
/* type===5 协作 */
export const IM_CONVERSATION_TAG_MAP = {
  [IM_CASE_SOURCE_TYPE.CASE_SOURCE]: {
    1: "平台派单",
    2: "自主抢单",
    8: "独享抢单",
    9: "案源包派发"
  },
  [IM_CASE_SOURCE_TYPE.CONSULTATION]: {
    1: "1v1咨询",
    2: "派单咨询"
  },
  [IM_CASE_SOURCE_TYPE.QUESTION_AND_ANSWER]: {
    1: "问答"
  },
  [IM_CASE_SOURCE_TYPE.COLLABORATION]: {
    0: "协作类型"
  }
};

/* 系统消息类型*/
export const SYSTEM_MSG_TYPE = {
  SYSTEM: "SYSTEM", // 系统消息
  SERVER: "SERVER", // 服务消息
  INTERACTION: "INTERACTION"// 互动消息
};

/* 跟进状态*/
/* 状态（0待锁定，1服务中，2待完成，3已完成，9取消锁定（律师拒接） ，91用户退单，99律师反无效）*/
/* 跟进中 1 2*/
/* 已完成 3*/
/* 已关闭 9 91 99*/
export  const FOLLOW_STATUS = {
  1: {
    text: "跟进中",
  },
  2: {
    text: "跟进中",
  },
  3: {
    text: "已完成",
  },
  9: {
    text: "已关闭",
  },
  91: {
    text: "已关闭",
  },
  99: {
    text: "已关闭",
  }
};
