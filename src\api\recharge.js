import { requestCommon } from "@/libs/axios";

export const orderLawyerCreateOrder = (params) => requestCommon.post("/order/v2/orderLawyer/createOrder", params, { loading: true }); // 用户订单创建v2

export const getCaseSourceGoodsDetail = (params) => requestCommon.post("/paralegal/lawyer/caseSourceGoods/detail/" + params); // 案源线索包的详情接口
export const caseSourceGoodsGetByType = (params) => requestCommon.post("/paralegal/caseSourceGoods/v2/getByType",  params);
export const canGrabClueNum = (params) => requestCommon.post("/paralegal/lawyerCluePack/canGrabClueNum",  params);
export const queryOrderStatus = (params) => requestCommon.post("/order/v2/orderLawyer/queryOrderStatus",  params);
export const walletBalance = (params) => requestCommon.post("/paralegal/v2/wallet/balance",  params);
export const lawyerBillPage = (params) => requestCommon.post("/paralegal/lawyerBill/page",  params);
export const lawyerBillDetail = (params) => requestCommon.post(`/paralegal/lawyerBill/${params}`);
export const temporaryAccount = (params) => requestCommon.post("/paralegal/v2/wallet/temporaryAccount/detail/page", params);
