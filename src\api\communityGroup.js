import { requestCommon } from "@/libs/axios";

/* 社群列表查询*/
export const communityGroupPage = (params) => requestCommon.post("/paralegal/communityGroup/page", params); //

/* 社群详情*/
export const communityGroupDetail = (params) => requestCommon.post("/paralegal/communityGroup/detail", params); //

/* 获取二维码*/
export const communityGroupGetQrCode = (params) => requestCommon.post("/paralegal/communityGroup/getQrCode", params); //

/* 分享二维码*/
export const communityGroupShareQrCode = (params) => requestCommon.post("/paralegal/communityGroup/shareQrCode", params); //

/* 发布群*/
export const communityGroupPublishGroup= (params) => requestCommon.post("/paralegal/communityGroup/publishGroup", params); //

/* 修改群*/
export const communityGroupUpdateGroup = (params) => requestCommon.post("/paralegal/communityGroup/updateGroup", params); //

/* 我发布的群列表*/
export const communityGroupMyPublishedGroups = (params) => requestCommon.post("/paralegal/communityGroup/myPublishedGroups", params); //

/* 获取权益状态*/
export const communityGroupGetBenefitStatus = (params) => requestCommon.post("/paralegal/communityGroup/getBenefitStatus", params); //