<template>
  <div
    class="bg-[#FFFFFF] rounded-[12px] p-[16px] pb-[0]"
    @click.stop="toDetail"
  >
    <div class="flex pb-[16px]">
      <img
        class="w-[60px] h-[60px] rounded-[5px] overflow-hidden flex-shrink-0"
        :src="data.groupAvatar"
        mode="aspectFill"
        alt=""
      >
      <div class="pl-[7px] flex-1">
        <div class="flex justify-between">
          <p class="font-bold text-[16px] text-[#333333]">
            {{ data.groupName }}
          </p>
          <p class="w-[58px] h-[28px] center bg-[linear-gradient(_116deg,_#71B5FF_0%,_#2676E4_100%)] rounded-[50px] text-[13px] text-[#FFFFFF]">
            进群
          </p>
        </div>
        <div class="flex pt-[7px]">
          <p class="px-[8px] py-[2px] bg-[#F7F6F6] rounded-[4px] ml-[4px] first-of-type:ml-[0] text-[14px] text-[#3887F5]">
            #工伤群
          </p>
        </div>
      </div>
    </div>
    <div class="flex items-center justify-between h-[42px] border-0 border-solid border-t-[1px] border-[#EEEEEE]">
      <div class="text-[13px] text-[#666666] flex items-center">
        <i class="iconfont icon-dingwei !text-[13px] !text-[#666666]" />
        {{ data.regionName }}
      </div>
      <div class="text-[12px] text-[#999999]">
        群人数：{{ data.currentMemberCount }}人
      </div>
    </div>
  </div>
</template>

<script>
import { toCompanionGroupDetails } from "@/libs/turnPages";

export default {
  name: "GroupLawyerItem",
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  methods: {
    toDetail() {
      toCompanionGroupDetails({ id: this.data.id });
    },
  },
};
</script>

<style scoped lang="scss">

</style>