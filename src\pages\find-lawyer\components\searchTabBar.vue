<template>
  <div class="tab-container">
    <div
      :style="[tabsStyle]"
      class="search-bar flex"
    >
      <div
        :class="{ active: show3 }"
        class="search-item"
        @click="handleClick('show3')"
      >
        {{ cityInfo.name || "全国" }}
        <span
          :class="{ active: show3 }"
          class="icon2 iconfont icon-xiala"
        />
      </div>
      <div
        :class="{ active: show1 }"
        class="search-item"
        @click="handleClick('show1')"
      >
        {{ goodAtTypeInfo.label }}
        <span
          :class="{ active: show1 }"
          class="icon2 iconfont icon-xiala"
        />
      </div>
      <div
        :class="{ active: show2 }"
        class="search-item"
        @click="handleClick('show2')"
      >
        <p class="text-ellipsis max-w-[80px]">
          {{ sortInfo.value==='9'?'默认排序': sortInfo.label }}
        </p>
        <span
          :class="{ active: show2 }"
          class="icon2 iconfont icon-xiala"
        />
      </div>
    </div>
    <popup-type-collaboration
      :show.sync="show1"
      :contentWrapperStyle="contentWStyle"
      @setType="changeCaseTypeInfo"
    />
    <!--   案件协作 -->
    <popup-select
      :activeStyle="activeStyle"
      :contentWrapperStyle="contentWStyle"
      :list="sortList"
      :show.sync="show2"
      :value="sortInfo.value"
      @handleSelect="changeSortInfoInfo"
    />
    <popup-location
      :contentWrapperStyle="contentWStyle"
      :show.sync="show3"
      isLocation
      nationwide
      @setCity="setCity"
    />
  </div>
</template>

<script>
import PopupLocation from "@/pages/index/components/popup-location/index.vue";
import PopupSelect from "@/pages/index/components/popupSelect.vue";
import { pxToRpx } from "@/libs/tools";
import PopupTypeCollaboration from "@/pages/index/components/popup-type-collaboration/index.vue";

export default {
  name: "FindLawyerSearchTabBar",
  components: { PopupTypeCollaboration, PopupSelect, PopupLocation },
  props: {
    localStatus: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      show1: false,
      show2: false,
      show3: false,
      /* 擅长类型*/
      goodAtTypeInfo: {
        label: "协作类型",
        value: "",
        parentValue: ""
      },
      /* 排序 选中信息*/
      sortInfo: {
        label: "默认排序",
        value: "9",
      },
      /* 城市信息*/
      cityInfo: {
        name: "全国",
      },
      /* 排序*/
      sortList: [
        {
          label: "默认排序（活跃越近，展示越靠前）",
          value: "9",
        },
        {
          label: "按平台服务好评数量排名",
          value: "7",
        },
        {
          label: "执业年限排名",
          value: "3",
        },
      ],
      contentWStyle: {
        top: pxToRpx(41),
      },
      activeStyle: {
        color: "#3887F5",
      },
    };
  },
  computed: {
    /** 是否显示弹窗 */
    showPopup() {
      return this.show1 || this.show2 || this.show3;
    },
    /** 顶部导航的样式 */
    tabsStyle() {
      if (!this.showPopup) return {};

      return uni.$u.addStyle({
        top: 0,
        right: 0,
        left: 0,
        position: "fixed",
      });
    },
  },
  methods: {
    // 案源线索子tab切换
    handleClick(type) {
      if (this[type]) return (this[type] = false);
      this.show1 = false;
      this.show2 = false;
      this.show3 = false;
      this[type] = true;
    },
    // 城市选择
    setCity(city) {
      console.log(city);
      this.cityInfo = city;
      this.dataChange();
    },
    // 数据变化时重新请求数据
    dataChange() {
      const data = {
        sort: this.sortInfo.value,
        /* {name: "石家庄", code: 130100, provinceName: "河北省", province: 130000*/
        cityCode: this.cityInfo.code || "",
        proviceCode: this.cityInfo.province || "",
        cdType: "",
        xzType: "",
        ...(this.goodAtTypeInfo.parentValue === "1" ? { cdType: this.goodAtTypeInfo.value } : { xzType: this.goodAtTypeInfo.value }),
      };
      this.$emit("searchChange", data);
    },
    // 擅长类型选中
    changeCaseTypeInfo(item) {
      this.goodAtTypeInfo = {
        ...item.childActive,
        parentValue: item.parentActive.value,
      };
      this.show1 = false;
      this.dataChange();
    },
    // 案件协作选择
    changeSortInfoInfo(item) {
      this.sortInfo = item;
      this.dataChange();
    },
  },
};
</script>

<style lang="scss" scoped>
.tab-container {
  background: #f5f5f7;
  .search-bar {
    padding: 0 12px;
    position: relative;
    z-index: 2;
    background: #f5f5f7;
    .search-item {
      flex: 1;
      height: 41px;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      display: flex;
      align-items: center;
      justify-content: center;
      &.active {
        color: #3887f5;
      }
      .icon2 {
        transition: all 0.3s;
        margin-left: 3px;
        font-size: 12px;
        &.active {
          transform: rotate(180deg);
        }
      }
      .icon3 {
        font-size: 16px;
      }
    }
  }
}
.lawyerTypes-box {
  padding-top: 8px;
  border-radius: 0 0 16px 16px;
  background: #f5f5f7;
}
</style>
