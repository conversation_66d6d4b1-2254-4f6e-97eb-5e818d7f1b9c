<template>
  <div
    class="p-[16px] bg-white flex items-center mt-[12px] MutualAidMessages"
    @click="jump"
  >
    <img
      :src="data.fromUserImg"
      alt=""
      class="w-[44px] flex-shrink-0 h-[44px] rounded-[34px]"
    >
    <div class="flex-1 pl-[10px]">
      <div class="flex items-center justify-between">
        <p class="font-bold text-[16px] text-[#333333]">
          {{ data.fromUserName }}
        </p>
        <p class="text-[12px] text-[#999999]">
          {{ data.createTimeDesc }}
        </p>
      </div>
      <p class="text-[12px] pt-[4px] text-[#999999] text-ellipsis">
        {{ data.toText }}
      </p>
    </div>
  </div>
</template>

<script>
export default {
  name: "MutualAidMessages",
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      }
    },
    currentEnum: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  methods: {
    jump(){
      this.$emit("jump");
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
