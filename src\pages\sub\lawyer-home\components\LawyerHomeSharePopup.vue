<template>
  <app-popup
    :safeAreaInsetBottom="false"
    :show="show"
    mode="center"
  >
    <div class="p-[24px] w-[311px] box-border">
      <div class="font-bold text-[16px] text-[#333333] text-center">
        温馨提示
      </div>
      <div class="text-[14px] text-[#333333] mt-[12px]">
        法临律师助手小程序不提供付费服务聊天IM，需下载法临律师端APP进行服务哦，服务结束佣金可提现哦
      </div>
    </div>
    <div
      class="border-t-[1px] border-0 border-solid border-[#EEEEEE] grid grid-cols-2 h-[46px] text-[16px]"
    >
      <div
        class="flex items-center justify-center border-r-[1px] border-0 border-solid border-[#EEEEEE] text-[#333333]"
        @click="handleCancel"
      >
        取消
      </div>
      <div
        class="flex items-center justify-center text-[#3887F5]"
        @click="handleConfirm"
      >
        继续分享
      </div>
    </div>
  </app-popup>
</template>
<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";

export default {
  name: "LawyerHomeSharePopup",
  components: { AppPopup },
  props: {
    value: {
      type: Boolean,
      default: false,
      required: true,
    },
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  methods: {
    handleCancel() {
      this.$emit("cancel");
    },
    handleConfirm() {
      this.$emit("confirm");
    },
  },
};
</script>
