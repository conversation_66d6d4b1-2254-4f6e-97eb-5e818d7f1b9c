<template>
  <repid-return-popup
    v-model="show"
    :lawyerInfo="lawyerInfo"
    :safeAreaInsetBottom="safeAreaInsetBottom"
    :tips="tips"
    @close="close"
    @pay="pay"
  />
</template>

<script>
import payFailMixins from "@/mixins/payFailMixins.js";
import RepidReturnPopup from "@/components/RepidReturnPopup.vue";
import { isObjNull } from "@/libs/basics-tools";

export default {
  name: "LawyerServicePayPopup",
  components: { RepidReturnPopup },
  mixins: [payFailMixins],
  props: {
    /** 安全距离 */
    safeAreaInsetBottom: {
      type: Boolean,
      default: false,
    },
    /** 律师信息 */
    lawyerInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      show: false,
    };
  },
  computed: {
    tips() {
      return isObjNull(this.lawyerInfo)
        ? "平台将立即匹配律师，10s内接入服务，提供定制解决方案"
        : "平台将立即通知律师，10s内接入";
    },
  },
  methods: {
    /** 支付失败时回调 */
    payFailCallback() {
      this.show = true;
    },
    close() {
      this.show = false;
      this.$store.commit("lawyerHome/RESET_PHONE_CONSULTATION_CONFIG");
      this.$emit("close", false);
    },
    pay() {
      this.$emit("click");
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-content {
  height: 313px;
  position: relative;
  border-radius: 24px;
  opacity: 1;
  box-sizing: border-box;
}

.interaction {
  padding-top: 208px;

  &-icon {
    display: block;
    width: 16px;
    height: 16px;
  }

  &-text {
    font-size: 15px;
    font-weight: 400;
    color: #333333;
  }

  &-close {
    width: 164px;
    height: 44px;
    background: #f5f5f7;
    border-radius: 68px;
    opacity: 1;
    font-size: 16px;
    font-weight: 400;
    color: #333333;
    padding: 11px 50px;
    box-sizing: border-box;
  }

  &-pay {
    width: 164px;
    height: 44px;
    background: linear-gradient(90deg, #fa700d 0%, #f34747 100%);
    border-radius: 68px;
    opacity: 1;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    padding: 11px 50px;
    box-sizing: border-box;
  }
}
</style>
