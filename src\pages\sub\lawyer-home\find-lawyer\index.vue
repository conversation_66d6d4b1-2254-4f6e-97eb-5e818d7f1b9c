<template>
  <div>
    <u-sticky :zIndex="99999">
      <div :style="[tabsStyle]">
        <div
          id="lawyer-tabs"
          class="sticky-truck tabbar flex flex-align-center flex-space-center"
        >
          <!-- 当地律师不展示可选择城市tab -->
          <div
            v-if="!optionCityName"
            class="bar flex flex-align-center flex-space-center flex-1"
            @click="changePopupState('locationState')"
          >
            <p class="text-ellipsis city-type">
              {{ params.cityName || "全部" }}
            </p>
            <img
              alt=""
              src="@/pages/findlawyer/imgs/<EMAIL>"
            >
          </div>
          <div
            class="bar flex flex-align-center flex-space-center flex-1"
            @click="changePopupState('goodAtTypeState')"
          >
            <p>{{ params.speciality || "擅长类型" }}</p>
            <img
              alt=""
              src="@/pages/findlawyer/imgs/<EMAIL>"
            >
          </div>
          <div
            class="bar flex flex-align-center flex-space-center flex-1"
            @click="changePopupState('popupSortState')"
          >
            <p>{{ params.sortName || "综合排序" }}</p>
            <img
              alt=""
              src="@/pages/findlawyer/imgs/<EMAIL>"
            >
          </div>
        </div>
      </div>
    </u-sticky>
    <div>
      <div
        v-if="lawyerList.length === 0 && reqState"
        class="placeholder"
      >
        <img
          alt=""
          src="@/pages/findlawyer/imgs/no-lawyer-new.png"
        >
      </div>
      <div class="pd-bm-20">
        <div class="lawyer-card">
          <div
            v-for="i in lawyerList"
            :key="i.id"
            class="lawyer-card-item"
          >
            <findlawyer-item-plus :data="i" />
          </div>
        </div>
      </div>
    </div>
    <popup-location
      :show.sync="popupState.locationState"
      :transitionStyleProps="{ top: `${tabsTop}px` }"
      nationwide
      @setCity="setCity"
    />
    <u-popup
      :duration="0"
      :show="popupState.goodAtTypeState"
      :transitionStyleProps="{ top: `${tabsTop}px` }"
      bgColor="transparent"
      mode="top"
      @close="changePopupState('goodAtTypeState', false)"
    >
      <!-- <div class="background-white type-list flex flex-wrap">
        <div
          v-for="i in goodAtType"
          :key="i.value"
          :class="{ active: params.specialityCode === i.value }"
          class="item"
          @click="handleGoodAtType(i)"
        >
          {{ i.label }}
        </div>
      </div> -->
      <div class="lawyer-type-box">
        <LawyerTypes
          :list="goodAtType"
          @getCaseType="getCaseTypeFun"
        />
      </div>
    </u-popup>
    <popup-sort
      :show.sync="popupState.popupSortState"
      :tabsTop="tabsTop"
      @handleSwitch="handleSwitchSort"
    />
    <telephone-consultation-popup />
  </div>
</template>

<script>
import PopupLocation from "@/components/popup-location/index.vue";
import { lawyerSpeciality } from "@/api";
import { currencyGetAddressByName, lawyerListV3 } from "@/api/findlawyer.js";
import PopupSort from "@/pages/findlawyer/components/popup-sort/index.vue";
import UPopup from "@/uview-ui/components/u-popup/u-popup.vue";
import headerTabsTop from "@/pages/findlawyer/mixins/headerTabsTop.js";
import USticky from "@/uview-ui/components/u-sticky/u-sticky.vue";
import FindlawyerItemPlus from "@/pages/findlawyer/components/findlawyer-item-plus/index.vue";
import LawyerTypes from "@/pages/findlawyer/components/find-lawyer-types/index.vue";
import TelephoneConsultationPopup from "@/components/telephone-consultation-popup/index.vue";
import { setNavigationBarTitle } from "@/libs/tools.js";

export default {
  name: "LawyerHomeFindLawyer",
  /* 律师列表*/
  components: {
    TelephoneConsultationPopup,
    USticky,
    UPopup,
    PopupSort,
    FindlawyerItemPlus,
    PopupLocation,
    LawyerTypes,
  },
  mixins: [headerTabsTop],
  data() {
    return {
      optionCityName: "", // 本地是否有定位城市
      popupState: {
        /* 定位 弹窗*/
        locationState: false,
        /* 擅长类型 弹窗*/
        goodAtTypeState: false,
        /* 排序 弹窗*/
        popupSortState: false,
      },
      /* 擅长类型*/
      goodAtType: [],
      /** 总数 */
      pageTotal: 0,
      /* 选择数据*/
      params: {
        specialityCode: "",
        speciality: "擅长类型",
        cityName: "全国",
        cityCode: "",
        proviceCode: "",
        sortName: "综合排名",
        sort: "",
      },
      pageParams: {
        /** 页码 */
        currentPage: 1,
        /** 每页条数 */
        pageSize: 20,
      },
      /* 律师列表*/
      lawyerList: [],
      /* 请求状态*/
      reqState: false,
    };
  },
  onLoad(options) {
    console.log("options:", options);
    if (options.typeLabel) {
      setNavigationBarTitle({
        title: options.typeLabel + " 精选律师",
      });
    }
    this.optionCityName = options.cityName || "";
    this.params.specialityCode = options.typeValue || "";

    if (this.optionCityName) {
      // uni.setNavigationBarTitle({
      //   title: `${this.optionCityName}本地好律师`,
      // });
    } else {
      this.params.sort = options.sort || 6;
    }
    this.lawyerList = [];
    const lastValue = uni.getStorageSync("cacheTypeValue");
    const city = uni.getStorageSync("geoData");
    this.params = {
      ...this.params,
      ...(city ? { cityName: city.cityName, cityCode: city.cityCode } : {}),
      ...(lastValue
        ? { specialityCode: lastValue.value, speciality: lastValue.label }
        : {}),
      cityName: "全国",
      cityCode: "",
    };
    lawyerSpeciality().then((data) => {
      this.goodAtType = data;
      if (options.typeValue) {
        const currentType = this.goodAtType.find(
          (item) => Number(item.value) === Number(options.typeValue)
        );
        if (currentType) this.params.speciality = currentType.label;
      }
    });
    // 如果是当地律师页面链接参数 就用当地的城市
    if (this.optionCityName) this.params.cityName = this.optionCityName;
    const paramsCity =
      this.params.cityName === "全国" ? "" : this.params.cityName;
    if (paramsCity && !this.params.cityCode) {
      try {
        /* 通过城市名称获取城市信息*/
        currencyGetAddressByName({
          cityName: paramsCity,
        }).then((res) => {
          this.params.cityCode = res.data.cityCode;
          this.reqList();
        });
      } catch (e) {
        console.log(e);
      }
    } else {
      this.reqList();
    }
  },
  onShow() {
    this.handleScroll();
  },
  computed: {
    /** 是否显示弹窗 */
    showPopup() {
      return (
        this.popupState.locationState ||
        this.popupState.goodAtTypeState ||
        this.popupState.popupSortState
      );
    },
    /** 顶部导航的样式 */
    tabsStyle() {
      if (!this.showPopup) return {};

      return uni.$u.addStyle({
        top: 0,
        right: 0,
        left: 0,
        position: "fixed",
      });
    },
  },
  onHide() {
    this.popupState = this.$options.data().popupState;
  },
  onPageScroll() {},
  onReachBottom() {
    this.scrollToLower();
  },
  mounted() {},
  methods: {
    /* 排行切换*/
    handleSwitchSort(data) {
      this.params = {
        ...this.params,
        sort: data.value,
        sortName: data.text,
      };
      this.pageParams = this.$options.data().pageParams;
      this.lawyerList = [];
      this.reqList();
    },
    changePopupState(key, state = true) {
      this.popupState = this.$options.data().popupState;
      this.popupState[key] = state;
    },
    /* 擅长类型选择*/
    handleGoodAtType({ value, label }) {
      this.params = {
        ...this.params,
        specialityCode: value,
        speciality: label,
      };
      this.popupState.goodAtTypeState = false;
      this.pageParams = this.$options.data().pageParams;
      this.lawyerList = [];
      this.reqList();
    },
    // 底部案件类型选择事件
    getCaseTypeFun(item) {
      this.handleGoodAtType(item);
    },
    /* 城市选择 包含定位*/
    setCity({ name, code, provinceName }) {
      this.params = {
        ...this.params,
        cityName: name === "不限" ? provinceName : name,
        cityCode: name === "不限" ? "" : code,
        proviceCode: name === "不限" ? code : "",
      };
      this.pageParams = this.$options.data().pageParams;
      this.lawyerList = [];
      this.reqList();
    },
    scrollToLower() {
      if (
        this.pageParams.currentPage * this.pageParams.pageSize >=
        this.pageTotal
      )
        return;

      this.pageParams.currentPage++;
      this.reqList();
    },
    reqList() {
      const { speciality, cityName, sort } = this.params;
      this.reqState = false;
      lawyerListV3({
        ...this.params,
        ...this.pageParams,
        sortName: "",
        speciality:
          speciality === "擅长类型" || speciality === "全部" ? "" : speciality,

        sort: sort === 1 ? "" : sort,
      })
        .then((res) => {
          this.lawyerList = [...this.lawyerList, ...(res.data.records || [])];
          this.pageTotal = res.data.total || 0;
        })
        .finally(() => {
          this.reqState = true;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.sticky-truck {
  background-color: #f5f5f7;
}

.lawyer-type-box {
  border-radius: 0 0 16px 16px;
  background: #f5f5f7;
}

.tabbar {
  height: 42px;
  width: 100%;
  z-index: 10079;
  border-bottom: 1px solid #f5f5f7;

  .bar {
    height: 42px;

    view {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
    }

    image {
      width: 12px;
      height: 12px;
      margin-left: 2px;
    }
  }

  .city-type {
    max-width: 100px;
  }
}

.placeholder {
  min-height: 100vh;
  padding-top: 40px;
  display: flex;
  justify-content: center;

  image {
    width: 255px;
    height: 256px;
  }
}

.lawyer-card {
  padding-left: 12px;
  padding-right: 12px;

  &-item {
    border-radius: 8px;
    overflow: hidden;

    &:not(:last-child) {
      margin-bottom: 12px;
    }
  }
}

.type-list {
  overflow: hidden;
  padding-bottom: 16px;
  border-radius: 0px 0px 16px 16px;

  .item {
    margin-top: 16px;
    margin-left: 16px;
    flex-shrink: 0;
    width: 74px;
    height: 28px;
    background: #f5f5f7;
    border-radius: 14px 14px 14px 14px;
    font-size: 12px;
    line-height: 28px;
    text-align: center;
    font-weight: 400;
    color: #333333;

    &.active {
      background: $theme-color;
      color: white;
    }
  }
}
</style>
