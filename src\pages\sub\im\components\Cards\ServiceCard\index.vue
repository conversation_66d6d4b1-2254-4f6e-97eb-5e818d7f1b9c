<template>
  <theme-layout-card :theme="theme">
    <p class="title">
      您好，根据您的咨询向您推荐以下服务
    </p>
    <div class="submissions-card flex">
      <img
        class="logo"
        :src="customExtsinExt.icon"
        alt=""
      >
      <div class="flex-1 flex flex-column flex-space-around">
        <p class="card-title">
          {{ customExts.title }}
        </p>
        <p class="look flex flex-align-center">
          {{ customExts.content }}
        </p>
      </div>
    </div>
  </theme-layout-card>
</template>

<script>
import ThemeLayoutCard from "@/pages/sub/im/components/Cards/ThemeLayoutCard/index.vue";
import cardProps, { customComputed } from "@/pages/sub/im/mixins/card-props.js";

export default {
  name: "ServiceCard",
  components: { ThemeLayoutCard },
  mixins: [cardProps, customComputed]
};
</script>

<style scoped lang="scss">
.title{
  padding-bottom: 8px;
}
.submissions-card{
  background: #FFFFFF;
  padding: 10px;
  border-radius: 8px 8px 8px 8px;
  .logo{
    width: 44px;
    height: 44px;
    padding-right: 12px;
  }
  .card-title{
    font-size: 14px;
    font-weight: 500;
    color: #333333;
  }
  .look{
    font-size: 12px;
    font-weight: 400;
    color: #999999;
  }
}
</style>
