.uno-start {
  --un: 0;
}

/* unocss 代码生成在这 */
.uno-end {
  --un: 0;
}


.text-ff8a36 {
  color: #F78C3E;
}

.text-eb4738 {
  color: #EB4738;
}

.text-3887f5 {
  color: #3887F5;
}

.text-f2af30 {
  color: #F2AF30;
}

.text-ffD17a {
  color: #ffd17a;
}

.text-ffa700 {
  color: #F2AF30;
}

.text-333333 {
  color: #333333;
}

.text-666666 {
  color: #666666;
}

.text-cccccc {
  color: #cccccc;
}

.text-c8c8c8 {
  color: #C8C8C8;
}

.text-bd934c {
  color: #bd934c;
}

.text-999999 {
  color: #999999;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.w-screen {
  width: 100vw;
}

.h-screen {
  height: 100vh;
}

.container {
  min-width: 1100px;
  width: 1100px;
  max-width: 1100px;
  overflow: hidden;
}

.mg-auto {
  margin: 0 auto;
}

.flex-vertically-centered {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-column-centered {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-space-between {
  justify-content: space-between;
}

.flex-space-around {
  justify-content: space-around;
}

.flex-space-center {
  justify-content: center;
}

.flex-space-end {
  justify-content: flex-end;
}

.flex-align-center {
  align-items: center;
}

.flex-align-end {
  align-items: flex-end;
}

.flex-1 {
  flex: 1;
}

.flex-2 {
  flex: 2;
}

.font-size-0 {
  font-size: 0;
}

.text-bold {
  font-weight: bold;
}

.cursor-pointer {
  cursor: pointer;
}

.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-wrap: break-word;
  word-break: break-all;
}

.text-ellipsis-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
}

.overflow-hidden {
  overflow: hidden;
}

.van-ellipsis {
  @extend .text-ellipsis;
}

.mg-tp-2 {
  margin-top: 2px;
}

.mg-tp-3 {
  margin-top: 3px;
}

.mg-tp-4 {
  margin-top: 4px;
}

.mg-tp-6 {
  margin-top: 6px;
}

.mg-tp-8 {
  margin-top: 8px;
}

.mg-tp-12 {
  margin-top: 12px;
}

.mg-tp-15 {
  margin-top: 15px;
}

.mg-tp-16 {
  margin-top: 16px;
}

.mg-tp-20 {
  margin-top: 20px;
}

.mg-tp-24 {
  margin-top: 24px;
}

.mg-tp-30 {
  margin-top: 30px;
}

.mg-l-10 {
  margin-left: 10px;
}

.mg-l-12 {
  margin-left: 12px;
}

.mg-l-14 {
  margin-left: 14px;
}

.mg-l-2 {
  margin-left: 2px;
}

.mg-l-4 {
  margin-left: 4px;
}

.mg-l-5 {
  margin-left: 5px;
}

.mg-l-8 {
  margin-left: 8px;
}

.mg-l-15 {
  margin-left: 15px;
}

.mg-l-16 {
  margin-left: 16px;
}

.mg-r-4 {
  margin-right: 4px;
}

.mg-r-8 {
  margin-right: 8px;
}

.mg-r-2 {
  margin-right: 2px;
}

.mg-r-7 {
  margin-right: 7px;
}

.mg-r-12 {
  margin-right: 12px;
}

.mg-r-15 {
  margin-right: 15px;
}

.mg-b-6 {
  margin-bottom: 6px;
}

.mg-b-8 {
  margin-bottom: 8px;
}

.mg-b-9 {
  margin-bottom: 9px;
}

.mg-b-12 {
  margin-bottom: 12px;
}

.mg-b-16 {
  margin-bottom: 16px;
}

.mg-b-20 {
  margin-bottom: 20px;
}

.mg-b-24 {
  margin-bottom: 24px;
}

.pd-tp-5 {
  padding-top: 5px;
}

.pd-tp-6 {
  padding-top: 6px;
}

.pd-tp-10 {
  padding-top: 10px;
}

.pd-tp-12 {
  padding-top: 12px;
}

.pd-tp-15 {
  padding-top: 15px;
}

.pd-tp-16 {
  padding-top: 16px;
}

.pd-tp-20 {
  padding-top: 20px;
}

.pd-tp-30 {
  padding-top: 30px;
}

.pd-lt-8 {
  padding-left: 8px;
}

.pd-lt-16 {
  padding-left: 16px;
}

.pd-lt-20 {
  padding-left: 20px;
}

.pd-lt-25 {
  padding-left: 25px;
}

.pd-rt-12 {
  padding-right: 12px;
}

.pd-rt-16 {
  padding-right: 16px;
}

.pd-lt-12 {
  padding-left: 12px;
}

.pd-rt-5 {
  padding-right: 5px;
}

.pd-lt-5 {
  padding-left: 5px;
}

.pd-rt-20 {
  padding-right: 20px;
}

.pd-bm-5 {
  padding-bottom: 5px;
}

.pd-bm-12 {
  padding-bottom: 12px;
}

.pd-bm-16 {
  padding-bottom: 16px;
}

.pd-bm-20 {
  padding-bottom: 20px;
}

.pd-bm-30 {
  padding-bottom: 30px;
}

.background-white {
  background: white;
}

.px-12 {
  padding-left: 12px;
  padding-right: 12px;
}

.px-16 {
  padding-left: 16px;
  padding-right: 16px;
}

.py-12 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.text-align-rt {
  text-align: right;
}

.text-align-center {
  text-align: center;
}

.sub-title {
  background: #fff;
  line-height: 40px;
  font-size: 14px;
  color: #46474b;
  font-weight: bold;
  padding: 0 16px;
  border-bottom: 1px solid #ececec;
}

.position-relative {
  position: relative;
  z-index: 0;
}

.position-absolute {
  position: absolute;
}

.position-fixed {
  position: fixed;
}

.font11 {
  font-size: 12px;
  transform: scale(0.9);
  transform-origin: left center;
}

.font12 {
  font-size: 12px;
}

.font13 {
  font-size: 13px;
}

.font14 {
  font-size: 14px;
}

.font15 {
  font-size: 15px;
}

.font16 {
  font-size: 16px;
}

.font17 {
  font-size: 17px;
}

.font18 {
  font-size: 18px;
}

.font19 {
  font-size: 19px;
}

.font20 {
  font-size: 20px;
}

.font22 {
  font-size: 22px;
}

.line-12 {
  height: 12px;
  background: #f5f5f7;
}

.line-1 {
  height: 1px;
  background: #f5f5f7;
}

.text-align-left {
  text-align: left;

  .van-dialog__message {
    text-align: left;
  }
}

.none-data-placeholder-parent {
  text-align: center;
  position: relative;

  .none-data-placeholder {
    width: 7rem;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
  }
}

// <editor-fold desc="grid相关">
.grid {
  display: grid;
}

.gap-0 {
  gap: 0;
}

.gap-10 {
  gap: 10px;
}

.gap-20 {
  gap: 20px;
}

.gap-x-0 {
  column-gap: 0;
}

.gap-x-10 {
  column-gap: 10px;
}

.gap-x-20 {
  column-gap: 20px;
}

.gap-y-0 {
  row-gap: 0;
}

.gap-y-10 {
  row-gap: 10px;
}

.gap-y-20 {
  row-gap: 20px;
}

.grid-flow-row {
  grid-auto-flow: row;
}

.grid-flow-col {
  grid-auto-flow: column;
}

.grid-flow-row-dense {
  grid-auto-flow: row dense;
}

.grid-flow-col-dense {
  grid-auto-flow: column dense;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}

.grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}

.grid-cols-9 {
  grid-template-columns: repeat(9, minmax(0, 1fr));
}

.grid-cols-10 {
  grid-template-columns: repeat(10, minmax(0, 1fr));
}

.grid-cols-11 {
  grid-template-columns: repeat(11, minmax(0, 1fr));
}

.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

.grid-cols-none {
  grid-template-columns: none;
}

.auto-cols-auto {
  grid-auto-columns: auto;
}

.auto-cols-min {
  grid-auto-columns: min-content;
}

.auto-cols-max {
  grid-auto-columns: max-content;
}

.auto-cols-fr {
  grid-auto-columns: minmax(0, 1fr);
}

.col-auto {
  grid-column: auto;
}

.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.col-span-4 {
  grid-column: span 4 / span 4;
}

.col-span-5 {
  grid-column: span 5 / span 5;
}

.col-span-6 {
  grid-column: span 6 / span 6;
}

.col-span-7 {
  grid-column: span 7 / span 7;
}

.col-span-8 {
  grid-column: span 8 / span 8;
}

.col-span-9 {
  grid-column: span 9 / span 9;
}

.col-span-10 {
  grid-column: span 10 / span 10;
}

.col-span-11 {
  grid-column: span 11 / span 11;
}

.col-span-12 {
  grid-column: span 12 / span 12;
}

.col-span-full {
  grid-column: 1 / -1;
}

.col-start-1 {
  grid-column-start: 1;
}

.col-start-2 {
  grid-column-start: 2;
}

.col-start-3 {
  grid-column-start: 3;
}

.col-start-4 {
  grid-column-start: 4;
}

.col-start-5 {
  grid-column-start: 5;
}

.col-start-6 {
  grid-column-start: 6;
}

.col-start-7 {
  grid-column-start: 7;
}

.col-start-8 {
  grid-column-start: 8;
}

.col-start-9 {
  grid-column-start: 9;
}

.col-start-10 {
  grid-column-start: 10;
}

.col-start-11 {
  grid-column-start: 11;
}

.col-start-12 {
  grid-column-start: 12;
}

.col-start-13 {
  grid-column-start: 13;
}

.col-start-auto {
  grid-column-start: auto;
}

.col-end-1 {
  grid-column-end: 1;
}

.col-end-2 {
  grid-column-end: 2;
}

.col-end-3 {
  grid-column-end: 3;
}

.col-end-4 {
  grid-column-end: 4;
}

.col-end-5 {
  grid-column-end: 5;
}

.col-end-6 {
  grid-column-end: 6;
}

.col-end-7 {
  grid-column-end: 7;
}

.col-end-8 {
  grid-column-end: 8;
}

.col-end-9 {
  grid-column-end: 9;
}

.col-end-10 {
  grid-column-end: 10;
}

.col-end-11 {
  grid-column-end: 11;
}

.col-end-12 {
  grid-column-end: 12;
}

.col-end-13 {
  grid-column-end: 13;
}

.col-end-auto {
  grid-column-end: auto;
}

.grid-rows-1 {
  grid-template-rows: repeat(1, minmax(0, 1fr));
}

.grid-rows-2 {
  grid-template-rows: repeat(2, minmax(0, 1fr));
}

.grid-rows-3 {
  grid-template-rows: repeat(3, minmax(0, 1fr));
}

.grid-rows-4 {
  grid-template-rows: repeat(4, minmax(0, 1fr));
}

.grid-rows-5 {
  grid-template-rows: repeat(5, minmax(0, 1fr));
}

.grid-rows-6 {
  grid-template-rows: repeat(6, minmax(0, 1fr));
}

.grid-rows-none {
  grid-template-rows: none;
}

.auto-rows-auto {
  grid-auto-rows: auto;
}

.auto-rows-min {
  grid-auto-rows: min-content;
}

.auto-rows-max {
  grid-auto-rows: max-content;
}

.auto-rows-fr {
  grid-auto-rows: minmax(0, 1fr);
}

.row-auto {
  grid-row: auto;
}

.row-span-1 {
  grid-row: span 1 / span 1;
}

.row-span-2 {
  grid-row: span 2 / span 2;
}

.row-span-3 {
  grid-row: span 3 / span 3;
}

.row-span-4 {
  grid-row: span 4 / span 4;
}

.row-span-5 {
  grid-row: span 5 / span 5;
}

.row-span-6 {
  grid-row: span 6 / span 6;
}

.row-span-full {
  grid-row: 1 / -1;
}

.row-start-1 {
  grid-row-start: 1;
}

.row-start-2 {
  grid-row-start: 2;
}

.row-start-3 {
  grid-row-start: 3;
}

.row-start-4 {
  grid-row-start: 4;
}

.row-start-5 {
  grid-row-start: 5;
}

.row-start-6 {
  grid-row-start: 6;
}

.row-start-7 {
  grid-row-start: 7;
}

.row-start-auto {
  grid-row-start: auto;
}

.row-end-1 {
  grid-row-end: 1;
}

.row-end-2 {
  grid-row-end: 2;
}

.row-end-3 {
  grid-row-end: 3;
}

.row-end-4 {
  grid-row-end: 4;
}

.row-end-5 {
  grid-row-end: 5;
}

.row-end-6 {
  grid-row-end: 6;
}

.row-end-7 {
  grid-row-end: 7;
}

.row-end-auto {
  grid-row-end: auto;
}

.place-content-center {
  place-content: center;
}

.place-items-start {
  place-items: start;
}

.place-items-end {
  place-items: end;
}

.place-items-center {
  place-items: center;
}

.place-items-stretch {
  place-items: stretch;
}

.self-center {
  align-self: center;
}

.place-self-center {
  place-self: center;
}

//</editor-fold>

.text-center {
  text-align: center;
}

.font-thin {
  font-weight: 100;
}

.font-extralight {
  font-weight: 200;
}

.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: bold;
}

.font-semibold {
  font-weight: bold;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-black {
  font-weight: 900;
}

.border-box {
  box-sizing: border-box;
}

// 背景图
.background-image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}

// 背景图容器
.background-container {
  position: relative;
  z-index: 999;
}

.button-animation {
  animation: keyScale 0.5s infinite alternate;
}

@keyframes keyScale {
  0% {
    transform: scale(0.94);
  }
  100% {
    transform: scale(1);
  }
}

.text-align-justify {
  text-align: justify;
  position: relative;
  top: 1PX;

  &:after {
    content: '';
    display: inline-block;
    width: 100%;
  }
}

// 白色
.tabs-mark-white {
  width: 44px;
  height: 44px;
  // 从左往右渐变
  background: linear-gradient(
                  90deg,
                  rgba(255, 255, 255, 0) 0%,
                  rgba(255, 255, 255, 1) 100%
  );
  opacity: 1;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;
}

// 灰色
.tabs-mark-gray {
  width: 44px;
  height: 44px;
  // 从左往右渐变灰色
  background: linear-gradient(
                  90deg,
                  rgba(245, 245, 247, 0) 0%,
                  rgba(245, 245, 247, 1) 100%
  );
  opacity: 1;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;
}

.color-text {
  color: #3887F5;
}

.absolute-x-center {
  left: 50%;
  transform: translateX(-50%);
}
.absolute-y-center{
  top: 50%;
  transform: translateY(-50%);
}

.absolute-center {
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}