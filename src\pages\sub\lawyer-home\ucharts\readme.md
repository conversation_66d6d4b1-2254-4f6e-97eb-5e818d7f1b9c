

## <font color='red'>写给uCharts使用者的一封信</font>
<font color='red'>
亲爱的用户：

- 由于最近上线的官网中实行了部分收费体验，收到了许多用户的使用反馈，大致反馈的问题都指向同一矛头：为何新官网的在线工具也要收费？对于这件事，我们深表歉意。由于新官网本身未提供技术文档，使得用户误以为我们对文档实行了收费。经我们连夜整改，新官网目前已经将技术文档开放出来供大家阅读使用，并免费对外开放了【演示】中的查看全端全平台的代码的功能，为此再次向所受影响的用户们致以诚恳的歉意。

- 其次，我们须澄清几点，如下：
1. uCharts的插件本身遵循开源原则，并不收费，用户可自行到DCloud市场与Gitee码云上获取源码
2. uCharts的技术文档永久对用户开放
3. 收费内容仅针对原生工具、组件工具、定制功能以及模板市场的部分收费模板

- uCharts为什么实行收费原则？
1. 服务器的费用支撑
2. 团队的运营支出；正如你所见，我们的群里有大量的用户在请教图表配置与反馈问题，群里的每一位管理员都在花费不少精力在积极解决用户的问题，然而遇到巨大的咨询量时，我们无法及时、精准解答回复，因此，我们推出了会员优先服务
3. 与其说模板市场是收费，倒不如说给野生用户提供了创造价值的机会，用户既可以在上面发布模板赚取费用，遇到心动的模板也能免费/付费使用

- 收费不是目的，正如你们所见，用户可以申请成为[【开发者】](https://www.ucharts.cn/v2/#/agreement/developer)，开发者不限制任何官网功能，并享有官方指导、开发、改造uCharts的权力，并且活动期间【返还超级会员费用】！我们想说的是，我们新版官网上线旨在希望更多的用户加入到开发者的队伍，我们共同去维护uCharts！
       
我们相信：星星之火可以燎原！

uCharts技术团队

2022.4.23

</font>


![logo](https://img-blog.csdnimg.cn/4a276226973841468c1be356f8d9438b.png)


[![star](https://gitee.com/uCharts/uCharts/badge/star.svg?theme=gvp)](https://gitee.com/uCharts/uCharts/stargazers)
[![fork](https://gitee.com/uCharts/uCharts/badge/fork.svg?theme=gvp)](https://gitee.com/uCharts/uCharts/members)
[![License](https://img.shields.io/badge/license-Apache%202-4EB1BA.svg)](https://www.apache.org/licenses/LICENSE-2.0.html)
[![npm package](https://img.shields.io/npm/v/@qiun/ucharts.svg?style=flat-square)](https://www.npmjs.com/~qiun)


## uCharts简介

`uCharts`是一款基于`canvas API`开发的适用于所有前端应用的图表库，开发者编写一套代码，可运行到 Web、iOS、Android（基于 uni-app / taro ）、以及各种小程序（微信/支付宝/百度/头条/飞书/QQ/快手/钉钉/淘宝）、快应用等更多支持 canvas API 的平台。

## 官方网站

## [https://www.ucharts.cn](https://www.ucharts.cn)

## 快速体验

一套代码编到多个平台，依次扫描二维码，亲自体验uCharts图表跨平台效果！其他平台请自行编译。

![](https://www.ucharts.cn/images/web/guide/qrcode20220224.png)

## 致开发者

感谢各位开发者`四年`来对秋云及uCharts的支持，uCharts的进步离不开各位开发者的鼓励与贡献。为更好的帮助各位开发者使用图表工具，我们推出了新版官网，增加了在线定制、问答社区、在线配置等一些增值服务，为确保您能更好的应用图表组件，建议您先`仔细阅读本页指南`以及`常见问题`，而不是下载下来`直接使用`。如仍然不能解决，请到`官网社区`或开通会员后加入`专属VIP会员群`提问将会很快得到回答。

## 社群支持

uCharts官方拥有4个2000人的QQ群及专属VIP会员群支持，庞大的用户量证明我们一直在努力，请各位放心使用！uCharts的开源图表组件的开发，团队付出了大量的时间与精力，经过四来的考验，不会有比较明显的bug，请各位放心使用。如果您有更好的想法，可以在`码云提交Pull Requests`以帮助更多开发者完成需求，再次感谢各位对uCharts的鼓励与支持！

#### 官方交流群
- 交流群1：371774600（已满）
- 交流群2：619841586（已满）
- 交流群3：955340127（已满）
- 交流群4：641669795
- 口令`uniapp`

#### 专属VIP会员群
- 开通会员后详见【账号详情】页面中顶部的滚动通知
- 口令`您的用户ID`

## 版权信息

uCharts始终坚持开源，遵循 [Apache Licence 2.0](https://www.apache.org/licenses/LICENSE-2.0.html) 开源协议，意味着您无需支付任何费用，即可将uCharts应用到您的产品中。

注意：这并不意味着您可以将uCharts应用到非法的领域，比如涉及赌博，暴力等方面。如因此产生纠纷或法律问题，uCharts相关方及秋云科技不承担任何责任。

## 合作伙伴

[![DIY官网](https://www.ucharts.cn/images/web/guide/links/diy-gw.png)](https://www.diygw.com/)
[![HasChat](https://www.ucharts.cn/images/web/guide/links/haschat.png)](https://gitee.com/howcode/has-chat)
[![uViewUI](https://www.ucharts.cn/images/web/guide/links/uView.png)](https://www.uviewui.com/)
[![图鸟UI](https://www.ucharts.cn/images/web/guide/links/tuniao.png)](https://ext.dcloud.net.cn/plugin?id=7088)
[![thorui](https://www.ucharts.cn/images/web/guide/links/thorui.png)](https://ext.dcloud.net.cn/publisher?id=202)
[![FirstUI](https://www.ucharts.cn/images/web/guide/links/first.png)](https://www.firstui.cn/)
[![nProUI](https://www.ucharts.cn/images/web/guide/links/nPro.png)](https://ext.dcloud.net.cn/plugin?id=5169)
[![GraceUI](https://www.ucharts.cn/images/web/guide/links/grace.png)](https://www.graceui.com/)


## 更新记录

详见官网指南中说明，[点击此处查看](https://www.ucharts.cn/v2/#/guide/index?id=100)


## 相关链接
- [uCharts官网](https://www.ucharts.cn)
- [DCloud插件市场地址](https://ext.dcloud.net.cn/plugin?id=271)
- [uCharts码云开源托管地址](https://gitee.com/uCharts/uCharts) [![star](https://gitee.com/uCharts/uCharts/badge/star.svg?theme=gvp)](https://gitee.com/uCharts/uCharts/stargazers)
- [uCharts npm开源地址](https://www.ucharts.cn)
- [ECharts官网](https://echarts.apache.org/zh/index.html)
- [ECharts配置手册](https://echarts.apache.org/zh/option.html)
- [图表组件在项目中的应用 ReportPlus数据报表](https://www.ucharts.cn/v2/#/layout/info?id=1) 
