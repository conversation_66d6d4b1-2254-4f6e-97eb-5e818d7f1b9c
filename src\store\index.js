import Vue from "vue";
import Vuex from "vuex";
import user from "./modules/user";
import im from "@/store/modules/im.js";
import payState from "@/store/modules/payState.js";
import popupState from "@/store/modules/popupState";
import commonData from "@/store/modules/commonData";
import lawyerProfile from "@/store/modules/lawyerProfile";
import lawyerVerify from "@/store/modules/lawyerVerify";
import lawyerHome from "@/store/modules/lawyerHome";
import recentLawyer from "@/store/modules/recentLawyer";
import share from "@/store/modules/share";

Vue.use(Vuex);

const store = new Vuex.Store({
  state: {
    /* 全局转化路径*/
    transformationPath: "0",
    /** 转化路径链路 */
    transformationPathList: [],
  },
  mutations: {
    SET_TRANSFORMATION_PATH(state, pathCode) {
      state.transformationPath = pathCode;
    },
    SET_TRANSFORMATION_PATH_LIST(state, pathCodeList) {
      state.transformationPathList = pathCodeList;
    },
  },
  getters: {
    getTransformationPath: (state) => state.transformationPath,
    getTransformationPathList: (state) => state.transformationPathList,
  },
  actions: {},
  modules: {
    user,
    im,
    payState,
    popupState,
    commonData,
    lawyerProfile,
    lawyerVerify,
    lawyerHome,
    recentLawyer,
    share
  },
});

export default store;
