<template>
  <div>
    <div class="flex bg-white">
      <div
        v-for="(i, index) in tabList"
        :key="index"
        class="flex items-center flex-1 justify-center leading-[44px]"
        @click="handoff(index)"
      >
        <div
          :class="[{ active: activeTabIndex === index }]"
          class="text-[16px] text-[#999999] text-center items-center relative"
        >
          {{ i.name }}
          <!--红点  -->
          <p
            v-if="i.num > 0 && i.type !== currentTab.type"
            class="absolute top-[9px] right-[-4px] w-[8px] h-[8px] bg-[#F34747] rounded-[8px] border-[1px] border-solid border-[#FFFFFF]"
          />
        </div>
      </div>
    </div>
    <div
      v-if="isArrNull(list) && isRequest"
      class="pt-[114px] text-center"
    >
      <img
        alt=""
        class="w-[240px] h-[180px]"
        src="../../../message-notice/imgs/no-data3.png"
      >
      <p class="pt-[16px] text-[14px] text-[#666666]">
        一条消息都没有哦
      </p>
    </div>
    <div v-else>
      <div
        v-for="i in list"
        :key="i.id"
        class="px-[12px]"
      >
        <notice-card
          :data="i"
          :showAvatar="false"
          :type="currentTab.type"
        />
      </div>
      <u-divider
        v-if="isLastPage"
        text="已经是最后一条了"
      />
      <u-safe-bottom />
    </div>
  </div>
</template>

<script>
import { toCollaborationDetails } from "@/libs/turnPages";
import { isArrNull, isNull } from "@/libs/basics-tools";
import { SYSTEM_MSG_TYPE } from "@/enum/imCardEnum";
import { coreMessageAllRead, coreMessageList, coreMessageNoRead } from "@/api/im";
import { onReachBottomPage } from "@/libs/tools";
import UDivider from "@/uview-ui/components/u-divider/u-divider.vue";
import NoticeCard from "@/pages/sub/im/components/NoticeCard/index.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "MessageNotice",
  components: { USafeBottom, NoticeCard, UDivider },
  data() {
    return {
      activeTabIndex: 0,
      list: [],
      /** 是否请求过 */
      isRequest: false,
      isLastPage: false,
      onReachBottomPage: {},
      systemMsg: {
        //	否	Integer	互动未读消息数
        interactionNum: 0,
        serverNum: 0,
        //	否	Integer	系统未读消息数
        systemNum: 0,
      },
    };
  },
  computed: {
    SYSTEM_MSG_TYPE() {
      return SYSTEM_MSG_TYPE;
    },
    /* 当前选中的tab*/
    currentTab() {
      return this.tabList[this.activeTabIndex];
    },
    tabList() {
      return [
        {
          name: "系统通知",
          type: SYSTEM_MSG_TYPE.SYSTEM,
          /* 未读数 */
          num: this.systemMsg.systemNum,
        },
        {
          name: "服务助手",
          type: SYSTEM_MSG_TYPE.SERVER,
          /* 未读数 */
          num: this.systemMsg.serverNum,
        },
        {
          name: "互动消息",
          type: SYSTEM_MSG_TYPE.INTERACTION,
          /* 未读数 */
          num: this.systemMsg.interactionNum,
        },
      ];
    },
  },
  mounted() {
    this.resetData();
    this.getData();
  },
  onReachBottom() {
    this.getData();
  },
  onPullDownRefresh() {
    console.log("refresh");
    this.resetData();
    this.getData().finally(() => {
      uni.stopPullDownRefresh();
    });
  },
  methods: {
    isArrNull,
    getUnreadSystemMsg() {
      coreMessageNoRead().then(({ data }) => {
        Object.keys(this.systemMsg).forEach((key) => {
          if (!isNull(data[key])) this.systemMsg[key] = data[key];
        });
      });
    },
    handleClick({ data }) {
      toCollaborationDetails(data);
    },
    handoff(index) {
      if (this.activeTabIndex === index) return;
      this.activeTabIndex = index;
      this.resetData();
      this.getData();
    },
    jump() {
      this.currentTab.jump();
    },
    /* 注册分页 */
    registerPagination() {
      this.onReachBottomPage = {};
      this.onReachBottomPage = onReachBottomPage((data) => {
        return coreMessageList({
          ...data,
          messageCenterEnum: this.currentTab.type,
        });
      });
    },
    /** 请求数据 */
    getData() {
      return this.onReachBottomPage()
        .then(({ list = [], paginationState, paginationData }) => {
          console.log();
          this.isLastPage = paginationState.isEnd;
          this.list = [...this.list, ...list];
          if (paginationData.currentPage - 1 < 2 && !isArrNull(this.list)) {
            coreMessageAllRead({
              messageCenterEnum: this.currentTab.type,
            });
          }
          return list;
        })
        .finally(() => {
          this.isRequest = true;
        });
    },
    /** 重置数据 */
    resetData() {
      this.list = [];
      this.isLastPage = false;
      this.isRequest = false;
      this.getUnreadSystemMsg();
      this.registerPagination();
    },
  },
};
</script>

<style lang="scss" scoped>
.active {
  color: #000000;
  font-weight: bold;
  position: relative;

  &:after {
    position: absolute;
    content: " ";
    width: 14px;
    height: 3px;
    background: #3887f5;
    border-radius: 70px 70px 70px 70px;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
  }
}
</style>
