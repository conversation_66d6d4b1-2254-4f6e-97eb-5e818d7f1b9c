<template>
  <div class="position-relative">
<!--    <div
      class="tips"
      @click="turnToRechargePage"
    >
      <div>少花钱抢高分案源，抢先了解平台权益</div>
      <i class="iconfont icon-erjiyou<PERSON><PERSON><PERSON>" />
    </div>-->
    <img
      alt=""
      class="bg"
      src="img/bg.png"
    >
    <img
      alt=""
      class="icon"
      src="img/1.png"
    >
    <div class="status">
      认证审核中...
    </div>
    <div class="content">
      提交成功，平台会尽快审核完成
    </div>
    <div
      class="button"
      @click="turnToServiceCenterPage"
    >
      审核有问题？去联系客服解决
    </div>
  </div>
</template>

<script>
import { turnToRechargePage, turnToServiceCenterPage } from "@/libs/turnPages";

export default {
  name: "ProfileInputVerify",
  methods: { turnToRechargePage, turnToServiceCenterPage },
};
</script>

<style lang="scss" scoped>
.tips {
  box-sizing: border-box;
  width: 375px;
  height: 37px;
  background: #fff4e5;
  opacity: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  font-size: 12px;
  font-weight: 400;
  color: #f78c3e;

  .iconfont {
    font-size: 16px;
    color: #666666;
  }
}

.bg {
  display: block;
  width: 375px;
  height: 240px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}

.icon {
  display: block;
  width: 180px;
  height: 180px;
  margin: 0 auto;
}

.status {
  margin-top: 16px;
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  text-align: center;
}

.content {
  margin-top: 16px;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  text-align: center;
}

.button {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 24px auto 0 auto;
  width: 214px;
  height: 32px;
  box-sizing: border-box;
  background: linear-gradient(116deg, #71b5ff 0%, #2676e4 100%);
  border-radius: 68px;
  opacity: 1;
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
}
</style>
