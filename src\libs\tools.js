import store from "@/store/index.js";
import { isArray, isArrNull, isNull, isObj, isString } from "@/libs/basics-tools.js";
import { getUserTokenStorage } from "@/libs/token.js";
import dayjs from "dayjs";
import { getCurrentPageRoute, turnToWebViewPage } from "@/libs/turnPages";
import { requestUserInfoCallback } from "@/store/modules/user";
import { shareDataDictionary } from "@/api";
import { WebIM } from "@/plugins/im";

/**
 * 判断是否登录 需要登录的事件都需要走一步
 * @param nextCallback 登陆后触发的回调
 * @param options 以后可能会用到的参数
 * @returns {Promise<never>|Promise<boolean>}
 */
export function whetherToLogIn(nextCallback, options = {}) {
  console.log("store.getters[\"user/getToken\"]", store.getters["user/getToken"]);
  if (!store.getters["user/getToken"]) {
    store.commit("popupState/SET_LOGIN_POPUP_STATE", true);
    store.commit("user/SET_LOGIN_CALL_BACK", nextCallback);
  } else {
    requestUserInfoCallback(nextCallback);
  }
}

/**
 * 微信小程序分享
 * @param {string} [title] 转发标题
 * @param {string} [path] 转发路径
 * @param {string | null} [imageUrl] 自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径。支持PNG及JPG。显示图片长宽比是 5:4。
 * @param {string | null} [scImgUrl] 支付宝分享图片 自定义社交图片链接，作为分享到支付宝好友时的主体图片。建议尺寸 376x330。
 * @param {string | null} [bgImgUrl] 支付宝分享图片 自定义分享预览大图，建议尺寸 750x825，支持：网络图片路径；不支持：base64。
 */
export function shareAppMessageNew({
  title,
  imageUrl,
  path,
  scImgUrl,
  bgImgUrl
} = {}) {
  // 设置分享状态
  store.commit("share/SET_SHARING_STATUS", true);

  // 如果path为空，或者path长度小于等于0 则默认为当前页面的路径
  if (isNull(path)) path = getCurrentPageRoute().fullPath;

  if (isNull(imageUrl)) imageUrl = null;

  if (!isNull(bgImgUrl)) bgImgUrl = null;

  if (isNull(scImgUrl)) scImgUrl = null;

  const channelId = uni.getStorageSync("shareChannelId");

  let sharePath = path;

  if (channelId) {
    sharePath =
      sharePath +
      (sharePath.includes("?") ? "&" : "?") +
      `channelId=${channelId}`;
  }

  console.log("分享path：", sharePath);

  return {
    title,
    imageUrl,
    /* 兼容支付宝*/
    /* #ifdef  MP-ALIPAY*/
    bgImgUrl,
    scImgUrl,
    /* #endif*/
    path: sharePath
  };
}

/**
 * @description 通过字典获取分享配置
 * @date 2024/4/23
 * @param {string} key 字典key
 * @return
 */
export const requestShareAppMessage = async (key) => {
  const { remark: data } =
  (await shareDataDictionary()).find((item) => item.value === key) || {};

  return shareAppMessageNew(data);
};

export const setNavigationBarTitle = ({
  frontColor = "#000000",
  title = "",
  backgroundColor = "#f5f5f7"
} = {}) => {
  // #ifdef MP-ALIPAY
  my.setNavigationBar({
    title,
    backgroundColor,
    frontColor
  });
  return false;
  // #endif
  uni.setNavigationBarTitle({
    title
  });

  uni.setNavigationBarColor({
    backgroundColor,
    frontColor
  });
};

/* 手机号验证*/
export function validateMobile(value) {
  console.log(value);
  const reg = /^1\d{10}$/;
  if (!value) {
    return Promise.reject("请输入手机号码");
  } else if (!reg.test(value)) {
    return Promise.reject("请输入正确的手机号码");
  } else {
    return Promise.resolve();
  }
}

/* 手机验证码*/
export function validateCode(value) {
  const reg = /^\d{4}$/;

  if (!value) {
    return Promise.reject("请输入验证码");
  } else if (!reg.test(value)) {
    return Promise.reject("请输入正确的验证码");
  } else {
    return Promise.resolve();
  }
}

/**
 * @description:首字母大写
 * @author:djsong
 * @date:2021/6/16
 * @param:str 字符串
 * @return:str 字符串
 */
export const titleCase = (str) => {
  // 把字符串所有的字母变为小写，并根据空格转换成字符数组
  const arr = str.toLowerCase().split(" ");
  // 遍历字符数组
  for (let i = 0; i < arr.length; i++) {
    // 把第一个字符变为大写
    arr[i] = arr[i][0].toUpperCase() + arr[i].substring(1, arr[i].length);
  }
  // 加上空格，返回原模式的字符串
  return arr.join(" ");
};

/**
 * 节流函数 用于防止多次点击 在最开始的时候执行
 * @param func
 * @param timeFrame
 * @return {(function(...[*]): void)|*}
 */
export const throttle = (func, timeFrame) => {
  let lastTime = 0;
  return function(...args) {
    const now = new Date();
    if (now - lastTime >= timeFrame) {
      func(...args);
      lastTime = now;
    }
  };
};

// 防抖
export const debounce = (func, wait) => {
  let timeout;
  return function() {
    let context = this;
    let args = arguments;
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(context, args);
    }, wait);
  };
};

export const judgeClient = () => {
  const system = uni.getSystemInfoSync();
  console.log(system);
  return system.osName;
};

/* 判断是否安卓*/
export const isAndroidClient = () => {
  return judgeClient() === "android";
};
/* 判断是否ios*/
export const isIOSClient = () => {
  return judgeClient() === "ios";
};
/* 判断是否钉钉*/
export const isDingTalk = () => navigator.userAgent.indexOf("DingTalk") > -1;

// 判断当前是不是微信
export const isWeiXin = () => {
  const ua = window.navigator.userAgent.toLowerCase();
  return ua.match(/MicroMessenger/i) == "micromessenger";
};

/**
 * 时间格式化
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string") {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time);
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), "/");
      }
    }

    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    return value.toString().padStart(2, "0");
  });
  return time_str;
}

export const parseTimeToDay = (val) => {
  console.log(val, "=======================");
  if (!val) return;
  const day = parseTime(val, "{d}");
  const nowDay = parseTime(new Date(), "{d}");
  console.log(val, day, nowDay, "=======================");
  if (Math.abs(day - nowDay) === 0) return "今天 " + parseTime(val, "{h}:{i}");
  if (Math.abs(day - nowDay) === 1) return "昨天 " + parseTime(val, "{h}:{i}");
  if (Math.abs(day - nowDay) >= 1) return parseTime(val, "{y}-{m}-{d} {h}:{i}");
  // const res = parseTime(val)
  // return res
};

/* 获取尺寸*/
export const findDimensions = () => {
  return new Promise((resolve, reject) => {
    uni.getSystemInfo({
      success: (res) => {
        resolve({
          height: res.windowHeight,
          width: res.windowWidth
        });
      }
    });
  });
};

/**
 * 实现页面定位，让不同宽度的页面定位到相同的位置
 * 计算滚动距离，以375的宽度为基准
 */
export function calculateScrollDistance(top = 0) {
  // 获取屏幕宽度
  const screenWidth = uni.getSystemInfoSync().screenWidth;

  return (top / 375) * screenWidth;
}

// 极速我的订单已支付中退款状态值转换
export const formatRefundOrder = (val) => {
  let obj = {
    [speedRefundType.UNREFUNDED]: { txt: "退款", color: "#333333" },
    [speedRefundType.REFUNDING]: { txt: "退款中", color: "#999999" },
    [speedRefundType.REFUNDSUCESS]: { txt: "退款详情", color: "#333333" },
    [speedRefundType.REFUNDFAILED]: { txt: "退款失败", color: "#999999" }
  };
  if (obj[val]) {
    return obj[val];
  } else {
    return val;
  }
};
/* 对象转化成字符串*/
const stringify = (obj = {}) => {
  let str = "";
  Object.keys(obj).forEach(function(key) {
    str += key + "=" + obj[key] + "&";
  });
  return isNull(str) ? str : str.slice(0, str.length - 1);
};

/**
 * 计算时间与今天的差值，返回格式为 今天  或者  昨天 或者 日期
 * @param time moment支持的时间格式
 */
export const parseTimeDiffToday = (time) => {
  const date = dayjs(time);
  const todayDate = dayjs();
  /** 判断是今天 */
  if (date.isSame(todayDate, "day")) {
    return "今天" + date.format("HH:mm");
  }

  /** 判断是昨天 */
  if (date.isSame(todayDate.subtract(1, "day"), "day")) {
    return "昨天" + date.format("HH:mm");
  }

  return date.format("MM-DD HH:mm");
};

export function requestSubscribeMessage() {
  return new Promise((resolve) => {
    console.log("触发订阅消息");

    if (uni.getStorageSync("subscribe")) {
      resolve();
      return;
    }
    const success = (res) => {
      // 订阅成功
      console.log("订阅成功", res);
      uni.setStorageSync("subscribe", "true");
      resolve(res);
    };
    const fail = (error) => {
      // 订阅失败
      console.log("订阅失败, 错误详情: ", error);
      resolve(error);
    };

    const complete = (res) => {
      // 订阅完成
      console.log("订阅API调用完成: ", res);
      resolve(res);
    };

    let tmplIds = [];

    // #ifdef MP-TOUTIAO
    tmplIds = [
      "MSG1358066efdd86e72c8f44ea2f9b5040be879cf514399",
      "MSG135806654dd39f778e76c4fc79f68d99b7c305215634"
    ];
    // #endif

    // #ifdef MP-WEIXIN
    tmplIds = [
      "90kjnxC6uNd4GckLQkCcQWQt0ykdF5kHZhGIpbTlDso",
      "JQNQuiH_edgmspPvsS4E6sP6teq2HADRpMwBYoUy2ac",
      "gt0vzpdGDhUWNaQMnCbYyzsOs1ONl2K7RlALNUIxiUE"
    ];
    // #endif

    if (isArrNull(tmplIds)) return resolve();

    uni.requestSubscribeMessage({
      // 需要填入开放平台申请的模版id，支持最多3个同类型模版
      tmplIds,
      success,
      fail,
      complete
    });
  });
}

/**
 * 微信小程序分享
 * @param {string} [title] 转发标题
 * @param {string} [path] 转发路径
 * @param {string} [imageUrl] 自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径。支持PNG及JPG。显示图片长宽比是 5:4。
 */
export function shareAppMessage({
  title,
  imageUrl,
  path = getCurrentPageRoute().fullPath
} = {}) {
  const channelId = uni.getStorageSync("shareChannelId");

  let sharePath = path;

  if (channelId) {
    sharePath =
      sharePath +
      (sharePath.includes("?") ? "&" : "?") +
      `channelId=${channelId}`;
  }

  console.log("分享path：", sharePath);

  // 设置分享状态
  store.commit("share/SET_SHARING_STATUS", true);

  return {
    title,
    imageUrl,
    path: sharePath
  };
}

/**
 * 微信小程序分享到朋友圈
 * @param {string} [title] 自定义标题，即朋友圈列表页上显示的标题
 * @param {string} [imageUrl] 自定义图片路径，可以是本地文件或者网络图片。支持 PNG 及 JPG，显示图片长宽比是 1:1。
 * @param {string} [query] 自定义页面路径中携带的参数，如 path?a=1&b=2 的 "?" 后面部分
 */
export function shareTimeline({ title, imageUrl, query }) {
  const channelId = uni.getStorageSync("shareChannelId");

  let shareQuery = query;

  if (channelId) {
    shareQuery = `channelId=${channelId}`;
  }

  return {
    title,
    imageUrl,
    query: shareQuery
  };
}

/**
 * 获取元素的数据
 * @param {string} selector
 * @return {Promise<NodeInfo>}
 */
export function getClientRect(selector) {
  return new Promise((resolve, reject) => {
    try {
      let query;
      // 如果this存在
      if (this) {
        query = uni.createSelectorQuery().in(this).select(selector);
      } else {
        query = uni.createSelectorQuery().select(selector);
      }

      query
        .boundingClientRect((res) => {
          return resolve(res);
        })
        .exec();
    } catch (e) {
      return reject(e);
    }
  });
}

/** 生成一个随机的id */
export function createRandomId() {
  return Math.random().toString(36).substr(2);
}

/** 判断是否没有登陆 */
export function isNotLogin() {
  return !(store.getters["user/getToken"] && getUserTokenStorage());
}

/**
 * 包装函数，为了解决接口同时调用的问题
 * @param {function} request 请求函数
 * @param {boolean} [isLogin] 是否需要登录
 * @param {number} [spacing] 请求间隔时间，单位为毫秒，默认5000毫秒
 * @return {function(*=): Promise<unknown>}
 */
export const wrapRequest = (request, { isLogin, spacing } = {}) => {
  /** 上次调用时间 */
  let lastTime = 0;
  /** 接口是否在请求中 */
  let isRequesting = false;
  /** 接口返回数据 */
  let responseData = {};

  /** 判断接口是否可以调用 */
  const canCall = () => {
    return lastTime === 0;
  };

  return function requestFunction(...data) {
    return new Promise((resolve, reject) => {
      // 如果要校验登陆
      if (isLogin) {
        if (!(store.getters["user/getToken"] && getUserTokenStorage())) return;
      }

      // 如果没有到调用时机，则直接返回上次的数据
      if (!canCall()) {
        // 如果还在请求中，则递归调用，直到请求完成
        if (isRequesting) {
          setTimeout(() => {
            requestFunction(...data).then((res) => {
              return resolve(res);
            });
          }, 100);

          // 这里不做任何 resolve reject 从而让递归一直进行下去
          return;
        }
        return resolve(responseData);
      }

      lastTime = spacing || 5000;
      isRequesting = true;
      // 如果到了调用时机，则调用接口
      request(...data)
        .then((res) => {
          lastTime = spacing || 5000;
          setTimeout(() => {
            // 5秒后才能再次调用
            lastTime = 0;
          }, spacing || 5000);
          // 保存数据
          responseData = res;
          return resolve(responseData);
        })
        .catch((err) => {
          // 如果接口调用错误，则0.5秒后就能再次调用
          setTimeout(() => {
            lastTime = 0;
          }, spacing || 500);
          return reject(err);
        })
        .finally(() => {
          isRequesting = false;
        });
    });
  };
};

// 判断当前时间是否在设置的夜间时间内
export function timeIsNight(minuteArr) {
  const nightStart = minuteArr[0] || 0;
  const nightEnd = minuteArr[1] || 480;
  const currentTime = new Date();
  const startNightTime = new Date();
  startNightTime.setHours(nightStart / 60); // 设置夜间开始时间的小时
  startNightTime.setMinutes(nightStart % 60); // 设置夜间开始时间的分钟

  const endNightTime = new Date();
  endNightTime.setHours(nightEnd / 60); // 设置夜间结束时间的小时
  endNightTime.setMinutes(nightEnd % 60); // 设置夜间结束时间的分钟

  if (startNightTime <= endNightTime) {
    // 夜间时间段没有跨越午夜
    return currentTime >= startNightTime && currentTime <= endNightTime;
  } else {
    // 夜间时间段跨越午夜
    return currentTime >= startNightTime || currentTime <= endNightTime;
  }
}

/* 将JSON.stringify 转成对象*/
export const parseJSON = (data) => {
  if (isString(data)) {
    try {
      const parseData = JSON.parse(data);
      if (typeof parseData === "object") {
        return parseData;
      } else {
        return data;
      }
    } catch (e) {
      return data;
    }
  } else if (isObj(data)) {
    Object.keys(data).forEach((key) => {
      data[key] = parseJSON(data[key]);
    });
  } else if (isArray(data)) {
    data.forEach((item, index) => {
      data[index] = parseJSON(item);
    });
  }
  return data;
};

/* 获取随机id*/
export const getRandomId = (key) => {
  let id = uni.getStorageSync(key) || "";
  if (isNull(id)) {
    id = createRandomId();
    uni.setStorageSync(key, id);
  }
  return id;
};

/* 下滑分页*/
export const onReachBottomPage = (api) => {
  const paginationState = {
    /* 请求状态*/
    requestState: false,
    /* 是否最后一页*/
    isEnd: false
  };
  const paginationData = {
    currentPage: 1,
    pageSize: 20
  };
  return () => {
    return new Promise((resolve, reject) => {
      const { isEnd, requestState } = paginationState;
      if (isEnd) return;
      if (requestState) {
        return;
      }
      paginationState.requestState = true;
      api(paginationData)
        .then((res) => {
          paginationState.requestState = false;
          const list = res.data;
          if (isArray(list)) {
            if (list.length < paginationData.pageSize) {
              paginationState.isEnd = true;
            }
            resolve({
              list,
              paginationState,
              paginationData
            });
          } else if (isObj(list)) {
            paginationState.isRealPagination = true;
            const { records = [], pages = 0 } = list;
            paginationState.pages = isNull(list.pages) ? 1 : list.pages;
            if (pages < paginationData.currentPage) {
              paginationState.isEnd = true;
            }
            resolve({
              list: records,
              paginationState,
              paginationData
            });
          }
          console.log(JSON.stringify(paginationState), "paginationState");
          paginationData.currentPage++;
        })
        .catch((res) => {
          paginationState.requestState = false;
          return reject(res);
        });
    });
  };
};

/* 将字符串 转换成正则*/
export const encodeReg = (text) => {
  const reg = /[\[\(\$\^\.\]\*\\\?\+\{\}\\|\)]/gi;

  return text.replace(reg, (key) => `\\${key}`);
};

/* 关键字替换*/
export const keywordReplace = (text, keywords = []) => {
  let testReg = false;
  for (const item of keywords) {
    const reg = new RegExp(encodeReg(item), "g");
    testReg = reg.test(text);
    if (testReg) {
      text = text.replace(reg, (...data) => {
        return "".padStart(data[0].length, "*");
      });
      return { text, testReg };
    }
  }
  return { text, testReg };
};

/* 传入数组 每页数量  返回分页函数 包括下一页 上一页*/
export const setPaginationTool = (arr, num) => {
  let page = 0;
  const total = Math.ceil(arr.length / num);
  return {
    /* 最后一页状态*/
    lastPageState: false,
    next() {
      if (page < total) {
        page++;
        this.lastPageState = page >= total;
        return arr.slice((page - 1) * num, page * num);
      } else {
        this.lastPageState = true;
        return [];
      }
    },
    prev() {
      if (page > 1) page--;
      else page = 1;
      return arr.slice((page - 1) * num, page * num);
    }
  };
};

/* 通过传入的value 返回数组或者对面的值*/
export const filterEnum = (data, value) => {
  if (isObj(data)) {
    return data[value];
  } else if (isArray(data)) {
    return data.find((item) => item.value === value);
  }
  return value;
};

export function toBannerAddress(addressUrl) {
  if (addressUrl) {
    if (addressUrl.indexOf("http") > -1) {
      turnToWebViewPage({ src: addressUrl });
    } else {
      uni.navigateTo({
        url: addressUrl
      });
    }
  }
}

/** 点击预览图片 */
export function handlePreviewImage(url) {
  uni.previewImage({
    current: url,
    urls: [url]
  });
}

/** 将px转换为rpx，主要是为了兼容支付宝小程序 */
export function pxToRpx(px) {
  if (!px) return 0;

  // #ifndef MP-ALIPAY
  return px * 2 + "rpx";
  // #endif

  // #ifdef MP-ALIPAY
  return px / 50 + "rem";
  // #endif
}

export function pxToScreenPx(rpx) {
  return (rpx / 375) * uni.getWindowInfo().windowWidth;
}

/**
 * 生成当天开始时间和结束时间
 * 格式：YYYYMMDD
 * @returns {{beginDate: string, endDate: string}}
 */
export function getDayTime(day = 0) {
  const endDate = dayjs().format("YYYYMMDD");
  // 往前推day天
  const beginDate = dayjs().subtract(day, "day").format("YYYYMMDD");

  return {
    beginDate,
    endDate
  };
}

export const priceNumber = (val, options = {}) => {
  const { decimal = true } = options;

  if (val) {
    if (decimal) return parseFloat((Number(val) / 100).toFixed(2));

    return Number(val) / 100;
  }

  return "0";
};

/** 法临币计算时需要 /100 */
export function flbAmount(val) {
  return priceNumber(val, { decimal: false });
}

/**
 * 清除登录相关的信息
 */
export function cleanLoginData() {
  store.dispatch("user/clearToken");
  store.commit("im/SET_IM_USER_INFO", {});
  WebIM.close();

  store.commit("recentLawyer/CLEAR_RECENT_LAWYER_LIST");
}


export function queryStringToObject(queryString) {
  if (!queryString) return {};

  // 去除开头的 ?
  if (queryString.startsWith("?")) {
    queryString = queryString.slice(1);
  }

  const pairs = queryString.split("&");
  const obj = {};

  for (const pair of pairs) {
    const [key, value] = pair.split("=");
    if (key !== undefined) {
      obj[key] = value || "";
    }
  }

  return obj;
}
/**
 * 判断是否需要绑定微信 需要绑定微信的事件都需要走一步
 * @param nextCallback 绑定后触发的回调
 * @param options 以后可能会用到的参数
 */
export function whetherToBindWechat(nextCallback, options = {}) {
  const { edit = false } = options;

  const userInfo = store.getters["user/getUserInfo"];

  if (edit) {
    store.commit("popupState/SET_WECHAT_BIND_POPUP_STATE", true);
    store.commit("user/SET_WECHAT_BIND_CALL_BACK", nextCallback);
  } else {
    if (userInfo.wechatId) {
      nextCallback?.();
    } else {
      store.commit("popupState/SET_WECHAT_BIND_POPUP_STATE", true);
      store.commit("user/SET_WECHAT_BIND_CALL_BACK", nextCallback);
    }
  }
}


export const scrollToTarget = ( { top = 9999999, duration = 100, selector = "" } = {}) => {
  uni.pageScrollTo({ duration: duration, ...(isNull(selector) ? { scrollTop: top } : { selector }) });
};

/* 对象排序*/
export const objKeySort = obj => {
  const newkey = Object.keys(obj).sort();
  const newObj = {};
  for (let i = 0; i < newkey.length; i++) {
    newObj[newkey[i]] = obj[newkey[i]];
  }
  return newObj;
};

/* 字符串脱敏 */
export const stringMask = (str, start = 0, end = 0) => {
  if (!str || typeof str !== "string") return "";

  const len = str.length;

  // 确保 start 和 end 不超过字符串长度
  start = Math.max(0, Math.min(start, len));
  end = Math.max(0, Math.min(end, len));

  const maskLength = len - start - end;

  if (maskLength <= 0) {
    return "*".repeat(len); // 整体替换
  }

  return str.slice(0, start) + "*".repeat(maskLength) + str.slice(-end);
};