import { clearUserTokenStorage, setUserTokenStorage } from "@/libs/token";
import { getUserInfo, setUserInfoAsync } from "@/api/user";
import { isNull } from "@/libs/basics-tools";
import { getCommonConfigKey } from "@/api";
import { getCurrentPlatform } from "@/libs/tool.js";
import { lawyerCertStatus } from "@/libs/config";
import { isNotLogin } from "@/libs/tools";
import { imGetImLimit } from "@/api/im";
import { communityGroupGetBenefitStatus } from "@/api/communityGroup";

/** 用户信息是否请求完成 */
let isRequestingComplete = false;

/** 用户信息在请求中存储回调 */
let callbackList = [];

/** 用户信息在请求完成回调 */
export const requestUserInfoCallback = (callback) => {
  // 如果已经完成请求，直接执行回调
  if (isRequestingComplete) callback?.();
  // 否则存储回调
  else callbackList.push(callback);
};

const state = () => {
  return {
    userInfo: {},
    token: "",
    uuid: "",
    channelId: "",
    loginCallback: "",
    wechatBindCallback: "",
    openid: null,
    /* 获取小程序审核开关 0 是审核中 1未审核*/
    appletAuditSwitch: "1",
    /* 联系律师次数 */
    contactLawyerConfig: {
      total: 6,
      /* 当前剩余次数 */
      remain: 0
    },
    /* 律师进群权益 */
    lawyerGroupConfig: {
      // 当前剩余权益次数
      remainingBenefitCount: 0,
      // 	每日最大权益次数
      dailyMaxBenefitCount: 0,
      // 今日已使用权益次数
      todayUsedBenefitCount: 0,
      // 今日已分享获得权益次数
      todaySharedBenefitCount: 0,
      // 每日最大分享获得权益次数
      dailyMaxShareBenefitCount: 0,
      // 是否可以获取二维码
      canGetQrCode: false,
      // 是否可以分享获得权益
      canShareForBenefit: false
    }

  };
};

const getters = {
  getUserInfo: (state) => state.userInfo,
  /**
   * 获取认证状态 1:认证审核中,2.已认证,3.未认证,4.认证失败
   * @param state
   * @return {number}
   */
  getCertStatus: (state) => Number(state.userInfo.certStatus),
  /**
   * 是否认证
   * @param state
   * @return {boolean} true 已认证 false 未认证
   */
  lawyerCertStatus: (state) => {
    return lawyerCertStatus(state.userInfo.certStatus);
  },
  getId: (state) => state.userInfo.id,
  /** 个人资料是否完成 */
  profileComplete: (state) => {
    return Number(state.userInfo.profileStatus) === 4;
  },
  /*
  * 是否是认证审核中
  * */
  isCertStatusAudit: (state) => state.userInfo.certStatus === 1,
  /* 未认证*/
  isCertStatusNot: (state) => state.userInfo.certStatus === 3,
  /* 认证失败*/
  isCertStatusFail: (state) => state.userInfo.certStatus === 4,
  getToken: (state) => state.token,
  getUuid: (state) => state.uuid,
  getChannelId: (state) => state.channelId,
  getLoginCallback: (state) => state.loginCallback,
  getWechatBindCallback: (state) => state.wechatBindCallback,
  getOpenid: (state) => state.openid,
  /* 未在审核*/
  appletNotUnderReview: (state) => state.appletAuditSwitch === "1",
  /* 获取当日联系律师剩余次数 */
  getRemainContactLawyer: (state) => state.contactLawyerConfig.remain,
  /* 获取当日联系律师总次数 */
  getTotalContactLawyer: (state) => state.contactLawyerConfig.total,
  /* 联系次数是否超限 */
  isContactLawyerOverLimit: (state) => state.contactLawyerConfig.remain <= 0,
  /* 获取进群权益 */
  getLawyerGroupConfig: (state) => state.lawyerGroupConfig,
};

const mutations = {
  SET_USER_INFO(state, data = {}) {
    state.userInfo = data;
  },
  UPDATE_NICKNAME(state, nickName) {
    state.userInfo.nickName = nickName;
  },
  SET_TOKEN(state, token) {
    state.token = token;
  },
  SET_UUID(state, uuid) {
    state.uuid = uuid;
  },
  SET_CHANNEL_ID(state, ChannelId) {
    state.channelId = ChannelId;
  },
  SET_LOGIN_CALL_BACK(state, loginCallback) {
    let callback = loginCallback;
    if (isNull(loginCallback)) {
      callback = () => {
      };
    }
    state.loginCallback = callback;
  },
  SET_WECHAT_BIND_CALL_BACK(state, wechatBindCallback) {
    let callback = wechatBindCallback;
    if (isNull(wechatBindCallback)) {
      callback = () => {
      };
    }
    state.wechatBindCallback = callback;
  },
  SET_OPENID(state, openid) {
    state.openid = openid;
  },
  SET_APPLET_AUDIT_SWITCH(state, appletAuditSwitch) {
    state.appletAuditSwitch = appletAuditSwitch;
  },
  /* 设置当前联系次数 */
  SET_REMAIN_CONTACT_LAWYER(state, data) {
    state.contactLawyerConfig = data;
  },
  /* 设置进群权益 */
  SET_LAWYER_GROUP_CONFIG(state, data) {
    state.lawyerGroupConfig = {
      ...state.lawyerGroupConfig,
      ...data
    };
  }

};

const actions = {
  /**
   * 获取用户信息
   * 由于 b 端和 c 端不同，b端多了用户认证
   * 所以该用户信息会频繁进行获取，保证用户认证状态能够及时进行刷新
   * @param commit
   * @returns {Promise<void>}
   */
  setUserInfo({ commit }) {
    // 如果没有登陆，则不请求
    if (isNotLogin()) return;

    isRequestingComplete = false;
    return getUserInfo()
      .then(({ data }) => {
        console.log(data);
        commit("SET_USER_INFO", data);
        return data;
      })
      .catch(() => {
        this.dispatch("user/clearToken");
      })
      .finally(() => {
        isRequestingComplete = true;

        // 执行回调
        callbackList.forEach((callback) => callback?.());

        // 清空回调
        callbackList = [];
      });
  },
  // 修改用户信息
  updateUserInfo({ commit }, payload) {
    console.log("payload----", payload);
    return setUserInfoAsync({
      ...payload,
    }).then((data) => {
      commit("UPDATE_NICKNAME", payload.nickName);
      return data;
    });
  },
  // 清除用户信息
  clearUserInfo({ commit }) {
    commit("SET_USER_INFO");
  },
  setToken({ commit }, token) {
    setUserTokenStorage(token);
    commit("SET_TOKEN", token);
  },
  setUuid({ commit }, uuid) {
    // setUuidStorage(uuid); // 函数未定义，暂时注释
    commit("SET_UUID", uuid);
  },
  setChannelId({ commit }, ChannelId) {
    // setChannelIdStorage(ChannelId); // 函数未定义，暂时注释
    commit("SET_CHANNEL_ID", ChannelId);
  },
  clearToken({ commit }) {
    clearUserTokenStorage();
    commit("SET_TOKEN", "");
    commit("SET_USER_INFO");
  },
  setOpenid({ commit }, openid) {
    uni.setStorageSync("openid", openid);
    commit("SET_OPENID", openid);
  },
  setAppletAuditSwitch({ commit }) {
    /* 获取小程序审核开关 0 是审核中 1未审核 */
    getCommonConfigKey({
      paramName: "applet_audit_switch",
    }).then(({ data }) => {
      try {
        const paramValue = JSON.parse(data.paramValue);
        if (!isNull(paramValue[getCurrentPlatform()]))
          commit("SET_APPLET_AUDIT_SWITCH", paramValue[getCurrentPlatform()]);
      } catch (e) {
        console.log(e);
      }
    });
  },
  /* 更新当前律师次数 */
  updateRemainContactLawyer({ commit }) {
    return imGetImLimit({ type: 12 }).then(({ data = {} }) => {
      console.log(data);
      commit("SET_REMAIN_CONTACT_LAWYER", {
        total: isNull(data.dayLimit) ? 0 : data.dayLimit,
        remain: isNull(data.dayRemain) ? 0 : data.dayRemain,
      });
      return data;
    });
  },
  /* 更新进群权益 */
  updateLawyerGroupConfig({ commit }) {
    return communityGroupGetBenefitStatus().then(({ data = {} }) => {
      console.log(data);
      commit("SET_LAWYER_GROUP_CONFIG", data);
      return data;
    });
  },

};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
