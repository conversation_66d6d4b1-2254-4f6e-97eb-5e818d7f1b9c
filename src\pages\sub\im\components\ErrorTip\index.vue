<template>
  <div
    v-if="showState"
    :style="[errorTipStyle]"
    class="flex error-tip flex-align-center flex-space-between"
  >
    <i class="iconfont icon-tixing" />
    <p class="text flex flex-1">
      <slot />
    </p>
    <i
      v-if="closeIcon"
      class="iconfont icon-a-cuowuguanbi"
      @click="close"
    />
  </div>
</template>

<script>
export default {
  name: "ErrorTip",
  props: {
    closeIcon: {
      type: Boolean,
      default: false
    },
    bgColor: {
      type: String,
      default: "#FCF1ED"
    },
    textColor: {
      type: String,
      default: "#EB4738"
    },
    borderStyle: {
      type: String,
      default: "1px solid #FBCFC3"
    },
    borderRadius: {
      type: String,
      default: "8px 8px 8px 8px"
    }
  },
  data() {
    return {
      showState: true
    };
  },
  computed: {
    errorTipStyle() {
      return {
        backgroundColor: this.bgColor,
        border: this.borderStyle,
        color: this.textColor,
        borderRadius: this.borderRadius
      };
    }
  },
  methods: {
    close(){
      this.showState = false;
      this.$emit("close");
    }
  }
};
</script>

<style scoped lang="scss">
.error-tip{
  opacity: 1;
  padding: 0 16px;
  height: 40px;
  .iconfont{
    font-size: 16px;
  }
  .text{
    font-size: 14px;
    font-weight: 400;
    padding-left: 8px;
  }
}
</style>
