<template>
  <div class="flex items-center justify-between px-[16px]">
    <div class="font-bold text-[16px] text-[#000000]">
      {{ title }}
    </div>
    <div
      class="flex items-center"
      @click="handleClick"
    >
      <div class="text-[12px] text-[#999999] ml-[4px]">
        更多
      </div>
      <i class="block w-[16px] h-[16px] text-[#999999] iconfont icon-erjiyoujiantou" />
    </div>
  </div>
</template>

<script>
export default {
  name: "HomeTitle",
  props: {
    title: {
      type: String,
      default: "",
      required: true
    }
  },
  methods: {
    handleClick(){
      this.$emit("click");
    }
  }
};
</script>
