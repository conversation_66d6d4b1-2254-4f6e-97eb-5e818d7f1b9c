<template>
  <login-layout>
    <div class="file-item">
      <p class="title flex flex-align-center">
        上传合同模板文件
        <img
          alt=""
          class="que"
          src="../../img/que.png"
          @click="showDesc = true"
        >
      </p>
      <p class="desc">
        建议上传DOC、DOCX、PDF格式，大小不超2M
      </p>
      <div
        v-if="showUploadLine"
        class="input-content flex flex-align-center flex-space-center"
        @click="uploadFileFun"
      >
        <img
          alt=""
          class="upload"
          src="../../img/upload.png"
        >
        <p class="label">
          上传文件
        </p>
      </div>
      <div v-else>
        <div
          v-for="(item, index) in chooseUploadArr"
          :key="index"
          class="input-content file-box flex flex-align-center flex-space-between"
        >
          <div class="file-name font18 text-ellipsis">
            <img
              :src="icons"
              alt=""
              class="format"
            >{{ item.name }}
          </div>
          <div class="file-icons font14">
            <label @click="previewFile(item)"><img
              alt=""
              class="iconsf"
              src="../../img/e.png"
            >预览</label>
            <img
              alt=""
              class="iconsf"
              src="../../img/d.png"
              @click="deleteFile(index)"
            >
          </div>
        </div>
      </div>
    </div>
    <file-popup :show.sync="showDesc" />
  </login-layout>
</template>

<script>
import { getUserTokenStorage } from "@/libs/token";
import { axiosBaseHeadersConfig } from "@/libs/config";
import FilePopup from "@/pages/sub/other/contractUpload/components/fileDescPopup/index.vue";
import LoginLayout from "@/components/login/LoginLayout.vue";
import { whetherToLogIn } from "@/libs/tools";

export default {
  name: "ContactFileForm",
  components: { LoginLayout, FilePopup },
  props: {
    value: {
      type: [Number, String],
      required: true,
    },
  },
  data() {
    return {
      chooseUploadArr: [], // 选择文件成功的数据
      uploadSucessArr: [], // 上传文件成功的数据
      showDesc: false,
    };
  },
  computed: {
    fileName() {
      return this.chooseUploadArr[0]?.name;
    },
    isPdf() {
      return this.fileName?.indexOf(".pdf") > 0;
    },
    icons(){
      if (this.isPdf) return require("@/pages/sub/other/contractUpload/img/p.png");
      return require("@/pages/sub/other/contractUpload/img/w.png");
    },
    showUploadLine() {
      return !this.uploadSucessArr.length;
    },
  },
  watch: {
    value(val) {
      // 如果没有值，就清空数据
      if (!val) {
        this.chooseUploadArr = [];
        this.uploadSucessArr = [];
      }
    },
    uploadSucessArr(val) {
      this.$emit("input", val[0]);
      this.$emit("uploadFile", val[0]);
    },
  },
  mounted() {
    console.log("我在mo");
    // 主动查询是否同意过隐私协议
    // wx.getPrivacySetting({
    //   success: res => {
    //     console.log(res) // 返回结果为: res = { needAuthorization: true/false, privacyContractName: '《xxx隐私保护指引》' }
    //   },
    //   fail: () => {},
    //   complete: () => {}
    // })
  },
  methods: {
    uploadFileFun() {
      whetherToLogIn(() => {
        wx.chooseMessageFile({
          count: 1, // 默认100
          type: "file",
          extension: ["pdf", ".pdf", "doc", ".doc", "docx", ".docx"],
          success: async (res) => {
            const files = res.tempFiles;
            // 1M=1024K 1K=1024B 超过10M不上传
            let overNum = files?.some(
              (item) => Number(item.size / (1024 * 1024)) > 2
            );
            if (overNum) {
              this.toastFailFun("只能上传word、pdf、wps格式文件，大小不超过2M");
              return false;
            }
            this.chooseUploadArr = files;
            const processResultFiles = async () => {
              const promise = files.map(async (v) => {
                return await this.asyncResultFiles(v);
              });
              return await Promise.all(promise);
            };
            // 最终我们需要的接口返回的数据集合
            this.uploadSucessArr = await processResultFiles();
          },
          fail: (err) => {
            this.toastFailFun("只能上传word、pdf、wps格式文件，大小不超过2M");
            // 用户取消选择文件或选择文件失败的回调函数
            throw err;
          },
        });
      });
    },
    // 异步配合Promise.all可以一次性拿到最终的结果
    asyncResultFiles(item) {
      return new Promise(async (resolve) => {
        const result = await this.uploadFilePromise(item.path);
        const res = JSON.parse(result);
        if (res.code === 0) {
          resolve(res.data);
        } else {
          this.toastFailFun();
        }
      });
    },
    // 异步返回接口的结果
    uploadFilePromise(filePath) {
      let uploadObj = {}; // uploadFile 参数
      uploadObj = {
        filePath,
        name: "file",
      };
      return new Promise((resolve, reject) => {
        // uni.showLoading({
        //   mask: true,
        //   title: "上传中..."
        // });
        uni.uploadFile({
          url:
            process.env.VUE_APP_ENV_BASE_URL +
            "/paralegal/upload/uploadLawyerContract", // 仅为示例，非真实的接口地址
          filePath,
          name: "file",
          ...uploadObj,
          formData: filePath,
          header: {
            "Content-Type": "multipart/form-data",
            token: getUserTokenStorage(),
            osversion: axiosBaseHeadersConfig.osVersion,
          },
          "Content-Type": "multipart/form-data",
          success: (res) => {
            // uni.hideLoading();
            resolve(res.data);
          },
          fail: (err) => {
            // uni.hideLoading();
            this.toastFailFun();
            reject(err);
          },
        });
      });
    },
    // 删除文件
    deleteFile(index) {
      this.chooseUploadArr.splice(index, 1);
      this.uploadSucessArr.splice(index, 1);
    },
    // 预览文件
    previewFile(data) {
      let filePath = data.path; // 微信临时文件路径
      console.log(filePath);
      uni.showLoading({
        mask: true,
        title: "加载中...",
      });
      wx.openDocument({
        filePath,
        showMenu: false, // 是否显示右上角菜单按钮  默认为false
        success() {
          uni.hideLoading();
          console.log("打开网络文档成功");
        },
        fail() {
          uni.hideLoading();
          uni.showToast({
            title: "打开网络文档失败",
            icon: "none",
            duration: 2000,
          });
        },
      });
    },
    // 失败
    toastFailFun(msg) {
      uni.showToast({
        title: msg || "上传失败",
        icon: "none",
        duration: 2000,
      });
      this.chooseUploadArr = [];
      this.uploadSucessArr = [];
    },
  },
};
</script>

<style lang="scss" scoped>
.file-item {
  .title {
    font-size: 16px;
    font-weight: 600;
    color: #333333;
    padding: 16px 0 0 0;
    .que {
      width: 16px;
      height: 16px;
      margin-left: 4px;
    }
  }
  .desc {
    font-size: 12px;
    color: #999999;
    margin: 4px 0 12px 0;
  }
  .input-content {
    width: 343px;
    height: 45px;
    background: #f5f5f7;
    border-radius: 8px 8px 8px 8px;
    font-size: 15px;
    box-sizing: border-box;
    padding: 0 12px;
    .upload {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
    .label {
      font-size: 16px;
      color: #3887f5;
    }
  }
}
.file-box {
  margin-top: 12px;
  margin-bottom: 12px;

  .file-name {
    height: 25px;
    width: 65%;
  }

  .file-icons {
    color: #666666;
    label {
      display: inline-block;
    }
    .iconsf {
      width: 16px;
      height: 16px;
      margin-left: 16px;
      vertical-align: middle;
      margin-right: 4px;
    }
  }
}
.format {
  width: 24px;
  height: 24px;
  vertical-align: middle;
  margin-right: 4px;
}
</style>
