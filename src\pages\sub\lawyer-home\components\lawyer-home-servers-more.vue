<template>
  <div
    class="more"
    @click="handleClick"
  >
    <div class="flex flex-align-center">
      <span>{{ text }}</span>
      <img
        :src="icon"
        alt=""
        class="more-icon"
      >
    </div>
  </div>
</template>

<script>
export default {
  name: "LawyerHomeServersMore",
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    text(){
      return this.value ? "收起更多服务" : "查看更多服务";
    },
    icon(){
      return this.value ? require("@/pages/sub/lawyer-home/img/arrow-up.png") :  require("@/pages/sub/lawyer-home/img/arrow-down.png");
    }
  },
  methods: {
    handleClick(){
      this.$emit("input", !this.value);
    }
  }
};
</script>

<style lang="scss" scoped>
.more {
  width: 319px;
  height: 32px;
  background: linear-gradient(180deg, rgba(245,245,247,0) 0%, #F5F5F7 100%);
  border-radius: 6px;
  opacity: 1;

  font-size: 13px;
  font-weight: 400;
  color: #666666;
  display: flex;
  justify-content: center;
  align-items: center;

  &-icon {
    display: block;
    width: 16px;
    height: 16px;
    margin-left: 2px;
  }
}
</style>
