<template>
  <div class="menu">
    <ul>
      <li
        v-for="(item, index) in menus"
        :key="index"
        class="menu-item"
        @click="jump(item)"
      >
        <div class="flex flex-1 flex-align-center">
          <img
            :src="item.icon"
            alt=""
            class="menu-item__icon flex-shrink-0"
          >
          <div
            v-if="item.slot"
            class="flex-1"
          >
            <slot
              :name="item.slot"
            >
              <div>{{ item.label }}</div>
            </slot>
          </div>
          <div
            v-else
            class="flex-1"
          >
            {{ item.label }}
          </div>
        </div>
        <i class="iconfont flex-shrink-0 icon-erjiyoujiantou" />
      </li>
    </ul>
  </div>
</template>

<script>
import { turnPages, turnToLawyerAuthResultPageToLogin } from "@/libs/turnPages";

export default {
  name: "MineLawyerMenu",
  data() {
    return {
      menus: [],
    };
  },
  methods: {
    /** 跳转方法 */
    jump({ isLogin = true, path }) {
      console.log(path, "pathttttttttttttttttt");

      const jump = () => {
        if (typeof path === "function") {
          path();
        } else {
          turnPages({
            path,
          });
        }
      };

      if (!isLogin) {
        jump();

        return;
      }

      turnToLawyerAuthResultPageToLogin(() => {
        jump();
      });
    },
    /** 向外层暴露一个设置菜单的方法 */
    setMenus(menus) {
      this.menus = menus;
    },
  },
};
</script>

<style lang="scss" scoped>
.menu {
  &-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    font-size: 14px;
    font-weight: bold;
    color: #333333;

    &__icon {
      display: block;
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }

    .iconfont {
      font-size: 16px;
      color: #999999;
    }
  }
}
</style>
