<template>
  <div class="header-box">
    <div
      :class="[headerClass]"
      class="header"
    >
      <div
        v-for="item in list"
        :key="item.value"
        :class="{ select: item.value === activeIndex }"
        @click="handleClick(item.value)"
      >
        <span>{{ item.label }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: "LawyerHomeInfoTabs",
  props: {
    /** 当前的索引 */
    value: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    activeIndex: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
    headerClass() {
      return `grid-cols-${this.list.length}`;
    },
    list() {
      const list = [
        {
          label: "档案",
          value: 0,
        },
      ];

      if (this.hasCase) {
        list.push({
          label: "案件",
          value: 1,
        });
      }

      if (this.hasEvaluate) {
        list.push({
          label: "评价",
          value: 2,
        });
      }

      return list;
    },
    ...mapGetters({
      hasEvaluate: "lawyerHome/hasEvaluate",
      hasCase: "lawyerHome/hasCase",
    }),
  },
  methods: {
    /** 点击事件 */
    handleClick(index) {
      this.activeIndex = index;
    },
  },
};
</script>

<style lang="scss" scoped>
.header-box {
  border-bottom: 1px solid #eeeeee;
}

.header {
  place-items: center;
  height: 44px;
  display: grid;
  line-height: 44px;
  font-size: 14px;
  font-weight: 400;
  color: #999999;
}

.select {
  position: relative;
  height: 100%;
  font-weight: bold;
  color: #333333;
  font-size: 15px;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 14px;
    height: 3px;
    background: #3887F5;
    border-radius: 70px 70px 70px 70px;
  }
}
</style>
