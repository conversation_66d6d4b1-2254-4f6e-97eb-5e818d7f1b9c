<template>
  <div>
    <div class="tabs">
      <u-tabs
        :activeStyle="{
          color: '#333333',
          fontSize: '28rpx',
          fontWeight: 500,
        }"
        :current="tabCurrent"
        :inactiveStyle="{
          color: '#999999',
          fontSize: '28rpx',
        }"
        :list="tabList"
        itemStyle="padding-left: 116rpx; padding-right: 116rpx; height: 88rpx;"
        lineColor="#3887F5"
        lineWidth="14"
        @change="changeTab"
      />
    </div>
    <img
      v-show="tabCurrent === 0"
      alt=""
      class="content"
      mode="widthFix"
      src="@/pages/pay/refund/intro/img/<EMAIL>"
    >
    <img
      v-show="tabCurrent === 1"
      alt=""
      class="content"
      mode="widthFix"
      src="@/pages/pay/refund/intro/img/Frame2887.png"
    >
  </div>
</template>

<script>
import UTabs from "@/uview-ui/components/u-tabs/u-tabs.vue";

export default {
  name: "PayRefundIntro",
  components: { UTabs },
  data() {
    return {
      tabCurrent: 0,
      tabList: [
        {
          name: "退单标准",
        },
        {
          name: "退单示例",
        },
      ],
    };
  },
  methods: {
    changeTab({ index }) {
      console.log(index);
      this.tabCurrent = index;
    },
  },
};
</script>

<style lang="scss" scoped>
.tabs {
  background: #ffffff;
  padding: 0 16px;
  border-bottom: 0.5px solid #EEEEEE;
}

.content {
  width: 375px;
}
</style>
