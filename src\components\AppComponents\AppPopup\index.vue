<template>
  <div>
    <u-popup
      :bgColor="bgColor"
      :closeIconPos="closeIconPos"
      :closeOnClickOverlay="closeOnClickOverlay"
      :duration="duration"
      :mode="mode"
      :overlay="overlay"
      :overlayOpacity="overlayOpacity"
      :overlayStyle="overlayStyle"
      :round="round"
      :safeAreaInsetBottom="safeAreaInsetBottom"
      :safeAreaInsetTop="safeAreaInsetTop"
      :show="show"
      :transitionStyleProps="transitionStyleProps"
      :zIndex="zIndex"
      :zoom="zoom"
      @click="$emit('click')"
      @close="$emit('close')"
      @open="$emit('open')"
    >
      <i
        v-if="mode === 'center' && closeable"
        class="iconfont icon-cuowu !text-[24px] !text-[#FFFFFF] absolute right-[16px] top-[-40px]"
        @click="$emit('cancel')"
      />
      <i
        v-if="mode === 'bottom' && closeable"
        class="iconfont icon-cuowu !text-[24px] !text-[#FFFFFF] absolute right-[16px] top-[-40px]"
        @click="$emit('cancel')"
      />
      <slot />
    </u-popup>
  </div>
</template>

<script>
import UPopup from "@/uview-ui/components/u-popup/u-popup.vue";
import props from "@/uview-ui/components/u-popup/props";

export default {
  name: "AppPopup",
  components: { UPopup },
  mixins: [props],
};
</script>

<style lang="scss" scoped>
.center {
  position: absolute;
  right: 0;
  top: -46px;
  font-size: 21px;
  color: #fff;
}
</style>
