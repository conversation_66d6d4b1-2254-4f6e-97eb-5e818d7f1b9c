<template>
  <div
    class="phrases-tools"
    :class="{active:show}"
  >
    <div class="phrases-container">
      <div
        v-for="i in list"
        :key="i.id"
        class="phrases"
        @click.stop="sendPhrases(i)"
      >
        {{ i.text }}
      </div>
    </div>
    <div class="tools flex flex-align-center">
      <div
        class="tool-item flex-1 flex flex-align-center flex-space-center"
        @click="jump('/pages/sub/im/phrases/index')"
      >
        <i class="iconfont icon-tianjia1" />
        添加
      </div>

      <div
        class="tool-item flex-1 flex flex-align-center flex-space-center"
        @click="jump('/pages/sub/im/phrases/list')"
      >
        <i class="iconfont icon-pinglun" />
        修改
      </div>
    </div>
  </div>
</template>

<script>
import { quickReplyGet } from "@/api/im.js";

export default {
  name: "PhrasesTools",
  props: {
    show: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
  },
  data() {
    return {
      list: []
    };
  },
  watch: {
    show(val){
      if(val){
        quickReplyGet().then(({ data = [] }) => {
          this.list = data;
        });
      }
    }
  },
  methods: {
    jump(url){
      this.$emit("update:show", false);
      uni.navigateTo({
        url
      });
    },
    sendPhrases(data){
      this.$emit("sendPhrases", data.text);
    }
  }
};
</script>

<style scoped lang="scss">
.phrases-tools{
  padding: 0 16px;
  height: 0;
  overflow: hidden;
  transition: height 0.2s;
  &.active{

    height: 208px;
  }
  .phrases-container{
    height: 156px;
    overflow-y: auto;
  }
  .phrases{
    padding: 16px 10px;
    border-bottom: 0.5px solid #DDDDDD;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
  }
  .tools{
    height: 52px;
    .tool-item{
      font-size: 14px;
      font-weight: 400;
      color: #333333;
    }
    .iconfont{
      padding-right: 4px;
      font-size: 16px;
      color: #3887F5;
    }
  }
}
</style>
