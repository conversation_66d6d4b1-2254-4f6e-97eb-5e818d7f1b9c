<template>
  <app-popup
    :show="show"
    :round="16"
    closeable
    mode="bottom"
    bgColor="transparent"
    :safeAreaInsetBottom="false"
    @cancel="show = false"
  >
    <img
      class="block w-[375px] h-[308px]"
      alt=""
      src="@/pages/sub/collaboration-details/imgs/Frame1321316351.png"
    >
    <div class="bg-[#FFFFFF]">
      <div
        class="w-[343px] h-[44px] bg-[#3887F5] rounded-[22px] box-border flex items-center justify-center mx-auto mt-[8px]"
        @click="handleClick"
      >
        <div class="font-bold text-[16px] text-[#FFFFFF]">
          我知道了，去发布
        </div>
      </div>
      <u-safe-bottom />
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "FirstSubmitPopup",
  components: { AppPopup, USafeBottom },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  methods: {
    handleClick() {
      this.show = false;
    },
  },
};
</script>