<template>
  <div>
    <div class="fixed bottom-0 left-0 right-0 bg-[#FFFFFF]">
      <div class="flex">
        <div
          v-for="i in list"
          :key="i.value"
          class="flex-1  h-[48px] flex flex-column items-center justify-center"
          @click.stop="turnPages(i)"
        >
          <img
            class="w-[24px] h-[24px]"
            :src="activeValue===i.value?i.activeIcon:i.icon"
            alt=""
          >
          <p
            class="text-[10px] text-[#999999]"
            :class="[{'!text-[#3887F5]':activeValue===i.value}]"
          >
            {{ i.label }}
          </p>
        </div>
      </div>
      <u-safe-bottom />
    </div>
    <div class="min-h-[48px]" />

    <u-safe-bottom />
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { turnPages, turnToLawyerAuthResultPageToLogin } from "@/libs/turnPages";

export default {
  name: "SubTabBar",
  components: { USafeBottom },
  props: {
    list: {
      type: Array,
      default(){
        return [];
      }
    },
    activeValue: {
      type: String,
      required: true
    }
  },
  methods: { 
    toTurnPages(data){
      turnPages({
        path: data.path,
        query: data.query || {}
      }, true);
    },
    turnPages(data){
      if(data.isLogin){
        turnToLawyerAuthResultPageToLogin(() => {
          this.toTurnPages(data);
        });
        return;
      }
      this.toTurnPages(data);
    } 
  },
};
</script>

<style scoped lang="scss">

</style>