<template>
  <div class="flex px-[24px] bg-white">
    <p
      v-for="(i,index) in list"
      :key="index"
      :class="[{active:activeTabIndex===index}]"
      class="text-[16px] text-[#333333] leading-[44px] text-center flex-1 items-center"
      @click="handoff(index)"
    >
      {{ i.label }}
    </p>
  </div>
</template>

<script>
import { turnPages, turnToLawyerAuthResultPageToLogin } from "@/libs/turnPages";

export default {
  name: "AppTabs",
  props: {
    list: {
      type: Array,
      default: () => []
    },
    /* 模式 页面切换模式还是点击emit 默认emit */
    mode: {
      type: String,
      default: "emit"
    },
    activeTabIndex: {
      type: Number,
      default: 0
    }
  },
  methods: {
    toTurnPages(data){
      turnPages({
        path: data.path,
        query: data.query || {}
      }, true);
    },
    handoff(index){
      if(this.activeTabIndex === index) return;
      if(this.mode === "emit"){
        this.$emit("update:activeTabIndex", index);
        this.$emit("handoff", index);
      }else{
        const  data = this.list[index];
        /* 页面切换默认 直接跳转页面 */
        if(data.isLogin){
          turnToLawyerAuthResultPageToLogin(() => {
            this.toTurnPages(data);
          });
          return;
        }
        this.toTurnPages(data);
      }
    }
  }
};
</script>

<style scoped lang="scss">
.active{
  color: #222222;
  font-weight: bold;
  position: relative;
  &:after{
    position: absolute;
    content: " ";
    width: 14px;
    height: 3px;
    background: #3887F5;
    border-radius: 70px 70px 70px 70px;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
  }
}
</style>