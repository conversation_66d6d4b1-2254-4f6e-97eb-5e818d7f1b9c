<template>
  <div>
    <pay-refund-header
      :statusText="statusText"
      :tipText="tipText"
    />
    <div class="refund-content">
      <div
        v-if="checkStatus === 3"
        class="refuse"
      >
        <div>驳回原因</div>
        <div class="mg-tp-12">
          {{ detail.refuseReason }}
        </div>
      </div>
      <refund-detail-steps
        v-else
        :current="current"
      />
      <div class="refund-content-title mg-tp-24">
        退单信息
      </div>
      <div class="refund-detail">
        <div class="refund-detail-item">
          <div class="refund-detail-item__label">
            退单原因
          </div>
          <div class="text-3887f5">
            {{ detail.feedbackLabel }}
          </div>
        </div>
        <div
          v-if="detail.feedbackReason"
          class="refund-detail-item"
        >
          <div class="refund-detail-item__label">
            补充说明
          </div>
          <div class="text-333333">
            {{ detail.feedbackReason }}
          </div>
        </div>
        <div class="refund-detail-item">
          <div class="refund-detail-item__label">
            截图凭证
          </div>
          <div class="images">
            <img
              v-for="(item, index) in attachFile"
              :key="index"
              :src="item"
              alt=""
              class="images__img"
            >
          </div>
        </div>
      </div>
      <div class="mg-tp-24">
        <pay-refund-introduce />
      </div>
    </div>
    <u-safe-bottom />
  </div>
</template>

<script>
import PayRefundHeader from "@/pages/pay/refund/components/PayRefundHeader.vue";
import PayRefundIntroduce from "@/pages/pay/refund/components/PayRefundIntroduce.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { caseSourceServerV2FeedbackDetail } from "@/api/pay";
import RefundDetailSteps from "@/pages/pay/refund/detail/components/RefundDetailSteps.vue";

export default {
  name: "PayRefundDetail",
  components: {
    RefundDetailSteps,
    USafeBottom,
    PayRefundIntroduce,
    PayRefundHeader,
  },
  data() {
    return {
      detail: {},
    };
  },
  onLoad({ id }) {
    this.getDetail(id);
  },
  computed: {
    /** 审核状态 */
    checkStatus() {
      return Number(this.detail.checkStatus);
    },
    /** 附件图片 */
    attachFile() {
      return this.detail.attachFile?.split(",");
    },
    current() {
      switch (this.checkStatus) {
      case 1:
        return 2;
      case 2:
        return 3;
      default:
        return 0;
      }
    },
    /** 状态文案 */
    statusText() {
      switch (this.checkStatus) {
      case 1:
        return "退单审核中";
      case 2:
        return "退单成功";
      case 3:
        return "退单审核失败";
      default:
        return "";
      }
    },
    /** 提示文案 */
    tipText() {
      switch (this.checkStatus) {
      case 1:
        return "请耐心等待，平台在3-5个工作日审核完毕";
      case 2:
        return "平台审核已通过，感谢您的反馈";
      case 3:
        return "退单申请审核未通过，若有疑问可联系客服";
      default:
        return "";
      }
    },
  },
  methods: {
    /** 获取详情信息 */
    getDetail(id) {
      caseSourceServerV2FeedbackDetail({ id }).then((res) => {
        this.detail = res.data;
      });
    },
  },
};
</script>

<style>
page {
  background-color: #fff;
}
</style>

<style lang="scss" scoped>
@import "@/pages/pay/refund/styles/refund.scss";

.refuse {
  width: 343px;
  background: #fcf1ed;
  border-radius: 8px;
  opacity: 1;
  box-sizing: border-box;
  font-size: 14px;
  font-weight: 400;
  color: #eb4738;
  padding: 12px 16px;
}

.refund-detail {
  width: 343px;
  background: #f5f5f7;
  border-radius: 8px;
  box-sizing: border-box;
  padding: 12px 16px;
  opacity: 1;

  &-item {
    display: flex;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    margin-top: 24px;

    &:first-child {
      margin-top: 0;
    }

    &__label {
      flex-shrink: 0;
      margin-right: 16px;
    }
  }
}

.images {
  display: grid;
  grid-template-columns: repeat(3, 74px);
  grid-gap: 8px;

  &__img {
    display: block;
    width: 74px;
    height: 74px;
    border-radius: 7px;
  }
}
</style>
