<template>
  <div>
    <div class="service-info">
      <div class="top flex flex-align-center flex-space-between">
        <img
          class="logo"
          :src="caseSourceServiceInfo.icon"
          alt=""
        >
        <div class="flex-1">
          <p class="service-name">
            {{ caseSourceServiceInfo.serviceName }}
          </p>
          <div class="service-price flex flex-align-center">
            返佣 <p class="price">
              ¥{{ amountFilter(caseInfo.shareProfit) }}
            </p>
          </div>
        </div>
        <p
          v-if="serverStatus===1"
          class="btn"
          @click="endServePopupState = true"
        >
          结束服务
        </p>

        <p
          v-if="isCaseInfoWaitEvaluate"
          class="btn-border"
        >
          待评价
        </p>

        <p
          v-if="isCaseInfoEvaluate"
          class="btn-border"
        >
          已评价
        </p>
      </div>
      <div class=" pd-tp-12">
        <text-over-flow :dt="`问题描述：${caseInfo.info}`" />
      </div>
    </div>
    <div
      v-if="serverStatus===1"
      class="service-tip flex flex-align-center flex-space-between"
    >
      <p class="tip flex flex-align-center">
        服务中  还剩<u-count-down
          :autoStart="!!computedTime"
          :customStyle="{ color: '#3887F5', fontSize: '24rpx' }"
          :time="computedTime * 1000"
          @finish="finish"
        />服务将自动结束
      </p>
    </div>
    <div
      v-if="isCaseInfoWaitEvaluate"
      class="service-tip flex flex-align-center flex-space-between"
    >
      <p class="tip">
        服务已结束，用户待评价
      </p>
      <p
        v-if="!isSendEvaluateRemind"
        class="btn"
        @click="sendEvaluateRemind"
      >
        发送评价提醒
      </p>
      <p
        v-else
        class="btn-text"
      >
        已发送评价提醒给用户
      </p>
    </div>
    <div
      v-if="isCaseInfoEvaluate"
      class="service-tip flex flex-align-center flex-space-between"
    >
      <p class="tip">
        服务已结束，用户已评价
      </p>
    </div>
    <app-popup
      :show="endServePopupState"
      closeable
      mode="bottom"
      @cancel="endServePopupState=false"
    >
      <div class="popup-container">
        <p class="title">
          是否确认发起服务完成？
        </p>
        <div class="popup-content">
          <div class="text">
            1.确认即代表解决了用户问题，平台将回访核实 <br>
            2.佣金将在服务完成<span style="color: #EB4738">24小时后</span>到账 <br>
            3.服务完成前请确保已编辑好法律意向书，邀请用户好评后可点击发放 <br>
            4.服务完成后用户可对您进行评价，出现差评您将无法收到返佣 <br>
          </div>
          <div class="tip flex flex-align-center">
            <i class="iconfont icon-shuoming" />请避免被投诉，严重者将永远关闭付费咨询功能！
          </div>
          <div
            class="btn"
            @click.stop="endOfService"
          >
            确认
          </div>
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
import TextOverFlow from "@/pages/sub/im/components/TextOverFlow/index.vue";
import { caseInfoStateProps, updateCaseInfoSymbol } from "@/pages/sub/im/mixins/case-info-state.js";
import UCountDown from "@/uview-ui/components/u-count-down/u-count-down.vue";
import { amountFilter } from "@/libs/filter.js";
import { lawyerCaseSourceDiscussPush, lawyerCaseSourceSendDiscussPush } from "@/api/im.js";
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { caseSourceServerV2CompleteCaseServer } from "@/api/clue.js";

export default {
  name: "InquiryCard",
  components: { AppPopup, UCountDown, TextOverFlow },
  mixins: [caseInfoStateProps],
  inject: [updateCaseInfoSymbol],
  data() {
    return {
      /* 结束服务弹窗*/
      endServePopupState: false,
      /* 评价提醒发送状态 false 未发送*/
      isSendEvaluateRemind: false,
      /* 评价提醒发送状态 loading*/
      isSendEvaluateRemindLoading: false
    };
  },
  computed: {
    computedTime() {
      return this.caseInfo.remainSeconds > 0
        ? this.caseInfo.remainSeconds
        : 0;
    },
  },
  methods: {
    amountFilter,
    /* 结束服务*/
    endOfService() {
      caseSourceServerV2CompleteCaseServer(this.caseSourceServerV2Id);
      this.endServePopupState = false;
    },
    finish() {
      /* 初始话 计时不是0的时候 才重新请求案源*/
      if(this.computedTime > 0){
        this[updateCaseInfoSymbol]();
      }
    },
    /* 获取评价提醒发送状态*/
    getEvaluateRemindState() {
      if (!this.caseSourceServerV2Id) return false;
      /* 已支付 查询是否发送过评价提示*/
      if (this.isCaseInfoOrderPay && this.isCaseInfoWaitEvaluate && !this.isSendEvaluateRemindLoading) {
        this.isSendEvaluateRemindLoading = true;
        /* 0.未发送评价提醒push 1. 已发送评价提醒的push*/
        lawyerCaseSourceDiscussPush({
          caseSourceServerV2Id: this.caseSourceServerV2Id
        }).then(data => {
          this.isSendEvaluateRemind = !!data;
        }).finally(() => {
          this.isSendEvaluateRemindLoading = false;
        });
      }
    },
    /* 发送评价提醒*/
    sendEvaluateRemind() {
      return lawyerCaseSourceSendDiscussPush({
        caseSourceServerV2Id: this.caseSourceServerV2Id
      }).then(data => {
        this.isSendEvaluateRemind = true;
        return data;
      });
    }
  },
  watch: {
    serverStatus: {
      immediate: true,
      handler() {
        this.getEvaluateRemindState();
      }
    },
  }
};
</script>

<style scoped lang="scss">
.service-info{
  background: white;
  padding: 12px 12px;
  .top{
    .logo{
      width: 44px;
      height: 44px;
      border-radius: 6px;
      overflow: hidden;
      margin-right: 8px;
    }
    .service-name{
      font-size: 15px;
      font-weight: 600;
      color: #333333;
    }
    .service-price{
      font-size: 12px;
      font-weight: 400;
      color: #999999;
      .price{
        font-size: 15px;
        font-weight: 600;
        color: #F78C3E;
        padding-left: 4px;
      }
    }
    .btn{
      padding: 6px 16px;
      background: #3887F5;
      border-radius: 68px 68px 68px 68px;
      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
    }
    .btn-border{
      padding: 6px 16px;
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      border-radius: 68px 68px 68px 68px;
      border: 1px solid #EEEEEE;
    }
  }
  .desc{

    font-size: 12px;
    font-weight: 400;
    color: #666666;
    .operate{
      font-size: 12px;
      font-weight: 400;
      color: #3887F5;
      float: left;

    }
  }
}
.service-tip{
  height: 37px;
  padding: 0 16px;
  background: #EBF3FE;
  .tip{
    font-size: 12px;
    font-weight: 400;
    color: #3887F5;
  }
  .btn{
    font-size: 12px;
    font-weight: 400;
    color: #FFFFFF;
    padding: 3px 12px;
    background: #3887F5;
    border-radius: 68px 68px 68px 68px;
  }
  .btn-text{
    font-size: 12px;
    font-weight: 400;
    color: #22BF7E;
  }
}
.popup-container{
  .title{
    text-align: center;
    line-height: 50px;
    font-size: 16px;
    border-bottom: 1px solid #EEEEEE;

    font-weight: 500;
    color: #000000;
  }
  .popup-content{
    padding: 12px 16px 24px;
    .text{
      font-size: 14px;
      font-weight: 400;
      color: #666666;
    }
    .tip{
      font-size: 14px;
      font-weight: 400;
      color: #EB4738;
      padding: 20px 0;
      .iconfont{
        font-size: 16px;
        padding-right: 4px;
      }
    }
    .btn{
      text-align: center;
      line-height: 44px;
      background: #3887F5;
      border-radius: 22px 22px 22px 22px;
      font-size: 16px;
      font-weight: 500;
      color: #FFFFFF;
    }
  }
}
</style>
