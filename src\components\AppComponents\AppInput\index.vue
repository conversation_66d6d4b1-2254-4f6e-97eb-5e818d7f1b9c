<template>
  <div>
    <u-input
      :adjustPosition="adjustPosition"
      :autoBlur="autoBlur"
      :border="border"
      :clearable="clearable"
      :color="color"
      :confirmHold="confirmHold"
      :confirmType="confirmType"
      :cursor="cursor"
      :cursorSpacing="cursorSpacing"
      :customStyle="customStyle"
      :disableDefaultPadding="disableDefaultPadding"
      :disabled="disabled"
      :disabledColor="disabledColor"
      :fixed="fixed"
      :focus="focus"
      :fontSize="fontSize"
      :formatter="formatter"
      :holdKeyboard="holdKeyboard"
      :inputAlign="inputAlign"
      :maxlength="maxlength"
      :password="password"
      :placeholder="placeholder"
      :placeholderClass="placeholderClass"
      :placeholderStyle="placeholderStyle"
      :prefixIcon="prefixIcon"
      :prefixIconStyle="prefixIconStyle"
      :readonly="readonly"
      :selectionEnd="selectionEnd"
      :selectionStart="selectionStart"
      :shape="shape"
      :showWordLimit="showWordLimit"
      :suffixIcon="suffixIcon"
      :suffixIconStyle="suffixIconStyle"
      :type="type"
      :value="value"
      @blur="(e) => $emit('blur', e)"
      @change="(e) => $emit('change', e)"
      @clear="$emit('clear')"
      @click="$emit('click')"
      @confirm="(e) => $emit('confirm', e)"
      @focus="$emit('focus')"
      @input="(e) => $emit('input', e)"
      @keyboardheightchange="$emit('keyboardheightchange')"
    >
      <!-- #ifdef MP -->
      <slot name="prefix" />
      <slot name="suffix" />
      <!-- #endif -->
      <!-- #ifndef MP -->
      <slot
        slot="prefix"
        name="prefix"
      />
      <slot
        slot="suffix"
        name="suffix"
      />
      <!-- #endif -->>
    </u-input>
  </div>
</template>

<script>
import UInput from "@/uview-ui/components/u--input/u--input.vue";
import props from "@/uview-ui/components/u-input/props";
import uvInput from "@/uview-ui/components/u-input/u-input.vue";

export default {
  name: "AppInput",
  components: { uvInput, UInput },
  mixins: [props],
};
</script>

<style lang="scss" scoped></style>
