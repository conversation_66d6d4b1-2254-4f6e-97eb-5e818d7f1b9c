export declare const api: {
    ecclevel: number;
    size: number;
    canvas: CanvasRenderingContext2D;
    getFrame: (string: string) => any[];
    utf16to8: (str: string) => any;
    /**
     * 新增$this参数，传入组件的this,兼容在组件中生成
     * @param bg 目前只能设置颜色值
     */
    draw: (str: string, ctx: CanvasRenderingContext2D, startX: number, startY: number, cavW: number, cavH: number, bg: string, color: string, $this?: any, ecc?: number) => void;
};
