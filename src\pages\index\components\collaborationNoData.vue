<template>
  <div class="pt-[24px] flex flex-col items-center">
    <img
      class="w-[240px] h-[180px]"
      src="@/pages/index/imgs/<EMAIL>"
      alt=""
    >
    <p class="pt-[16px] text-[14px] text-[#666666]">
      {{ desc }}
    </p>
    <p
      class="mt-[24px] font-bold text-[14px] text-[#FFFFFF] w-[160px] leading-[32px] text-center bg-[#3887F5] rounded-[68px]"
      @click="jump"
    >
      {{ btnText }}
    </p>
  </div>
</template>

<script>
import { toPublishCollaboration } from "@/libs/turnPages";

export default {
  name: "CollaborationNoData",
  methods: { 
    jump(){
      this.$emit("handleClick");
    }
  },
  props: {
    desc: {
      type: String,
      default: "暂无协作数据",
    },
    btnText: {
      type: String,
      default: "去发布"
    },
    deter: {
      type: Boolean,
      default: false
    }
  },
};
</script>

<style scoped lang="scss">

</style>