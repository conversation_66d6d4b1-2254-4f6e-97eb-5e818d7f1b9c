<template>
  <div>
    <div class="home-tab">
      <div
        v-for="item in list"
        :class="{ 'home-tab-item--selected': item.key === current }"
        class="home-tab-item"
        @click="changeTab(item.key)"
      >
        <div>
          {{ item.label }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { INDICATOR_NAME } from "@/libs/config.js";

export default {
  name: "LawyerHomeTab",
  props: {
    list: {
      type: Array,
      default: () => [
        {
          label: "案由分布",
          key: INDICATOR_NAME.caseReason,
        },
        {
          label: "常去法院",
          key: INDICATOR_NAME.courtName,
        },
        {
          label: "案件标的",
          key: INDICATOR_NAME.caseAmt,
        },
        {
          label: "近期案件数",
          key: INDICATOR_NAME.recentCase,
        },
      ],
    },
  },
  data() {
    return {
      current: INDICATOR_NAME.caseReason,
    };
  },
  methods: {
    changeTab(key) {
      this.current = key;
      this.$emit("change", key);
    },
  },
};
</script>

<style lang="scss" scoped>
.home-tab {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 5px;
  height: 31px;
  line-height: 31px;
  background: #fff4e3;
  border-radius: 60px 60px 60px 60px;
  opacity: 1;
  box-sizing: border-box;

  &-item {
    flex-shrink: 0;
    text-align: center;
    font-size: 12px;
    font-weight: 400;
    color: #333333;

    &--selected {
      background: #bd934c;
      border-radius: 68px 68px 68px 68px;
      opacity: 1;
      font-weight: bold;
      color: #ffffff;
    }
  }
}
</style>
