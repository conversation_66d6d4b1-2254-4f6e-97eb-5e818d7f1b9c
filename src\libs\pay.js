import { createOrder, orderPay } from "@/api/order";
import Store from "@/store";

import { debounce } from "@/libs/tools";

function filterUndefined(val) {
  if (val && val !== "undefined") {
    return val;
  } else {
    return null;
  }
}

function toPayNow(pageOptions) {
  let { orderId, serviceCode } = pageOptions;
  const storageOrder = uni.getStorageSync("orderId");
  const { orderId: storageOrderId } = storageOrder || {};
  orderId = filterUndefined(orderId);

  if (orderId) {
    toPayMethods(orderId);
  } else {
    toCreateOrder(pageOptions);
  }
}

function toPayMethods(orderId) {
  orderPay({
    payType: 2,
    openid: Store.getters["user/getOpenid"],
    orderId,
  })
    .then(async ({ data }) => {
      const res = JSON.parse(data.payResultMsg);
      // const [, { provider }] = await uni.getProvider({
      //   service: "oauth",
      // });
      uni.requestPayment({
        provider: "weixin",
        ...res,
        success: function (res) {
          console.log("success:" + JSON.stringify(res));
          Store.commit("payState/PAY_SUCCESS");
          uni.removeStorageSync("orderId");
          uni.redirectTo({
            url: "/pages/pay/pay-success?id=" + orderId,
          });
        },
        fail: function (err) {
          console.log(err);
          Store.commit("payState/PAY_FAIL");
          uni.showToast({
            title: "支付失败",
            duration: 2000,
            icon: "none",
          });
        },
      });
    })
    .catch((err) => {
      // {code: 10, message: "操作频繁，请稍后再试！", ok: false}
      // ! 订单关闭时也会返回 10 这个错误
      // if(err.code !== 10) {
      if (err.message !== "操作频繁，请稍后再试！") {
        uni.removeStorageSync("orderId");
      }
      // }
    });
}

async function toCreateOrder(params) {
  try {
    for (let key in params) {
      params[key] = filterUndefined(params[key]);
    }
    let respOrder = await createOrder(params);
    if (respOrder) {
      // 缓存订单id
      toPayMethods(respOrder.data.orderId);

      uni.setStorageSync("orderId", {
        orderId: respOrder.data.orderId,
      });
    }
  } catch (error) {
    console.log(error);
  }
}

const debounceToPayNow = debounce(toPayNow, 500);

export function handleOrderPay(pageOptions) {
  debounceToPayNow(pageOptions);
}
