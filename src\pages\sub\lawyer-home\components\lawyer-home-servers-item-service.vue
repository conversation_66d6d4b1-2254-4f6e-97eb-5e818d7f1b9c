<template>
  <div>
    <div class="flex items-center">
      <img
        :src="data.icon"
        alt=""
        class="w-[28px] h-[28px] block rounded-[8px] shrink-0"
      >
      <div class="ml-[8px] flex items-center justify-between flex-1">
        <div class="text-[16px] font-bold text-[#333333] mr-[8px]">
          {{ data.serviceName }}
        </div>
        <div class="flex items-center">
          <span class="text-[15px] font-bold text-[#EB4738] mr-[4px]">¥{{
            (data.servicePrice ? data.servicePrice : 0) | amountFilter
          }}</span><span class="text-[12px] text-[#999999]">/{{ data.serviceNum + data.unitLabel }}</span>
        </div>
      </div>
    </div>
    <div class="flex items-center justify-between mt-[6px]">
      <div
        class="text-[12px] text-[#999999] mt-[2px] text-ellipsis-2 w-[236px]"
      >
        {{ data.info }}
      </div>
      <div
        class="w-[80px] h-[28px] bg-[#3887F5] rounded-[40px] flex items-center justify-center shrink-0"
        @click="handleClick"
      >
        <div class="text-[13px] font-bold text-[#FFFFFF]">
          分享下单
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { amountFilter } from "@/libs/filter";

export default {
  name: "LawyerHomeServersItemService",
  props: {
    data: {
      type: Object,
      default: () => ({}),
      required: true,
    },
  },
  methods: {
    amountFilter,
    handleClick() {
      this.$emit("click", this.data);
    },
  },
};
</script>
