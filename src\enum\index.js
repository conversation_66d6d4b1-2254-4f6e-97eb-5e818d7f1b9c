/**
 * 角色类型 0-兼职律师 1-自营律师 2-咨询师 （1.8.0新增）
 */
export const ROLE_TYPE = {
  0: "律师",
  1: "律师",
  2: "咨询师",
};

/* 案件等级*/
export const CASE_GRADE = [
  {
    label: "高分",
    bg: "#5E69BE",
    value: 7,
  },
  {
    label: "精准",
    bg: "#35B28E",
    value: 2,
  },
  {
    label: "优质",
    bg: "#D4B37C",
    value: 1,
  },
  {
    label: "特惠",
    bg: "#F78C3E",
    value: 8,
  },
];

export const CaseSourceStatus = {
  /**
   * 待锁定
   */
  CASE_SOURCE_STATUS_UNLOCK: 0,

  /**
   * 服务中
   */
  CASE_SOURCE_STATUS_SERVICING: 1,

  /**
   * 待完成
   */
  CASE_SOURCE_STATUS_WAIT_FINISH: 2,

  /**
   * 已完成
   */
  CASE_SOURCE_STATUS_FINISHED: 3,

  /**
   * 取消锁定
   */
  CASE_SOURCE_STATUS_CANCEL_LOCK: 9,

  /**
   * 用户退款
   */
  CASE_SOURCE_STATUS_REFUND: 91,

  /**
   * 律师反馈无效
   */
  CASE_SOURCE_STATUS_FEEDBACK_INVALID: 99,

  /**
   * 已关闭
   */
  CASE_SOURCE_STATUS_CLOSED: 4,
};

export const WebAddress = {
  // 法临币充值协议
  lawyer_legal_currency_recharge_protocol:
    "https://protocol.imlaw.cn/lawyer_legal_currency_recharge_protocol.html",
  // 平台服务购买协议
  lawyer_plat_buy_case: "https://protocol.imlaw.cn/lawyer_plat_buy_case.html",
  // 法临网律师服务公约
  lawyer_service_convention:
    "https://protocol.imlaw.cn/lawyer_service_convention.html",
  // 普通案源退款协议
  lawyer_reimburse: "https://protocol.imlaw.cn/lawyer_reimburse.html",
};

/* 协作类型对象*/
export const COOPERATION_TYPE_OBJ = {
  // 异地查档
  CHECK_FILES_IN_DIFFERENT_PLACES: {
    label: "异地查档",
    value: "1",
  },
  //   案件协作
  CASE_COLLABORATION: {
    label: "案件协作",
    value: "2",
  },
};
/* 案件协作列表*/
export const COOPERATION_LIST = [
  {
    label: "全部",
    value: "",
  },
  COOPERATION_TYPE_OBJ.CASE_COLLABORATION,
  COOPERATION_TYPE_OBJ.CHECK_FILES_IN_DIFFERENT_PLACES,
];

export const URGENCY_TYPE = [
  {
    label: "普通发布",
    value: 1,
    tip: "提示：默认普通发布，平台仅使用站内消息提醒的方式通知本地律师",
    showTip:
      "您的诉求平台已通过站内消息提醒本地律师，请耐心等待律师接单",
  },
  {
    label: "加急发布",
    value: 2,
    tip: "提示：选择加急发布，平台将使用站内消息提醒+短信提醒的方式通知本地律师",
    showTip:
      "您的诉求平台已通过站内消息提醒和短信通知本地律师，请耐心等待律师接单",
  },
];

export const JD_AREA_LIMIT_TYPE = [
  {
    label: "全国律师",
    value: 1,
    tip: "提示：平台默认全国可接单，外地律师也可以抢单",
  },
  {
    label: "仅限本地律师",
    value: 2,
    tip: "提示：您已选择仅限本地律师接单，外地律师将无法抢单",
  },
];

// 协作类型 发布协作页面使用
export const COOPERATION_TYPE = [
  COOPERATION_TYPE_OBJ.CHECK_FILES_IN_DIFFERENT_PLACES,
  COOPERATION_TYPE_OBJ.CASE_COLLABORATION,
];

/* 协作详情接单状态 1.待接单、2.已匹配（已接单）、9.已关闭*/
export const COOPERATION_RECEIVE_STATUS = {
  1: {
    label: "待接单",
    value: 1,
    /* 字体颜色*/
    color: "#22BF7E",
    /* 徽章颜色*/
    badgeColor: "#22BF7E",
  },
  2: {
    label: "已匹配",
    value: 2,
    color: "#999999",
  },
  9: {
    label: "已关闭",
    value: 9,
    color: "#999999",
  },
};

/* 协作状态列表*/
export const COOPERATION_STATUS = [
  {
    label: "全部状态",
    value: "",
  },
  {
    label: "待接单",
    value: 1,
  },
  {
    label: "已匹配",
    value: 2,
  },
];

/** 分享type 0案源 1 成单喜报 2 文章 3协作 */
export const SHARE_TYPE = {
  case: 0,
  success: 1,
  article: 2,
  collaboration: 3,
};
