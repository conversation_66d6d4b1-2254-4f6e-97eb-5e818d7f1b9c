<template>
  <div>
    <img
      v-if="isShowBanner && getRemainClueNum <= 0"
      class="top-img"
      src="@/pages/caseSource/img/Frame1321314643@2x(2).png"
      alt=""
      @click="turnToRechargePage"
    >
    <div
      v-if="!isShowBanner && getShowStyle.theme"
      class="status-top"
      :class="getShowStyle.theme"
    >
      <div class="flex flex-align-center flex-space-between">
        <span>
          {{ getShowStyle.text }}
        </span>
        <span
          v-show="getShowStyle.theme==='closed'"
          class="btn"
          @click="turnToRefundDetailPage({id: detail.currentFeedBackId})"
        >
          {{ getFeedBackText }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { turnToRechargePage, turnToRefundDetailPage } from "@/libs/turnPages";
import { CaseSourceStatus } from "@/enum";

export default {
  methods: { turnToRechargePage, turnToRefundDetailPage },
  props: {
    isShowBanner: {
      type: Boolean,
      default: true
    },
    detail: {
      type: Object,
      default: () => ({})
    },
    getRemainClueNum: {
      type: Number,
      default: 0
    },
  },
  computed: {
    CaseSourceStatus() {
      return CaseSourceStatus;
    },
    getShowStyle(){
      if(this.detail.status === CaseSourceStatus.CASE_SOURCE_STATUS_FINISHED) {
        return {
          text: "已完成",
          theme: "success",
        };
      }
      if(
        this.detail.status === CaseSourceStatus.CASE_SOURCE_STATUS_CLOSED ||
          this.detail.status === CaseSourceStatus.CASE_SOURCE_STATUS_REFUND ||
          this.detail.status === CaseSourceStatus.CASE_SOURCE_STATUS_FEEDBACK_INVALID ||
          this.detail.status === CaseSourceStatus.CASE_SOURCE_STATUS_CANCEL_LOCK
      ) {
        return {
          text: "已关闭",
          theme: "closed",
        };
      }
      if(
          this.detail.status === CaseSourceStatus.CASE_SOURCE_STATUS_SERVICING ||
          this.detail.status === CaseSourceStatus.CASE_SOURCE_STATUS_WAIT_FINISH
        ){
        return {
          text: "跟进中",
          theme: "default",
        };
      }
      return {
        text: '',
        theme: null,
      };
    },
    getFeedBackText(){
      switch (this.detail.feedBackStatus) {
      case 1:
        return "审核中";
      case 2:
        return "退单详情";
      case 3:
        return "审核不通过";
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.top-img{
  width: 100%;
  height: 64px;
}
.status-top{
  height: 64px;
  background: linear-gradient(90deg, #8E9AFF 0%, #6474F8 100%);
  border-radius: 8px 8px 8px 8px;
  font-size: 20px;
  font-weight: 500;
  color: #FFFFFF;
  padding: 16px;
  box-sizing: border-box;
  margin-bottom: 12px;
  &.success{
    background: linear-gradient(90deg, #8E9AFF 0%, #6474F8 100%);
  }
  &.closed{
    color: #666666;
    background: linear-gradient(90deg, #E5E5E5 0%, #CFCFCF 100%);
    .btn{
      width: 102px;
      line-height: 32px;
      background: #3887F5;
      border-radius: 68px 68px 68px 68px;
      text-align: center;
      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
    }
  }
}
</style>
