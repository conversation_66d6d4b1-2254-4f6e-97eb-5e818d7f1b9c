<template>
  <login-layout>
    <div class="position-relative">
      <img
        alt=""
        class="bg"
        src="@/pages/mine/img/bg.png"
      >
      <div class="mine px-[12px]">
        <div class="info">
          <mine-lawyer-info />
        </div>
        <div
          v-if="!profileComplete && !isNotLogin()"
          class="tips"
          @click="turnToProfileInfoPage"
        >
          <mine-lawyer-tips />
        </div>
        <div class="mt-[12px]">
          <mine-button />
        </div>
        <mine-recent-lawyer />
        <div class="menus mt-[16px] rounded-[12px] bg-[#FFFFFF]">
          <div class="font-bold text-[16px] text-[#333333] px-[16px] py-[12px]">
            我的服务
          </div>
          <mine-lawyer-menu ref="menus" />
        </div>
      </div>
      <div
        v-if="isLogin"
        class="w-[160px] h-[38px] rounded-[46px] border-[1px] border-solid border-[#CCCCCC] text-[16px] text-[#666666] flex items-center justify-center mt-[32px] mx-auto mb-[57px]"
        @click="logOutVisible = true"
      >
        退出登录
      </div>
    </div>
    <mine-lawyer-login-out-popup
      :show="logOutVisible"
      @cancel="logOutVisible = false"
      @config="userLoginOut"
    />
    <mine-popup />
  </login-layout>
</template>

<script>
import LoginLayout from "@/components/login/LoginLayout.vue";
import { userLoginOut } from "@/api/user";
import MineLawyerInfo from "@/pages/mine/components/MineLawyerInfo.vue";
import MineLawyerTips from "@/pages/mine/components/MineLawyerTips.vue";
import MineLawyerMenu from "@/pages/mine/components/MineLawyerMenu.vue";
import {
  toContractUpload,
  toMessageNotice,
  toMyAnswer,
  toOrderSuccessful,
  turnToLawyerAuthResultPageToLogin,
  turnToProfileInfoPage,
  turnToServiceCenterPage
} from "@/libs/turnPages";
import { cleanLoginData, isNotLogin } from "@/libs/tools";
import store from "@/store";
import MineLawyerLoginOutPopup from "@/pages/mine/components/MineLawyerLoginOutPopup.vue";
import { mapGetters } from "vuex";
import { coreMessageNoRead } from "@/api/im";
import MineButton from "@/pages/mine/components/MineButton.vue";
import MineRecentLawyer from "@/pages/mine/components/MineRecentLawyer.vue";
import MinePopup from "@/pages/mine/components/MinePopup.vue";

/**
 * @param {Array} menus 菜单列表
 * @param {String} menus[].icon 菜单图标
 * @param {String} menus[].label 菜单名称
 * @param {String | function} menus[].path 菜单路径
 * @param {boolean} menus[].isLogin 是否验证登陆，默认为true
 */
const menus = [
  {
    label: "合同上传",
    icon: require("@/pages/mine/img/img-3.png"),
    path() {
      toContractUpload();
    },
    isLogin: true,
  },
  {
    label: "联系客服",
    icon: require("@/pages/mine/img/4.png"),
    path() {
      turnToServiceCenterPage();
    },
    isLogin: false,
  },
];

export default {
  name: "Mine",
  components: {
    MinePopup,
    MineRecentLawyer,
    MineButton,
    MineLawyerLoginOutPopup,
    MineLawyerMenu,
    MineLawyerTips,
    MineLawyerInfo,
    LoginLayout,
  },
  data() {
    return {
      logOutVisible: false,
      systemMsg: {},
    };
  },
  onShow() {
    store.dispatch("user/setUserInfo");

    // 领取法令币奖励
    // lawyerGetAwardByRuleTypeCode();
  },
  computed: {
    isLogin() {
      return !isNotLogin();
    },
    ...mapGetters({
      profileComplete: "user/profileComplete",
    }),
    /* 未读总数 */
    unreadTotal() {
      return this.systemMsg.totalNum || 0;
    },
  },
  mounted() {
    this.$refs.menus.setMenus(menus);
  },
  methods: {
    isNotLogin,
    turnToProfileInfoPage,
    userLoginOut() {
      userLoginOut()
        .then(() => {
          cleanLoginData();
          uni.removeStorageSync("shareChannelId");
          uni.showToast({
            title: "退出成功",
            icon: "none",
          });
        })
        .finally(() => {
          this.logOutVisible = false;
        });
    },
    /* 获取未读的系统消息*/
    getUnreadSystemMsg() {
      coreMessageNoRead().then(({ data }) => {
        this.systemMsg = data;
      });
    },
    toMessageNotice() {
      turnToLawyerAuthResultPageToLogin(() => {
        toMessageNotice();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 375px;
  height: 220px;
  z-index: -1;
}

.mine {
  .info {
    padding-top: 36px;
  }

  .tips {
    margin-top: 12px;
  }

  .menus {
    &__guide {
      margin-left: 8px;
      width: 64px;
      height: 18px;
      background: linear-gradient(109deg, #ff913e 0%, #f54a3a 100%);
      border-radius: 8px 2px 8px 2px;
      opacity: 1;
      font-size: 12px;
      font-weight: 400;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
    }
  }
}
</style>
