<template>
  <div>
    <div
      v-if="isCertStatusNot"
      class="placeholder-container"
      :style="[getPlaceholderStyle]"
    >
      <img
        alt=""
        class="tip-img"
        src="@/pages/message-notice/imgs/no-data1.png"
      >
      <p class="title">
        未认证
      </p>
      <p class="tip">
        您还未完成律师认证
      </p>
      <p
        class="btn"
        @click="turnToLawyerAuthResultPage"
      >
        去认证
      </p>
    </div>

    <div
      v-else-if="isCertStatusFail"
      class="placeholder-container"
      :style="[getPlaceholderStyle]"
    >
      <img
        alt=""
        class="tip-img"
        src="@/pages/message-notice/imgs/no-data1.png"
      >
      <p class="title">
        认证失败
      </p>
      <p class="tip">
        您还未完成律师认证
      </p>
      <p
        class="btn"
        @click="turnToLawyerAuthResultPage"
      >
        重新认证
      </p>
    </div>

    <div
      v-else-if="isAuthing"
      class="placeholder-container"
      :style="[getPlaceholderStyle]"
    >
      <img
        alt=""
        class="tip-img"
        src="@/pages/message-notice/imgs/no-data2.png"
      >
      <p class="title">
        认证审核中…
      </p>
      <p class="tip">
        可联系客服催促进度
      </p>
      <p
        class="btn"
        @click="turnToLawyerAuthResultPage"
      >
        查看认证进度
      </p>
    </div>

    <div v-else>
      <slot />
    </div>
  </div>
</template>

<script>
import { turnToLawyerAuthResultPage } from "@/libs/turnPages";

export default {
  name: "CertificationProgress",
  props: {
    placeholderStyle: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    getUserInfo(){
      return this.$store.getters["user/getUserInfo"];
    },
    /* 认证审核中*/
    isAuthing(){
      return this.$store.getters["user/isCertStatusAudit"];
    },
    /* 认证成功*/
    whetherOrNotToCertify(){
      return this.$store.getters["user/lawyerCertStatus"];
    },
    /* 未认证*/
    isCertStatusNot(){
      return this.$store.getters["user/isCertStatusNot"];
    },
    /* 认证失败*/
    isCertStatusFail(){
      return this.$store.getters["user/isCertStatusFail"];
    },
    getPlaceholderStyle(){
      return uni.$u.addStyle(this.placeholderStyle);
    }
  },
  methods: { turnToLawyerAuthResultPage },
};
</script>

<style scoped lang="scss">
.placeholder-container{
  padding-top: 114px;
  text-align: center;
  .tip-img{
    width: 240px;
    height: 180px;
  }
  .title{
    padding-top: 16px;
    font-size: 16px;
    font-weight: 500;
    color: #333333;
  }
  .tip{
    padding-top: 16px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
  }
  .btn{
    width: 160px;
    line-height: 32px;
    text-align: center;
    background: #3887F5;
    border-radius: 68px 68px 68px 68px;
    font-size: 14px;
    font-weight: 400;
    color: #FFFFFF;
    margin: 24px auto 0;
  }
}
</style>