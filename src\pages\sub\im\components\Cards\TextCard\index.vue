<template>
  <theme-layout-card :theme="theme">
    <p class="text">
      {{ data.msg }}
    </p>
  </theme-layout-card>
</template>

<script>
import ThemeLayoutCard from "@/pages/sub/im/components/Cards/ThemeLayoutCard/index.vue";
import cardProps from "@/pages/sub/im/mixins/card-props.js";

export default {
  name: "TextCard",
  components: { ThemeLayoutCard },
  mixins: [cardProps]
};
</script>

<style scoped lang="scss">
.text{
  word-break:break-all;
}
</style>
