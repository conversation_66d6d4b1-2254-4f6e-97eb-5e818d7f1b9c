import { requestCommon, requestCore } from "@/libs/axios";
import { storeRequest } from "@/libs/store-request";

/* 用户基本信息*/
export const getUserInfo = () => requestCommon.post("/paralegal/user/info");

/* 用户基本信息更新*/
export const setUserInfoAsync = (data) =>
  requestCore.post("/user/info/update", data);


/* 获取IM*/
export const imLoadUser = (data) => requestCore.post("/im/loadUser", data);

/* IM缓存 */
export const imLoadUserCache = (data) => storeRequest({
  api: imLoadUser,
  data,
  cacheName: "imLoadUser",
  storeMaxTime: 3000,
});

/** 发送登录短信 */
export const messageSendSms = (data) => requestCore.post("/common/message/sendSms", data);

/** 短信登录 */
export const userLogin = (data) => requestCommon.post("/paralegal/user/login", data);

/** 律师端登出 */
export const userLoginOut = (data) => requestCommon.post("/paralegal/user/loginOut", data);

/** 个人资料 */
export const userDetail = () => requestCommon.post("/paralegal/user/detail");

/** 更新律师个人资料(统一接口) */
export const updateAllLawyerInfo = (data) => requestCommon.post("/paralegal/update/allLawyerInfo", data);

/** 个人网站资料评分 */
export const lawyerDataGetScore = () => requestCommon.post("/paralegal/lawyer/data/getScore");

/* 获取会员一对一付费服务列表*/
export const serviceManegeGetVipServiceList = () => requestCommon.post("/paralegal/serviceManege/getVipServiceList");

/** 律师分享code获取 */
export const getLawyerShareCode = () => requestCommon.post("/paralegal/lawyer/share/code");

/** 
 * 修改微信号
 * https://showdoc.imlaw.cn/web/#/5/3884
 */
export const lawyerUpdateWechatId = (data) => requestCommon.post("/paralegal/lawyer/updateWechatId", data);

/** 
 * 获取微信号
 * https://showdoc.imlaw.cn/web/#/5/3885
 */
export const lawyerGetWechatId = () => requestCommon.post("/paralegal/lawyer/getWechatId");

/** 
 * 修改其他联系人电话
 * https://showdoc.imlaw.cn/web/#/5/3278
 */
export const lawyerUpdateOtherPhone = (data) => requestCommon.post("/paralegal/lawyer/updateOtherPhone", data);

/** 
 * 获取其他联系人电话
 * https://showdoc.imlaw.cn/web/#/5/3279
 */
export const lawyerGetOtherPhone = () => requestCommon.post("/paralegal/lawyer/phone");

/** 
 * 分享成单喜报和文章
 * https://showdoc.imlaw.cn/web/#/5/3289
 */
export const contentShare = (data) => requestCommon.post("/info/content/share", data);