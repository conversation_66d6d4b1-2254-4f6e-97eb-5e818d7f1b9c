<template>
  <div class="wap">
    <div class="case-information">
      <div class="background-white">
        <!--        <p class="title">{{ lawCaseRes.title || "" }}</p>-->
        <p class="title">
          基本信息
        </p>
        <div class="content">
          <div class="header-item flex">
            <p class="text-align-justify text-align-justify-p">
              审理法院
            </p>
            ：{{ lawCaseRes.lawCourt || "" }}
          </div>
          <div class="header-item flex">
            <p class="text-align-justify text-align-justify-p">
              案号
            </p>
            ：{{ lawCaseRes.caseNo || "" }}
          </div>
          <div class="header-item flex">
            <p class="text-align-justify text-align-justify-p">
              裁判日期
            </p>
            ：{{ lawCaseRes.refereeTime || "" }}
          </div>
          <div class="header-item flex">
            <p class="text-align-justify text-align-justify-p">
              案件类型
            </p>
            ：{{ lawCaseRes.caseType || "" }}
          </div>
          <div class="header-item flex">
            <p class="text-align-justify text-align-justify-p">
              文书性质
            </p>
            ：{{ lawCaseRes.documentType || "" }}
          </div>
          <div class="header-item flex">
            <p class="text-align-justify text-align-justify-p">
              审理程序
            </p>
            ：{{ lawCaseRes.trialProcedure || "" }}
          </div>
        </div>
      </div>
    </div>
    <div class="case-content background-white">
      <p class="title">
        文书正文
      </p>
      <div class="content-wr">
        <div class="case-title">
          当事人信息
        </div>
        <div class="case-text">
          {{ lawCaseRes.personConcernedInfo || "" }}
        </div>
        <div class="case-title">
          诉讼记录
        </div>
        <div class="case-text">
          {{ lawCaseRes.litigationRecord || "" }}
        </div>
        <div class="case-title">
          案件基本情况
        </div>
        <div class="case-text">
          {{ lawCaseRes.caseBasicInfo || "" }}
        </div>
        <div class="case-title">
          裁判分析过程
        </div>
        <div class="case-text">
          {{ lawCaseRes.refereeProcess || "" }}
        </div>
        <div class="case-title">
          判决结果
        </div>
        <div class="case-text">
          {{ lawCaseRes.judgmentResult || "" }}
        </div>
        <div class="case-title">
          引用法条
        </div>
        <div class="case-text">
          {{ lawCaseRes.quote || "" }}
        </div>
        <div class="case-title">
          法官信息
        </div>
        <div class="case-text">
          {{ lawCaseRes.judgeInfo || "" }}
        </div>
      </div>
    </div>
    <u-safe-bottom />
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "DocumentDetailOfMeInfo",
  components: { USafeBottom },
  props: {
    lawCaseRes: {
      type: Object,
      default: () => ({}),
    },
  },
};
</script>

<style lang="scss" scoped>
.wap {
  padding-bottom: 10px;

  .case-content,.case-information {
    .title {
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      padding: 0 16px 12px;
      border-bottom: 1px dashed #ededed;
    }
  }
  .case-information {
    margin-bottom: 12px;
    .background-white {
      border-radius:0 0  16px 16px;
    }

    .content {
      padding: 16px;

      .header-item {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: 20px;
        margin-bottom: 4px;
        .text-align-justify-p {
          height: 18px;
          width: 63px;
          font-size: 13px;
          font-weight: 400;
          color: #666666;
        }
      }
    }
  }

  .case-content {
    .title{
      padding: 12px 16px;
    }
    border-radius: 16px;
    .content-wr {
      padding: 0 16px;
    }
    .case-title {
      padding-left: 7px;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      position: relative;
      line-height: 44px;

      &:after {
        position: absolute;
        content: " ";
        top: 50%;
        transform: translateY(-50%);
        left: 0;
        width: 3px;
        height: 14px;
        background: #3887F5;
        border-radius: 40px 40px 40px 40px;
      }
    }

    .case-text {
      font-size: 13px;
      font-weight: 400;
      color: #666666;
      padding: 0 4px 20px;
    }
  }
}
</style>
