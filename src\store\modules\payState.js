export default {
  namespaced: true,
  state: {
    /** 支付状态 */
    state: false,
    /** 支付失败回调 */
    failCallback: [],
    /** 支付弹窗支付事件 */
    payPopupPay: null,
  },
  mutations: {
    /** 支付成功后触发 */
    PAY_SUCCESS(state) {
      state.state = true;
    },
    /** 支付失败后触发 */
    PAY_FAIL(state) {
      state.state = false;
      // 支付失败后执行回调
      state.failCallback.forEach((fail) => {
        fail?.callback();
      });
    },
    /**
     * 添加支付失败回调
     * @param state {Object} state
     * @param name {String} 回调名称
     * @param {Function} callback 添加支付失败回调
     * @constructor
     */
    ADD_FAIL_CALLBACK(state, { name, callback }) {
      state.failCallback.push({
        name,
        callback,
      });
    },
    /**
     * 移除支付失败回调
     * @param state {Object} state
     * @param name {String} 回调名称
     */
    REMOVE_FAIL_CALLBACK(state, name) {
      state.failCallback = state.failCallback.filter(
        (item) => item.name !== name
      );
    },
    /** 移除所有回调 */
    REMOVE_ALL_FAIL_CALLBACK(state) {
      state.failCallback = [];
    },
    /** 支付失败弹窗中支付事件 */
    PAY_FAIL_POPUP_PAY(state) {
      state.payPopupPay?.();
    },
    /** 设置支付失败弹窗中支付事件 */
    SET_PAY_FAIL_POPUP_PAY(state, payPopupPay) {
      state.payPopupPay = payPopupPay;
    }
  },
};
