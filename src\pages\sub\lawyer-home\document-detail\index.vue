<template>
  <div class="instrument-details-page">
    <div class="header">
      <div class="title">
        <span class="label">{{ info.caseType }}</span>{{ info.title }}
      </div>
      <div class="font13 header-item flex">
        <p class="text-align-justify text-align-justify-p">
          文书来源
        </p>
        ：
        <span class="_a">裁判文书网</span>
      </div>
      <div class="font13 header-item flex">
        <p class="text-align-justify text-align-justify-p">
          案号
        </p>
        ：{{ info.caseNo }}
      </div>
    </div>
    <div class="essential-info">
      <div class="title">
        基本信息
      </div>
      <div class="info-list">
        <div
          v-for="(i, index) in essentialInfoList"
          :key="index"
          class="info-item flex"
        >
          <p class="text-align-justify text-align-justify-p">
            {{ i.label }}
          </p>
          ：{{ info[i.prop] || "" }}
        </div>
      </div>
      <div class="info-gray-list">
        <div class="info-gray-item background-1">
          <div class="flex">
            <p class="text-align-justify text-align-justify-p">
              原告
            </p>
            ：{{ arrayTostring(info.ygName) }}
          </div>
          <div class="flex">
            <p class="text-align-justify text-align-justify-p">
              原告律师
            </p>
            ：{{ arrayTostring(info.ygLawyerName) }}
          </div>
        </div>
        <div class="info-gray-item background-2">
          <div class="flex">
            <p class="text-align-justify text-align-justify-p">
              被告
            </p>
            ：{{ arrayTostring(info.bgName) }}
          </div>
          <div class="flex">
            <p class="text-align-justify text-align-justify-p">
              被告律师
            </p>
            ：{{ arrayTostring(info.bgLawyerName) }}
          </div>
        </div>
      </div>
    </div>
    <div class="document-body">
      <p class="title">
        文书正文
      </p>
      <div class="document-body-c">
        <p class="sub-title_body">
          当事人信息
        </p>
        <div class="party-info">
          <div
            v-for="(item, i) in strTohtml"
            :key="i"
          >
            <p>{{ item.label }}：</p>
            <p class="cont">
              {{ item.text }}
            </p>
          </div>
        </div>
      </div>
      <div class="document-body-c">
        <p class="sub-title_body">
          法院观点
        </p>
        <p v-html="info.courtView" />
      </div>
      <div class="document-body-c">
        <p class="sub-title_body">
          裁判结果
        </p>
        <p v-html="info.judgeResult" />
      </div>
      <div class="document-body-c">
        <p class="sub-title_body">
          裁判日期
        </p>
        <p>{{ info.judgeDate }}</p>
      </div>
    </div>
    <div
      v-show="info.fg"
      class="citing-regulations"
    >
      <p class="title">
        引用法规
      </p>
      <p
        v-for="(item, i) in info.fg"
        :key="i"
      >
        {{ item }}
      </p>
    </div>
  </div>
</template>

<script>
import { lawyerCaseDetail } from "@/api/lawyer.js";
import { isArray, isNull } from "@/libs/basics-tools.js";

export default {
  name: "Index",
  data() {
    return {
      essentialInfoList: [
        {
          label: "审理法院",
          prop: "courtName",
          text: "青海省玉树藏族自治州中级人民法院",
        },
        {
          label: "案号",
          prop: "caseNo",
          text: "（2021） 青27民终249号",
        },
        {
          label: "案件类型",
          prop: "caseType",
          text: "民事案件",
        },
        {
          label: "案由",
          prop: "caseReason",
          text: "买卖合同纠纷",
        },
        {
          label: "裁判日期",
          prop: "judgeDate",
          text: "2021-11-12",
        },
        {
          label: "审理程序",
          prop: "caseStage",
          text: "二审",
        },
        {
          label: "文书性质",
          prop: "docType",
          text: "裁定书",
        },
      ],
      caseId: null,
      info: {
        fg: [],
      },
    };
  },
  onLoad({ id }) {
    console.log(id, "0e6704d435a40f2ba7254e2a5386d88a");
    this.caseId = id;
  },
  onShow() {
    this.getInfo();
  },
  computed: {
    strTohtml() {
      const _str = this.info.partyInfo?.split("\n") || [];
      const _html = [];
      _str.forEach((e) => {
        if (isNull(e)) return;
        const s = e.split("：") || [];
        _html.push({
          label: s[0],
          text: s[1],
        });
      });
      console.log(_html);
      return _html;
    },
  },
  methods: {
    arrayTostring(str) {
      return (isArray(str) ? str.toString() : str) || "";
    },
    getInfo() {
      console.log("我在加载");
      if (!this.caseId) return this.$toast("案件详情id不存在");
      lawyerCaseDetail({ caseId: this.caseId }).then(({ data }) => {
        console.log(data, 9999);
        this.info = Object.assign({}, data.caseDetail, {
          ygName: data.caseDetail?.plaintiffInfo?.map((res) => {
            return res.name;
          }),
          ygLawyerName: data.caseDetail?.ygLawyer?.map((res) => {
            return res.lawyerName;
          }),
          bgName: data.caseDetail?.defendantInfo?.map((res) => {
            return res.name;
          }),
          bgLawyerName: data.caseDetail?.bgLawyer?.map((res) => {
            return res.lawyerName;
          }),
          fg: data.caseDetail?.quoteLegislation?.map((res) => {
            return res.quoteLegislation;
          }),
        });
        console.log(this.info, 88888);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.text-align-justify-p {
  height: 18px;
  width: 63px;
}

.instrument-details-page {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 22px;
}

.header {
  padding: 16px;
  border-radius: 0 0 16px 16px;
  background: white;

  .title {
    font-size: 18px;
    font-weight: bold;
    padding-bottom: 4px;
    color: #222222;

    .label {
      padding-right: 17px;
      color: #3887f5;
      position: relative;

      &:after {
        position: absolute;
        content: " ";
        width: 1px;
        height: 18px;
        top: 50%;
        transform: translateY(-50%);
        background: #eeeeee;
        right: 8px;
      }
    }
  }

  .header-item {
    padding-top: 4px;
    color: #999999;
    align-items: center;

    ._a {
      color: #3887f5;
    }
  }
}

.essential-info {
  margin: 12px 16px;
  border-radius: 8px 8px 8px 8px;
  background: white;
  padding: 0 16px 16px;

  .title {
    font-size: 16px;
    line-height: 46px;
    font-weight: bold;
    color: #333333;
    border-bottom: 1px dashed #ededed;
  }

  .info-list {
    padding-top: 16px;
    padding-bottom: 12px;

    .info-item {
      align-items: center;
      padding-bottom: 4px;

      view {
        font-size: 13px;
        font-weight: 400;
        color: #666666;
      }

      font-size: 13px;
      font-weight: 400;
      color: #333333;
    }
  }

  .info-gray-list {
    .info-gray-item {
      & + .info-gray-item {
        margin-top: 12px;
      }

      font-size: 13px;
      font-weight: 400;
      color: #333333;
      border-radius: 4px 4px 4px 4px;
      padding-left: 20px;
      padding-top: 10px;
      padding-bottom: 6px;

      & > .flex {
        padding-bottom: 4px;
      }

      view {
        font-size: 13px;
        font-weight: 400;
        color: #666666;
      }
    }
  }
}

.document-body {
  border-radius: 16px 16px 16px 16px;
  background: white;
  padding: 0 16px;

  .title {
    font-size: 16px;
    line-height: 46px;
    font-weight: bold;
    color: #333333;
  }

  .sub-title_body {
    font-size: 14px;
    font-weight: bold;
    color: #333333;
    padding-left: 7px;
    position: relative;
    line-height: 44px;

    &:after {
      position: absolute;
      content: " ";
      width: 3px;
      height: 14px;
      background: #3887f5;
      border-radius: 40px 40px 40px 40px;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .document-body-c {
    font-size: 13px;
    font-weight: 400;
    color: #666666;
    padding-bottom: 20px;

    .party-info {
      > label {
        padding-bottom: 6px;

        &:last-child {
          padding-bottom: 0;
        }
      }

      view {
        padding-bottom: 6px;
        // &:last-child {
        //   padding-bottom: 0;
        // }
      }

      .cont {
        font-size: 13px;
        font-weight: 400;
        color: #333333;
      }
    }
  }
}

.citing-regulations {
  background: white;
  border-radius: 16px 16px 16px 16px;
  margin-top: 12px;
  padding: 0 16px 8px;

  .title {
    font-size: 16px;
    font-weight: bold;
    line-height: 46px;
    color: #333333;
    padding-bottom: 0;
  }

  view {
    font-size: 12px;
    font-weight: 400;
    color: #666666;
    padding-bottom: 16px;
  }
}

.background-1 {
  background: #edf3f7;
}

.background-2 {
  background: #fcf1ed;
}
</style>
