<template>
  <div>
    <img
      :src="headerImage"
      alt=""
      class="header-image"
    >
  </div>
</template>

<script>
import { LAWYER_SERVICE_ENUM } from "@/pages/sub/lawyer-home/lawyer-services/js";

export default {
  name: "LawyerServicesHeaders",
  props: {
    /** 服务code */
    scene: {
      type: String,
      required: true,
    },
  },
  computed: {
    headerImage() {
      const headerImages = {
        [LAWYER_SERVICE_ENUM.htdx]: require("@/pages/sub/lawyer-home/lawyer-services/img/htdx.png"),
        [LAWYER_SERVICE_ENUM.wsdx]: require("@/pages/sub/lawyer-home/lawyer-services/img/wsdx.png"),
        [LAWYER_SERVICE_ENUM.htsc]: require("@/pages/sub/lawyer-home/lawyer-services/img/htsc.png"),
        [LAWYER_SERVICE_ENUM.sszd]: require("@/pages/sub/lawyer-home/lawyer-services/img/sszd.png"),
        [LAWYER_SERVICE_ENUM.lsh]: require("@/pages/sub/lawyer-home/lawyer-services/img/lsh.png"),
      };

      return headerImages[this.scene];
    },
  },
};
</script>

<style lang="scss" scoped>
.header-image {
  display: block;
  width: 375px;
  height: 310px;
}
</style>
