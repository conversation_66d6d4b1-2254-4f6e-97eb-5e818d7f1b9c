<template>
  <div class="caseSource-detail">
    <status-top
      :detail="detail"
      :isShowBanner="isShowBanner"
      :getRemainClueNum="getRemainClueNum"
    />
    <div class="content-wrapper">
      <div class="line">
        <div class="label">
          案件类型
        </div>
        <div class="content">
          {{ detail.typeLabel }}
        </div>
      </div>
      <div class="line">
        <div class="label">
          案件城市
        </div>
        <div class="content">
          {{ detail.regionName }}
        </div>
      </div>
      <div class="line">
        <div class="label">
          案件诉求
        </div>
        <div class="content">
          {{ detail.appealTypeStr }}
        </div>
      </div>
      <div class="line">
        <div class="label">
          用户需求
        </div>
        <div class="content">
          {{ detail.info }}
        </div>
      </div>
      <div class="line">
        <div class="label">
          涉案金额
        </div>
        <div class="content">
          {{ detail.amountGradeStr }}
        </div>
      </div>
      <div
        v-if="!isSpecial"
        class="tips"
      >
        <img
          :src="getCaseGradeStr.img"
          alt=""
          class="img"
        >
      </div>
    </div>
    <div
      v-if="!isSpecial"
      class="content-wrapper"
    >
      <div class="line">
        <div class="label">
          联系方式
        </div>
        <div class="content">
          案件开始跟进后可见
        </div>
      </div>
      <div class="line">
        <div class="label">
          {{ type === '2' ? '独享价' : '案源价格' }}
        </div>
        <div class="content flex flex-space-between">
          <div v-if="type !== '2'">
            <template v-if="getRemainClueNum > 0">
              <span class="cur-price">优惠价 ¥0</span>
              <span class="old-price">¥{{ priceNumber(detail.biddingNum) }}</span>
            </template>
            <span
              v-else
              class="cur-price dark"
            >¥{{ priceNumber(detail.biddingNum) }}</span>
          </div>
          <div v-else>
            <span class="cur-price">¥{{ priceNumber(detail.fixedAmount) }}</span>
          </div>
          <div
            v-if="detail.fixedAmount && type === '1'"
            class="price2 flex flex-align-center"
            @click="questionClick"
          >
<!--            <img-->
<!--              alt=""-->
<!--              class="img"-->
<!--              src="@/pages/index/img/ques.png"-->
<!--            >-->
            <span class="text">独享 ¥{{ priceNumber(detail.fixedAmount) }} </span>
          </div>
        </div>
      </div>
    </div>
    <div
      v-else
      class="content-wrapper"
    >
      <div class="line">
        <div class="label">
          联系方式
        </div>
        <div class="content flex-space-between flex">
          <template v-if="detail.feedBackStatus">
            已退单无法查看
          </template>
          <template v-else>
            <span>{{ phoneNumber }}</span>
            <span
              class="call"
              @click="callPhone"
            >拨打电话 <span class="iconfont icon-erjiyoujiantou" /></span>
          </template>
        </div>
      </div>
      <div class="line">
        <div class="label">
          案源价格
        </div>
        <div class="content">
          <span class="cur-price dark">¥{{ priceNumber(detail.biddingNum) }}</span>
        </div>
      </div>
    </div>
    <div
      v-if="getQaList.length"
      class="content-wrapper"
    >
      <div class="title">
        平台跟进信息
      </div>
      <div class="list-wr">
        <div
          v-for="(item, index) in getQaList"
          :key="index"
          class="li"
        >
          <span class="label">{{ item.label }}</span>
          <span class="content">{{ item.inputType === 1 ? priceNumber(item.value) : item.value }}</span>
        </div>
      </div>
    </div>
    <div class="content-wrapper">
      <div class="title flex-space-between flex">
        <span>服务流程</span>
      </div>
      <div class="process flex">
        <div class="line" />
        <div class="process-item">
          <span class="num">1</span>
          <div>
            <div class="txt">
              锁定案源
            </div>
            <div>开始跟进</div>
          </div>
        </div>
        <div class="process-item">
          <span class="num">2</span>
          <div>
            <div class="txt">
              跟进中
            </div>
            <div
              v-if="detail.fixedAmount && type === '2'"
              class="only-one"
            >
              个人独享
            </div>
          </div>
        </div>
        <div class="process-item">
          <span class="num">3</span>
          <div>
            <div class="txt">
              结束跟进
            </div>
            <div>标记结果</div>
          </div>
        </div>
      </div>
    </div>
    <fixed-buttons
      :currentCaseText="getCaseGradeStr.label"
      :detail="detail"
      :isSpecial="isSpecial"
      :remainClueNum="getRemainClueNum"
      :type="type"
      @refreshCaseServer="refreshCaseServer"
    />
    <u-modal
      :show="isCertStatusNotShow"
      :content="modal1.content"
      :title="modal1.title"
      :customStyle="{borderRadius: '16px'}"
      lineBorderColor="#eeeeee"
      showCancelButton
      cancelText="再考虑下"
      confirmText="立即认证"
      @confirm="confirm"
      @cancel="isCertStatusNotShow = false"
    />
    <u-modal
      :show="isCertStatusAuditShow"
      :content="modal2.content"
      :title="modal2.title"
      :customStyle="{borderRadius: '16px'}"
      lineBorderColor="#eeeeee"
      confirmText="我知道了"
      @confirm="isCertStatusAuditShow=false"
    />
    <app-popup
      mode="center"
      round="16"
      :show="errorShow"
      :safeAreaInsetBottom="false"
      bgColor="transparent"
    >
      <div class="error-wrapper">
        <div>
          <img
            class="img"
            src="@/pages/caseSource/img/<EMAIL>"
            alt=""
          >
        </div>
        <div class="title">
          温馨提示
        </div>
        <div class="info">
          下手慢啦~，当前案源已被其他律师锁定
          去案源广场看看其他案源吧
        </div>
        <div class="btns flex">
          <div
            class="btn"
            @click="errorShow=false"
          >
            我知道了
          </div>
          <div
            class="btn color-text"
            @click="turnToCaseSourceSquare"
          >
            去案源广场
          </div>
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
import { lawyerCaseSourceDetail, orderCenterSourceDetail, remainClueNum } from "@/api";
import { priceNumber } from "@/libs/tool";
import FixedButtons from "@/pages/caseSource/components/fixedButtons.vue";
import StatusTop from "@/pages/caseSource/components/statusTop.vue";
import { getNewServerCall } from "@/api/im";
import { CASE_GRADE } from "@/enum";
import { filterEnum, shareAppMessage, shareTimeline } from "@/libs/tools";
import { getCurrentPageRoute, turnToCaseSourceSquare, turnToLawyerAuthResultPage } from "@/libs/turnPages";
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";

export default {
  components: { AppPopup, StatusTop, FixedButtons },
  data() {
    return {
      id: "",
      detail: {},
      type: null,
      currentCaseText: "",
      phoneNumber: "",
      isShowBanner: true,
      remainClueNumList: [],
      aboutPriceShow: false,
      isCertStatusNotShow: false,
      isCertStatusAuditShow: false,
      modal1: {
        title: "暂未认证律师身份, 请立即前往认证",
        content: "认证后即可获得平台赠送的本地精准案源",
      },
      modal2: {
        title: "认证审核中，联系客服催一催进度",
        content: "认证后即可获得平台赠送的本地精准案源",
      },
      errorShow: false,
    };
  },
  onLoad(options) {
    this.id = options.id;
    this.type = options.type;
  },
  onShow() {
    this.getDetail();
  },
  onHide() {
    this.isCertStatusNotShow = false;
    this.isCertStatusAuditShow = false;
    this.errorShow = false;
    this.aboutPriceShow = false;
  },
  onShareAppMessage() {
    return shareAppMessage({
      title: `【${this.detail.regionName}】新增【${this.detail.typeLabel}】案源1条，速来抢案！`,
      imageUrl: require("@/img/share/<EMAIL>"),
      // 分享出去的页面路径固定 type=1
      path: getCurrentPageRoute().fullPath.replace(/type=\d/, "type=1"),
    });
  },
  onShareTimeline() {
    return shareTimeline({
      title: `【${this.detail.regionName}】新增【${this.detail.typeLabel}】案源1条，速来抢案！`,
      imageUrl: require("@/img/share/<EMAIL>")
    });
  },
  computed: {
    // 是否是抢单之后的案源
    isSpecial() {
      return this.type === "3" || this.type === "4";
    },
    // 获取案源等级样式
    getCaseGradeStr() {
      const tip7 = require("@/pages/caseSource/img/detail-tip7.png");
      const tip2 = require("@/pages/caseSource/img/detail-tip2.png");
      const tip1 = require("@/pages/caseSource/img/detail-tip1.png");
      const tip8 = require("@/pages/caseSource/img/detail-tip8.png");
      const imgs = { tip7, tip2, tip1, tip8 };
      const res = filterEnum(CASE_GRADE, this.detail.caseGrade) || {};
      return { ...res, img: imgs["tip" + res.value] };
    },
    // 获取跟进信息和问题列表
    getQaList() {
      const problemVos = this.detail.problemVos || [];
      const followInfo = this.detail.followInfo || [];
      return followInfo.map((item, index) => {
        const { caseSourceV2ProblemId, resultValue } = item;
        const problem = problemVos.find(i => i.id === caseSourceV2ProblemId) || {};
        const resultDetails = problem.resultDetails;
        const value = resultDetails.find(i => i.answerValue == resultValue) || {};
        return {
          label: problem.problemInfo,
          value: problem.inputType === 1 ? resultValue : value.answerLabel,
          inputType: problem.inputType, // 1 显示金额
        };
      });
    },
    // 获取剩余条数
    getRemainClueNum() {
      const { remainClueNumList = [] } = this;
      const { caseGrade } = this.detail;
      const res = remainClueNumList.find(item => item.caseGrade === caseGrade) || {};
      return res.remainClueNum;
    },
    /* 未认证*/
    isCertStatusNot(){
      return this.$store.getters["user/isCertStatusNot"];
    },
    /* 认证中*/
    isCertStatusAudit(){
      return this.$store.getters["user/isCertStatusAudit"];
    },
    /* 认证失败*/
    isCertStatusFail(){
      return this.$store.getters["user/isCertStatusFail"];
    }
  },
  methods: {
    turnToCaseSourceSquare,
    priceNumber,
    // 查看详情
    getDetail() {
      if (!this.isSpecial) {
        // 获取案源广场的案源详情
        orderCenterSourceDetail(this.id).then(res => {
          this.detail = res.data;
          if(this.detail.status === 3){
            this.errorShow = true;
          }
        });
      } else {
        this.isShowBanner = false;
        // 获取锁定的案源详情
        lawyerCaseSourceDetail(this.id).then(res => {
          this.detail = res.data;
        });
        // 获取电话号码
        getNewServerCall({ caseSourceServerV2Id: this.id }).then(({ data }) => {
          this.phoneNumber = data.realPhone;
        });
      }
      // 获取剩余条数数
      remainClueNum().then(({ data = [] }) => {
        this.remainClueNumList = data;
      });
    },
    // 拨打电话
    callPhone() {
      uni.makePhoneCall({
        phoneNumber: this.phoneNumber
      });
    },
    // 点击问题
    async questionClick() {
      await this.checkAuth();
      this.aboutPriceShow = true;
    },
    // 刷新案源
    refreshCaseServer({ id, type } = {}) {
      this.type = type || this.type;
      this.id = id || this.id;
      this.getDetail();
    },
    // 检查权限
    checkAuth() {
      return new Promise((resolve, reject) => {
        // 认证弹窗
        if(this.isCertStatusNot || this.isCertStatusFail) {
          this.isCertStatusNotShow = true;
          reject();
        } else if(this.isCertStatusAudit) {
          this.isCertStatusAuditShow = true;
          reject();
        } else {
          resolve();
        }
      });
    },
    // 认证弹窗-立即认证
    confirm() {
      this.isCertStatusNotShow = false;
      turnToLawyerAuthResultPage();
    },
  }
};
</script>

<style lang="scss" scoped>
.caseSource-detail {
  padding: 10px 12px 150px;

  .content-wrapper {
    padding: 16px;
    background: #fff;
    border-radius: 8px 8px 8px 8px;
    position: relative;
    & + .content-wrapper {
      margin-top: 12px;
    }
    .line {
      display: flex;

      .label {
        font-size: 14px;
        font-weight: 400;
        color: #6B6B6F;
        width: 56px;
        flex-shrink: 0;
      }

      .content {
        font-size: 14px;
        font-weight: 400;
        color: #46474B;
        margin-left: 24px;
        flex: 1;
        white-space: normal;
        word-break: break-word;
        .cur-price {
          color: #F78C3E;
          &.dark{
            color: #333;
          }
        }

        .old-price {
          color: #999999;
          text-decoration: line-through;
          margin-left: 8px;
        }

        .price2 {
          line-height: 16px;
          background: #EBF7F0;
          border-radius: 16px;;
          font-size: 12px;
          font-weight: 400;
          color: #22BF7E;
          padding-right: 8px;
          padding-left: 1px;

          .img {
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }
        }

        .call {
          font-size: 14px;
          font-weight: 400;
          color: #3887F5;
        }
      }

      & + .line {
        margin-top: 16px;
      }
    }

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #111111;
      margin-bottom: 12px;

      .other {
        font-size: 12px;
        font-weight: 400;
        color: #999999;

      }
    }

    .list-wr {
      background: #F4F8FE;
      border-radius: 8px 8px 8px 8px;
      padding: 14px 16px;

      .li {
        display: flex;
        justify-content: space-between;

        .label {
          font-size: 14px;
          font-weight: 400;
          color: #666;
          flex-shrink: 0;
        }

        .content {
          font-size: 14px;
          font-weight: 400;
          color: #333;
        }

        & + .li {
          margin-top: 28px;
        }
      }
    }

    .process {
      display: flex;
      justify-content: space-between;
      position: relative;
      margin-top: 2px;

      .process-item {
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        line-height: 14px;
        text-align: center;
        position: relative;
        z-index: 2;

        .num {
          display: inline-block;
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          border-radius: 50%;
          background: #ccc;
          font-size: 12px;
          font-weight: 500;
          color: #fff;
          margin-bottom: 8px;
        }

        & + .process-item {
          margin-left: 16px;
        }

        .only-one {
          background: #EBF7F0;
          border-radius: 8px 8px 8px 8px;
          padding: 2px 8px;
          color: #22BF7E;
        }

        .txt {
          margin-bottom: 2px;
        }
      }

      .line {
        position: absolute;
        top: 9px;
        left: 50%;
        transform: translateX(-50%);
        width: 270px;
        height: 2px;
        background: #ccc;
        z-index: 1;
      }
    }

    .iconfont {
      vertical-align: bottom;
    }

    .tips {
      position: absolute;
      top: 16px;
      right: -4px;

      .img {
        width: 72px;
        height: 24px;
      }
    }
  }
  .error-wrapper{
    background: #fff;
    text-align: center;
    border-radius: 16px;
    width: 311px;
    .img{
      width: 160px;
      height: 120px;
      margin: 24px 0 12px;
    }
    .title{
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      margin-bottom: 12px;
    }
    .info{
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      padding:0 24px;
    }
    .btns{
      margin-top: 24px;
      line-height: 46px;
      border-top: 1px solid #eee;
      font-size: 16px;
      font-weight: 400;
      color: #000000;
      .btn{
        flex: 1;
        box-sizing: border-box;
        &:first-child{
          border-right: 1px solid #eee;
        }
      }
    }
  }
}
</style>
