<template>
  <div>
    <div class="flex items-center pb-[12px] border-0 border-solid border-b border-[#EEEEEE]">
      <div class="position-relative shrink-0">
        <img
          :src="data.imgUrl"
          mode="aspectFill"
          alt=""
          class="w-[32px] h-[32px] rounded-[16px]"
        >
        <div
          v-if="data.online"
          class="absolute top-[24px] absolute-x-center w-[28px] h-[14px] bg-[#22BF7E] rounded-[4px] border-[1px] border-solid border-[rgba(34,191,126,0.2)] box-border flex items-center justify-center"
        >
          <div class="text-[10px] text-[#FFFFFF]">
            在线
          </div>
        </div>
      </div>
      <div class="font-bold text-[14px] text-[#333333] ml-[8px] shrink-0">
        {{ data.realName }} 律师
      </div>
    </div>
    <div
      class="w-[88px] h-[26px] bg-[linear-gradient(_116deg,_#71B5FF_0%,_#2676E4_100%)] rounded-[50px] flex items-center justify-center box-border mx-auto mt-[6px]"
      @click="callPhone"
    >
      <div class="text-[13px] text-[#FFFFFF]">
        立即联系
      </div>
    </div>
    <find-lawyer-popup
      v-model="popup"
      :data="data"
    />
  </div>
</template>

<script>
import FindLawyerPopup from "@/pages/mine/components/FindLawyerPopup.vue";
import { turnToLawyerAuthResultPageToLogin } from "@/libs/turnPages";

export default {
  name: "MineRecentLawyerCard",
  components: { FindLawyerPopup },
  props: {
    data: {
      type: Object,
      default: () => ({}),
      required: true
    }
  },
  data(){
    return {
      popup: false
    };
  },
  methods: {
    /** 拨打电话 */
    callPhone() {
      turnToLawyerAuthResultPageToLogin(() => {
        this.popup = true;
      });
    }
  }
};
</script>
