import { WS_MESSAGE_TYPE } from "yoc-im-web";

export default {
  inheritAttrs: false,
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    theme: {
      type: String,
      default: "",
      validator: (value) => ["white", "blue", ""].includes(value)
    }
  }
};

/* 自定义结构*/
/*
  customEvent:"server"
  customExts:{
    content:""
    ext:{}
    showType:""
    style:""
    subType:""
    title:""
   }
*/

/* im 自定义字段解析*/
export const customComputed = {
  computed: {
    customData() {
      return this.data.msgType === WS_MESSAGE_TYPE.CUSTOM ? this.data.msg : {};
    },
    customExts() {
      return this.customData.customExts || {};
    },
    customExtsinExt() {
      return this.customExts.ext || {};
    }
  }
};

/* 当条信息和会话信息  */
/* ! 需要在外面 手动引入 customComputed conversationInfoStateProps这两个mixins */
export const messageAndConversationInfoComputed = {
  computed: {
    getCurrentToken() {
      return this.getConversationInfo.getCurrentToken;
    },
    //   是不是自己发送的消息
    isSelf() {
      return this.data.from === this.getCurrentToken;
    }
  }
};