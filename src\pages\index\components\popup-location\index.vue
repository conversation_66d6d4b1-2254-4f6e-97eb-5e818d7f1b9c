<template>
  <div>
    <float-popup
      :contentWrapperStyle="contentWrapperStyle"
      :show="show"
      @maskClick="$emit('update:show', false)"
    >
      <view class="geo-wrapper">
        <div
          v-if="isLocation"
          class="geo-wrapper-title flex flex-align-center flex-space-between"
        >
          <div class="geo-current">
            当前：{{ currentSelect || "暂无定位" }}
          </div>
          <div
            class="geo-reset flex flex-align-center"
            @click="handleGetLocation"
          >
            <img
              alt=""
              class="location-icon"
              src="@/pages/index/imgs/location-blue.png"
            >
            {{ locationText }}
          </div>
        </div>
        <div class="geo-wrapper-con flex">
          <div class="left-province">
            <scroll-view
              class="scroll-view"
              scrollY="true"
            >
              <div
                v-for="item in province"
                :key="item.code"
                :class="[
                  {'!text-[#3887F5]':item.code === provinceActive}
                ]"
                class="geo-line fontStyle flex flex-align-center"
                @click="provinceClick(item)"
              >
                {{ item.name }}
                <div
                  v-if="item.code === provinceActive"
                  class="active"
                />
              </div>
            </scroll-view>
          </div>
          <div class="right-city">
            <scroll-view
              class="scroll-view"
              scrollY="true"
            >
              <div
                v-for="item in city"
                :key="item.code"
                :class="[
                  {'!text-[#3887F5]':item.code === cityActive}
                ]"
                class="geo-line fontStyle flex flex-align-center flex-space-between"
                @click="cityClick(item)"
              >
                {{ item.name }}
                <span
                  v-if="item.code === cityActive"
                  class="active iconfont icon-a-zhengquewancheng"
                />
              </div>
            </scroll-view>
          </div>
        </div>
      </view>
    </float-popup>
  </div>
</template>

<script>
import { getArea } from "@/api/index.js";
import FloatPopup from "@/pages/index/components/floatPopup.vue";
import { getLocation } from "@/libs/getLocation";

export default {
  name: "PopupLocation",
  components: { FloatPopup },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    nationwide: {
      type: Boolean,
      default: false,
    },
    contentWrapperStyle: {
      type: Object,
      default: () => ({}),
    },
    /* 需不需要定位 */
    isLocation: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      province: [],
      city: [],
      provinceActive: null,
      cityActive: null,
      cityCode: null,
      currentSelect: "暂无定位",
      locationText: "重新定位",
    };
  },
  mounted() {
    this.getAreaRequest();
    if (this.isLocation) {
      this.reqGetLocation();

    /*   bindOnHook.call(this, "onShow", () => {
        this.reqGetLocation();
      }); */
    }
  },
  methods: {
    /* 定位*/
    handleGetLocation() {
      this.$emit("handleGetLocation");
      this.reqGetLocation();
    },
    /* 请求定位*/
    reqGetLocation() {
      // 获取定位
      getLocation()
        .then(({ cityCode, cityName, provinceCode, provinceName }) => {
          this.currentSelect = cityName;
          this.locationText = "重新定位";
          this.provinceActive = null;
          this.cityActive = null;
          // this.$emit('setCity',{name: city})
          this.$emit("getLocationCallback", { name: cityName });
          this.$emit("update:show", false);
          this.$emit("setCity", {
            name: cityName,
            code: cityCode,
            provinceName,
            provinceCode,
          });
        })
        .catch(() => {
          this.locationText = "无法获取定位";
          // 默认全国
          this.$emit("setCity", { name: "全国", code: "" });
        });
    },

    /* 获取地图*/
    getAreaRequest(type = 1, code = null, name) {
      if (code === "全国") {
        this.city = [{ code: "", type: 2, name: "全国" }];
      } else {
        getArea({ type, code }).then(({ data }) => {
          if (type === 1) {
            this.province = data;
            if (this.nationwide)
              this.province.unshift({ code: "全国", type: 1, name: "全国" });
          }
          if (type === 2) {
            /* 有些城市是自治区 要特殊判断*/
            this.city =
              this.nationwide && data.length > 1
                ? [
                  {
                    code: code,
                    type: 2,
                    name: "所有城市",
                    provinceName: name,
                    province: code,
                  },
                  ...data,
                ]
                : data;
          }
        });
      }
    },
    /* 省级选择*/
    provinceClick(item) {
      this.provinceActive = item.code;
      this.getAreaRequest(2, item.code, item.name);
    },
    /* 城市选择*/
    cityClick({ name, code, provinceName, province }) {
      this.cityActive = code;
      this.$emit("update:show", false);
      const data = { name, code, provinceName, province };
      /* 直辖市或者全省的时候*/
      if (code === province) {
        data.name = provinceName;
        data.code = "";
      }
      this.$emit("setCity", data);
    },
    close() {
      this.$emit("update:show", false);
    },
  },
};
</script>

<style lang="scss" scoped>
.geo-wrapper {
  border-radius: 0 0 16px 16px;

  &-title {
    height: 44px;
    padding: 0 16px;
    border-bottom: 1px solid #eee;

    .geo-current {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
    }

    .geo-reset {
      font-size: 14px;
      font-weight: 400;
      color: #3887f5;

      .location-icon {
        width: 16px;
        height: 16px;
        margin-right: 2px;
      }
    }
  }

  &-con {
    .scroll-view {
      height: 352px;
    }

    .fontStyle {
      font-size: 14px;
      font-weight: 400;
      color: #333333;
    }

    .left-province {
      flex: 0 0 130px;

      .geo-line {
        height: 44px;
        position: relative;
        padding-left: 16px;

        .active {
          position: absolute;
          width: 4px;
          height: 20px;
          background: #3887f5;
          border-radius: 20px 20px 20px 20px;
          left: 0;
        }
      }
    }

    .right-city {
      flex: 1;
      border-left: 1px solid #eee;
      padding-right: 16px;
      .geo-line {
        padding: 12px 0 16px 12px;
        box-sizing: border-box;
        height: 44px;

        .iconfont {
          font-size: 20px;
          color: #3887f5;
        }
      }
    }
  }
}
</style>
