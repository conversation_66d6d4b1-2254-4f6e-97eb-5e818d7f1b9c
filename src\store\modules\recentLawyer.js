import { LOCAL_STORAGE_KEY } from "@/libs/config";

export default {
  namespaced: true,
  state: {
    recentLawyerList: uni.getStorageSync(LOCAL_STORAGE_KEY.RECENT_LAWYER) || [],
  },
  mutations: {
    PUSH_RECENT_LAWYER_LIST(state, data) {
      const find = state.recentLawyerList.find((item) => item.id === data.id);

      if (!find) {
        // 最多 3 个
        state.recentLawyerList.unshift(data);
        
        if (state.recentLawyerList.length > 3) {
          state.recentLawyerList.pop();
        }
      }

      uni.setStorageSync(LOCAL_STORAGE_KEY.RECENT_LAWYER, state.recentLawyerList);
    },
    CLEAR_RECENT_LAWYER_LIST(state) {
      state.recentLawyerList = [];
      uni.removeStorageSync(LOCAL_STORAGE_KEY.RECENT_LAWYER);
    }
  },
};
