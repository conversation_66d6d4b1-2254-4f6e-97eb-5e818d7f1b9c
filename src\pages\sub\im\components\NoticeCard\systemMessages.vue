<template>
  <div class="notice-card-item">
    <div class="header flex flex-align-center">
      <img
        v-if="currentEnum.icon"
        class="icon"
        :src="currentEnum.icon"
        alt=""
      >
      <p class="title text-ellipsis flex-1">
        {{ data.messageTitle }}
      </p>
      <p class="date">
        {{ data.createTime && parseTimeDiffToday(data.createTime) }}
      </p>
    </div>
    <div class="desc">
      {{ data.toText }}
    </div>
    <div
      v-if="currentEnum.text"
      class="link flex flex-align-center flex-space-end"
      @click="jump"
    >
      {{ currentEnum.text }}
      <i class="iconfont icon-erjiyoujiantou" />
    </div>
  </div>
</template>

<script>
import { parseTimeDiffToday } from "@/libs/tools";

export default {
  name: "SystemMessages",
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      }
    },
    currentEnum: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  methods: {
    parseTimeDiffToday,
    jump(){
      this.$emit("jump");
    }
  }
};
</script>

<style scoped lang="scss">

.notice-card-item{
  border-radius: 8px 8px 8px 8px;
  padding: 12px;
  margin-top: 12px;
  background: white;
  .header{
    .icon{
      width: 24px;
      height: 24px;
      border-radius: 52px 52px 52px 52px;
      padding-right: 8px;
    }
    .title{
      font-size: 16px;
      font-weight: 600;
      word-break: break-all;
      color: #333333;
    }
    .date{
      font-size: 12px;
      font-weight: 400;
      color: #999999;
    }
  }
  .desc{
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    word-break: break-all;
    padding-top: 8px;
  }
  .link{
    font-size: 14px;
    font-weight: 400;
    padding-top: 8px;
    color: #3887F5;
    .iconfont{
      padding-left: 4px;
      color: #3887F5;
      font-size: 16px;
    }
  }
}
</style>