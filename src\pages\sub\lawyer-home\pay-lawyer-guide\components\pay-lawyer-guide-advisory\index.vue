<template>
  <div>
    <div class="advisory-title">
      请选择服务：
    </div>
    <div
      class="overflow-x-auto advisory-container"
    >
      <div class="flex">
        <!-- 兼容支付宝       -->
        <!-- #ifdef MP-ALIPAY  -->
        <div
          v-for="serviceInfo in serviceList"
          :key="serviceInfo.serviceCode"
          class="advisory-box-item"
        >
          <PayLawyerGuideAdvisoryCard
            :itemCount="serviceList.length"
            :selected="value === serviceInfo.serviceCode"
            :serviceInfo="serviceInfo || {}"
            @click="handleSelect(serviceInfo)"
          />
        </div>
        <!-- #endif -->
        <!-- #ifndef  MP-ALIPAY -->
        <PayLawyerGuideAdvisoryCard
          v-for="serviceInfo in serviceList"
          :key="serviceInfo.serviceCode"
          :itemCount="serviceList.length"
          :selected="value === serviceInfo.serviceCode"
          :serviceInfo="serviceInfo"
          class="advisory-box-item"
          @click="handleSelect(serviceInfo)"
        />
        <!-- #endif -->
      </div>
    </div>
    <p class="advisory-tips">
      <span class="text-[#3887F5]">{{ selectedServiceInfo.serviceName }}：</span>{{ selectedServiceInfo.info }}
    </p>
  </div>
</template>

<script>
import PayLawyerGuideAdvisoryCard
  from "@/pages/sub/lawyer-home/pay-lawyer-guide/components/pay-lawyer-guide-advisory/pay-lawyer-guide-advisory-card.vue";
import { getCurrentPageRoute } from "@/libs/turnPages.js";

export default {
  name: "PayLawyerGuideAdvisory",
  components: { PayLawyerGuideAdvisoryCard },
  props: {
    /** 当前律师对应的服务列表 */
    serviceManageList: {
      type: Array,
      required: true,
      default: () => [],
    },
    value: {
      type: [Number, String],
      required: true,
      default: 0,
    },
  },
  data() {
    return {
      serviceList: [],
      selectedServiceInfo: {},
    };
  },
  watch: {
    /** 每次初始化时，如果有serverCode，则选中serverCode，否则默认选中第一个 */
    serviceManageList: {
      handler(val) {
        if (this.haveType()) return;
        if (this.routerHaveServerCode()) return;

        this.serviceList = val || [];
        this.selectedServiceInfo = val?.[0] || {};
        this.$emit("input", val?.[0]?.serviceCode || 0);
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    /**
     * 这里是为了实现用户从律师主页点击服务到当前页面，置顶选中的服务
     * 如果有缓存则重新排序列表
     * @param serverCode 服务code
     */
    sortList(serverCode) {
      const selectService = this.serviceManageList?.find(
        (item) => Number(item.serviceCode) === Number(serverCode)
      );

      // 过滤掉当前选中的服务
      const newList = this.serviceManageList?.filter(
        (item) => Number(item.serviceCode) !== Number(serverCode)
      );

      // 将当前选中的服务放到第一位
      this.serviceList = [selectService, ...newList];

      this.selectedServiceInfo = selectService || {};
      this.$emit("input", selectService?.serviceCode || 0);
    },
    /** 如果type 为3 则从缓存中获取数据 */
    haveType() {
      const type = getCurrentPageRoute().query.type;

      // 如果type 为3 则从缓存中获取数据
      if (type === "3") {
        const serverCode = Number(getCurrentPageRoute().query.serverCode);
        // 这里以前取的缓存中的值，现在改成从路由中取
        //  const serverCode = Number(getPayLawyerGuideStorage().serverCode);

        // 如果不存在则不排序
        if (!serverCode) {
          this.serviceList = this.serviceManageList || [];
          this.selectedServiceInfo = this.serviceManageList?.[0] || {};
          this.$emit("input", this.serviceManageList?.[0]?.serviceCode || 0);
          return true;
        }

        // this.sortList(serverCode);
        this.$emit("input", serverCode);
        return true;
      }

      return false;
    },
    /** 如果路由上面有serverCode则赋值这个值 */
    routerHaveServerCode() {
      console.log(getCurrentPageRoute().query, "getCurrentPageRoute().query");
      const serverCode = Number(getCurrentPageRoute().query.serverCode);

      // 如果路由上面有serverCode则赋值这个值
      if (serverCode) {
        this.$emit("input", serverCode);
        // 现在业务已经不需要排序了，如果这里启动，支付宝小程序中会的到0值
        // this.sortList();
        return true;
      }
      return false;
    },
    /** 选中服务 */
    handleSelect(serviceInfo) {
      this.selectedServiceInfo = serviceInfo;
      this.$emit("input", serviceInfo.serviceCode);
      console.log(serviceInfo.serviceCode, "选中的服务");
      // 切换时清除缓存
      uni.removeStorageSync("orderId");
    },
  },
};
</script>

<style lang="scss" scoped>
.advisory {
  &-title {
    padding: 0 0 16px 0;
    font-size: 16px;
    font-weight: bold;
    color: #333333;
    margin-right: 16px;
  }

  &-container {
    width: 351px;
    margin-left: -16px;
  }

  &-box {
    &-item {
      flex-shrink: 0;
    }

    // @extend .scroll-bar-none;
  }

  &-tips {
    margin-top: 16px;
    border-radius: 8px 8px 8px 8px;
    font-size: 12px;
    font-weight: 400;
    color: #666666;
    margin-right: 16px;
  }
}
</style>
