const fs = require("fs");
const path = require("path");
const request = require("request");
/** image 图片缓存地址 */
const stateLockUrl = path.resolve(__dirname, "./state-lock.json");
const addMemo = {};
// 如果不存在则创建文件
try {
  fs.statSync(stateLockUrl);
} catch (e) {
  fs.writeFileSync(stateLockUrl, JSON.stringify({}));
}

const memo = JSON.parse(fs.readFileSync(stateLockUrl, "utf-8"));

const uploadFile = (filUrl, fileName) => {
  return new Promise((resolve, reject) => {
    const url = "https://testapi.imlaw.cn/cms/upload/file";
    const readerStream = fs.createReadStream(filUrl);

    const formData = {
      // 普通文本
      modPath: "uniapp",
      // 文件
      file: readerStream,
    };

    if (memo[fileName]) {
      return resolve(memo[fileName]);
    }

    request.post({ url, formData }, function (error, response) {
      if (error) {
        reject(error);
      }

      console.log(filUrl);

      const imgUrl = JSON.parse(response.body).data;
      addMemo[fileName] = imgUrl;
      // 写入文件，不存在就创建
      fs.writeFileSync(stateLockUrl, JSON.stringify({ ...memo, ...addMemo }));
      console.log("上传成功", imgUrl);
      resolve(imgUrl);
    });
  });
};

/**
 * 获取指定路径下的所有图片
 * @param {string} dirPath
 */
function getFiles(dirPath) {
  const allPath = path.resolve(__dirname, dirPath);
  const files = fs.readdirSync(allPath);
  files.forEach((file) => {
    const filePath = path.resolve(dirPath, file);
    const f = fs.statSync(filePath);
    if (f.isDirectory()) {
      getFiles(filePath);
    } else {
      // 判断filePath是否是图片
      if (/\.(png|jpg|jpeg|gif)$/.test(filePath)) {
        const str = filePath.split("assets")[1];
        // 将 str 中的 \\ 转为 /
        const fileName = str.replace(/\\/g, "/");
        uploadFile(filePath, fileName);
      }
    }
  });
}

function main() {
  const dirPath = path.resolve(__dirname, "./src/assets");
  getFiles(dirPath);
}

main();
