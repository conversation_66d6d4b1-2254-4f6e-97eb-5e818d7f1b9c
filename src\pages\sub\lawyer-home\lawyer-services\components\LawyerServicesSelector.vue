<template>
  <div class="selector">
    <p class="selector-text">
      请选择服务：
    </p>
    <div class="service">
      <div
        v-for="item in list"
        :key="item.value"
        :class="{
          'service-item__selected': LAWYER_SERVICE_ENUM[value] === item.value,
        }"
        class="service-item"
        @click="selected(item)"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script>
import { LAWYER_SERVICE_ENUM } from "@/pages/sub/lawyer-home/lawyer-services/js";

export default {
  name: "LawyerServicesSelector",
  props: {
    value: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      LAWYER_SERVICE_ENUM,
      list: [
        {
          label: "合同代写",
          value: LAWYER_SERVICE_ENUM.contractWriting,
        },
        {
          label: "协议代写",
          value: LAWYER_SERVICE_ENUM.agreementWriting,
        },
        {
          label: "合同审查",
          value: LAWYER_SERVICE_ENUM.contractReview,
        },
        {
          label: "律师函",
          value: LAWYER_SERVICE_ENUM.lawyerLetter,
        },
      ],
    };
  },
  methods: {
    selected(item) {
      console.log(LAWYER_SERVICE_ENUM[this.value]);
      console.log(
        this.value,
        "this.valuethis.valuethis.valuethis.valuethis.valuethis.valuethis.valuethis.value"
      );
      console.log(item.value);
      this.$emit("input", item.value);
    },
  },
};
</script>

<style lang="scss" scoped>
.selector {
  padding: 4px 16px 16px 16px;
  box-sizing: border-box;
  background: #e5efff;

  &-text {
    font-size: 13px;
    font-weight: 400;
    color: #666666;
  }
}

.service {
  display: flex;
  margin-top: 8px;

  &-item {
    color: #333333;
    background: #ffffff;
    border-radius: 68px;
    opacity: 1;
    font-size: 13px;
    font-weight: 400;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 4px 12px;

    &:not(:last-child) {
      margin-right: 12px;
    }

    &__selected {
      background: #2b9cff;
      color: #ffffff;
    }
  }
}
</style>
