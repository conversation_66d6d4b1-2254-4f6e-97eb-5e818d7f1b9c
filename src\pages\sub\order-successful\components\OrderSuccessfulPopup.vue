<template>
  <app-popup :show="show">
    <div
      class="font-bold text-[16px] text-[#333333] py-[12px] text-center border-0 border-b-[1px] border-solid border-[#EEEEEE]"
    >
      奖励说明
    </div>
    <div
      class="px-[16px] py-[24px] text-[14px] text-[#333333] whitespace-pre-line"
    >
      <div
        v-for="(item, index) in info"
        :key="index"
      >
        <div class="font-bold">
          {{ item.title }}
        </div>
        <div>
          {{ item.content }}
        </div>
      </div>
    </div>
    <div
      class="w-[343px] h-[44px] bg-[#3887F5] rounded-[68px] flex items-center justify-center mx-auto my-[8px]"
      @click="show = false"
    >
      <div class="font-bold text-[16px] text-[#FFFFFF]">
        我知道了
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { dataDictionary } from "@/api";

export default {
  name: "OrderSuccessfulPopup",
  components: { AppPopup },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      infoObject: {},
    };
  },
  computed: {
    info() {
      return [
        {
          title: "基础奖励：",
          content: this.infoObject.lawyerInviteTimeDesc + "\n\n",
        },
        {
          title: "月度成单榜排名奖励：",
          content: this.infoObject.cdbRewardDesc + "\n\n",
        },
        {
          title: "沾喜气，瓜分红包：",
          content: this.infoObject.redPacketRuleDesc + "\n\n",
        },
        {
          title: "奖励发放规则：",
          content: this.infoObject.lawyerRewardRuleDesc,
        },
      ];
    },
    show: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit("input", value);
      },
    },
  },
  mounted() {
    this.getInfo();
  },
  methods: {
    getInfo() {
      dataDictionary({
        groupCode: "CONFIG_RULE",
      }).then(({ data }) => {
        const info = data.find((item) => item.value === "cdxb_rule_reward");

        try {
          info.remark = JSON.parse(info.remark);
        } catch (e) {
          console.log(e);
        }

        this.infoObject = info.remark;
      });
    },
  },
};
</script>
