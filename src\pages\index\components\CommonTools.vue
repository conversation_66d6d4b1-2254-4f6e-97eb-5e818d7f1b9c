<template>
  <div class="mx-[12px]  rounded-[8px] bg-white mt-[8px]">
    <p class="font-bold py-[16px] pl-[16px] text-[16px] text-[#333333]">
      常用工具
    </p>
    <div class="flex flex-wrap pb-[24px]">
      <div
        v-for="(item,index) in toolsList"
        :key="index"
        class="flex-1 relative"
        @click.stop="toTools(index)"
      >
        <img
          :src="item.icon"
          alt=""
          class="w-[32px] h-[32px] mx-auto block"
        >
        <p class="mt-[6px] text-[12px] text-[#333333] text-center">
          {{ item.title }}
        </p>
        <p
          v-if="item.tag"
          class="py-[3px] left-[50%] ml-[5px] top-[-10px] px-[4px] absolute font-[500] text-[11px] text-[#FFFFFF] bg-[linear-gradient(_109deg,_#FF913E_0%,_#F54A3A_100%)] rounded-tl-[10px] rounded-br-[10px] rounded-tr-[10px] rounded-bl-[2px] whitespace-nowrap"
        >
          {{ item.tag }}
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { toArbitrationFeeCalculator, toLawyerFeeCalculator } from "@/libs/turnPages";

export default {
  name: "CommonTools",
  data() {
    return {
      toolsList: [
        {
          title: "诉讼费计算",
          icon: require("@/pages/index/imgs/<EMAIL>"),
        },
        {
          title: "律师费计算",
          icon: require("@/pages/index/imgs/<EMAIL>"),
          click: toLawyerFeeCalculator,
          tag: "新功能"
        },
        {
          title: "工伤赔偿计算",
          icon: require("@/pages/index/imgs/<EMAIL>"),
        },
        {
          title: "仲裁费计算",
          icon: require("@/pages/index/imgs/<EMAIL>"),
          click: toArbitrationFeeCalculator,
          tag: "新功能"
        },
      ],
    };
  },
  methods: {
    toTools(index) {
      const data = this.toolsList[index];
      if(data.click){
        data.click();
        return;
      }
      uni.showToast({
        title: "全力开发中敬请期待",
        icon: "none",
      });
    },
  },
};
</script>

<style scoped lang="scss">

</style>