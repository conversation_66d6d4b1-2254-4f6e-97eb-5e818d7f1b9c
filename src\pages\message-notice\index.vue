<template>
  <login-layout>
    <div class="flex bg-white items-center px-[16px] py-[5px]">
      <h1 class="center font-[600] text-[24px] text-[#333333] pr-[12px]">
        信息
        <span class="font-normal text-[14px] text-[#333333]">({{ imNoReadNum }})</span>
      </h1>
      <div
        class="center"
        @click.stop="clearConverReadAll"
      >
        <i class="iconfont icon-shuoming1 text-[16px] text-[#999999]" />
        <p class="text-[14px] text-[#999999] pl-[4px]">
          全部已读
        </p>
      </div>
    </div>
    <div class="system-notifications py-[12px] grid grid-cols-4 gap-x-[13px] px-[16px]">
      <div
        v-for="(i,index) in systemMsgList"
        :key="index"
        class=""
        @click="jump(i)"
      >
        <div class="position-relative icon-container">
          <img
            :src="i.icon"
            alt=""
            class="w-[76px] block h-[80px]"
          >
          <div
            v-if="systemMsg[i.key]>0"
            class="absolute top-[2px] right-[2px]"
          >
            <u-badge
              :isDot="true"
              type="error"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="messages-list">
      <scroll-view scrollY="true">
        <uni-swipe-action>
          <uni-swipe-action-item
            v-for="item in filterFindsList"
            :key="item.id"
            :disabled="item.swipeDisabled"
            :rightOptions="item.top===1?options2:options1"
            @click="handleTop(item)"
          >
            <div
              class="message-item flex"
              @click="handleClick(item)"
            >
              <div class="avatar position-relative">
                <img
                  :src="item.userInfo.img"
                  alt=""
                  class="icon"
                >
                <div
                  v-if="item.unread_num>0"
                  class="box position-absolute"
                >
                  <u-badge
                    :value="item.unread_num"
                    bgColor="#EB4738"
                    max="99"
                    type="error"
                  />
                </div>
              </div>
              <div class="content">
                <div class="top flex flex-align-center flex-space-between">
                  <p class="user-name flex flex-align-center">
                    {{ item.userInfo.name }}
                    <span
                      v-if="item.tagConfig&&item.tagConfig.text"
                      :style="item.tagConfig.style"
                      class="tag"
                    >{{ item.tagConfig.text }}</span>
                  </p>
                  <p class="date">
                    {{ item.updateTime && parseTimeDiffToday(item.updateTime) }}
                  </p>
                </div>
                <div
                  v-if="item.msgType==='getLawyerComplaint'"
                  class="msg text-ellipsis"
                  v-html="item.lastMsg"
                />
                <div
                  v-else
                  class="msg text-ellipsis"
                >
                  {{ item.lastMsgType===1?item.lastMsg:`[自定义消息]` }}
                </div>
              </div>
              <i
                v-if="item.top===1"
                class="badge iconfont icon-zding"
              />
            </div>
          </uni-swipe-action-item>
        </uni-swipe-action>
      </scroll-view>
    </div>
    <div class="placeholder-tip">
      <div
        v-if="!isLogin"
        class="placeholder-container"
      >
        <img
          alt=""
          class="tip-img"
          src="@/pages/message-notice/imgs/no-data3.png"
        >
        <p class="tip">
          登录后可使用消息功能
        </p>
        <p
          class="btn"
          @click="toLogin"
        >
          去登录
        </p>
      </div>

      <div v-else>
        <certification-progress>
          <div
            v-if="isArrNull(filterFindsList)"
            class="placeholder-container"
          >
            <img
              alt=""
              class="tip-img"
              src="@/pages/message-notice/imgs/no-data3.png"
            >
            <p class="tip">
              一条消息都没有哦
            </p>
          </div>
        </certification-progress>
      </div>
    </div>
  </login-layout>
</template>

<script>
import UBadge from "@/uview-ui/components/u-badge/u-badge.vue";
import { http } from "yoc-im-web";
import { getRandomId, parseJSON, parseTimeDiffToday, whetherToLogIn } from "@/libs/tools.js";
import { isArrNull, isNull, isObjNull, isString } from "@/libs/basics-tools.js";
import { IM_CASE_SOURCE_TYPE, IM_CONVERSATION_TAG_MAP, SYSTEM_MSG_TYPE } from "@/enum/imCardEnum.js";
import UniSwipeAction from "@/components/UniSwipeAction/uni-swipe-action/uni-swipe-action.vue";
import UniSwipeActionItem from "@/components/UniSwipeAction/uni-swipe-action-item/uni-swipe-action-item.vue";
import { coreMessageNoRead, lawyerComplaintWarnMsg, orderCenterCaseSourceV2Clues } from "@/api/im.js";
import { addStyle } from "@/uview-ui/libs/function/index.js";
import {
  getCurrentPageRoute,
  turnPages,
  turnToDetailsOfTheComplaint,
  turnToImPage,
  turnToLawyerAuthResultPage, turnToServiceCenterPage,
  turnToSystemMessagesPage
} from "@/libs/turnPages.js";
import { getUserTokenStorage } from "@/libs/token.js";
import LoginLayout from "@/components/login/LoginLayout.vue";
import { requestUserInfoCallback } from "@/store/modules/user.js";
import CertificationProgress from "@/components/CertificationProgress/index.vue";

let topFindsList = {};

/* 全部的好友列表*/
let findsAllList = [];
export default {
  name: "Index",
  components: { CertificationProgress, LoginLayout, UniSwipeActionItem, UniSwipeAction, UBadge },
  data() {
    return {
      options1: [{
        text: "置顶",
        style: {
          backgroundColor: "#F78C3E"
        }
      }],
      options2: [{
        text: "取消置顶",
        style: {
          backgroundColor: "#F78C3E"
        }
      }],
      findsList: [],
      /* 系统消息*/
      systemList: [],
      /* 头部的系统消息*/
      systemMsgList: [{
        title: "服务助手",
        icon: require("@/pages/sub/im/imgs/<EMAIL>"),
        url: () => turnToSystemMessagesPage({ type: SYSTEM_MSG_TYPE.SERVER }),
        key: "serverNum"
      }, {
        title: "案源推送",
        icon: require("@/pages/sub/im/imgs/<EMAIL>"),
        url: () => turnToServiceCenterPage(),
        key: "caseSourcePush"
      }, {
        title: "系统通知",
        icon: require("@/pages/sub/im/imgs/<EMAIL>"),
        url: () => turnToSystemMessagesPage({ type: SYSTEM_MSG_TYPE.SYSTEM }),
        key: "systemNum"
      }, {
        title: "互动消息",
        icon: require("@/pages/sub/im/imgs/<EMAIL>"),
        url: "/pages/sub/im/interactive-msg/index",
        key: "interactionNum"
      }],
      systemMsg: {
        interactionNum: 0, //	否	Integer	互动未读消息数
        systemNum: 0, //	否	Integer	系统未读消息数
        serverNum: 0//	否	Integer	服务未读消息数
      },
      // im未读数量
      imNoReadNum: 0,
      /* 当前更新状态 是否在请求数据 */
      isUpdate: false
    };
  },
  onShow() {
    this.initList();
  },
  onHide() {
    this.$ImEventBus.$offRewrite();
    this.$store.dispatch("im/unloadOpened");
  },
  onReachBottom(){
    this.nextPage();
  },
  computed: {
    isLogin() {
      return (this.$store.getters["user/getToken"] || getUserTokenStorage());
    },
    filterFindsList() {
      return [...this.systemList, ...this.findsList];
    },
    getUserInfo(){
      return this.$store.getters["user/getUserInfo"];
    },
    /* 认证成功*/
    whetherOrNotToCertify(){
      return this.$store.getters["user/lawyerCertStatus"];
    }
  },
  methods: {
    parseTimeDiffToday,
    initList(){
      console.log(this.isLogin, "isLogin");
      const resetData = this.reset();
      this.isUpdate = false;
      this.systemMsg = resetData.systemMsg;
      requestUserInfoCallback(() => {
        //        this.systemList = [];
        /* 未登录 未认证 认证审核中 禁止浏览*/
        if(!this.isLogin || !this.whetherOrNotToCertify){
          return;
        }
        // Promise.all([this.getLawyerComplaint(), this.getLawyerAroundCase()]).then(datas => {
        //   datas.forEach(item => {
        //     if(isObjNull(item)) return;
        //     this.systemList.push(item);
        //   });
        // });
        this.getUnreadSystemMsg();


        this.$store.dispatch("im/onOpened", () => {
          console.log("im/onOpened");
          this.getFriendsList();
        });

        // 收到消息重新更新好友列表
        this.$ImEventBus.$onRewrite("onMessage", (message) => {
          console.log("收到消息重新更新好友列表onMessage", message);
          /* 收到消息后更新本地列表*/
          this.updateFriendsList(message);
        });

        // 收到指令消息 更新置顶
        this.$ImEventBus.$onRewrite("onOtherCommandMessage", (message) => {
          console.log("收到指令消息 更新置顶onCommand", message);
          const data = parseJSON(message.data.msg);
          /* 判断是否置顶 指令*/
          this.$nextTick(() => {
            if (data.code === "set_top_session") {
              /* 通过toC 设置findsList 置顶*/
              const findIndex = this.getFriendsListByType(message.data.toC);
              if (findIndex > -1) {
                const item = this.findsList[findIndex];
                /* 是否置顶，0.取消置顶，1，置顶*/
                this.$set(item, "top", data.status);
                /* 将置顶的数据 放在最前面*/
                if (data.status === 1) {
                  topFindsList[item.id] = item.id;
                } else {
                  delete topFindsList[item.id];
                }
                this.spliceFindsList(findIndex);
                console.log(topFindsList, "topFindsList");
              }
            }
          });
        });
      });

    },
    toLogin(){
      whetherToLogIn(() => {
        uni.reLaunch({
          url: getCurrentPageRoute().fullPath,
        });
      });
    },
    turnToLawyerAuthResultPage,
    isArrNull,
    jump(data){
      const { url } = data;
      whetherToLogIn(() => {
        if(!this.whetherOrNotToCertify) return false;
        if(isString(url)){
          uni.navigateTo({
            url
          });
        }else{
          url();
        }
      });
    },
    /* 获取未读的系统消息*/
    getUnreadSystemMsg(){
      coreMessageNoRead().then(({ data }) => {
        this.systemMsg = {
          ...this.systemMsg,
          ...data
        };
      });
    },
    /* 获取律师周边案源*/
    getLawyerAroundCase(){
      return orderCenterCaseSourceV2Clues({
        currentPage: 1,
        pageSize: 1,
        provinceCode: this.getUserInfo.workProvinceCode
      }).then(({ data = {} }) => {
        const {  records = [] } = data;
        const param = records[0];
        if(param){
          return  this.getSimulationItem({
            id: getRandomId("orderCenterCaseSourceV2Clues"),
            updateTime: param.refreshTime,
            userInfo: {
              name: "本省新增案源通知",
              img: require("@/pages/message-notice/imgs/<EMAIL>")
            },
            lastMsg: param.info,
            tagConfig: {
              text: "最新发布",
              style: addStyle({
                color: "#FFFFFF",
                backgroundColor: "#07C160",
                border: "none"
              }, "string")
            },
            other: {
              msgType: "orderCenterCaseSourceV2Clues"
            }
          });
        }
        return {};
      });
    },
    /* 获取律师投诉信息*/
    getLawyerComplaint(){
      return  lawyerComplaintWarnMsg().then(({ data }) => {
        console.log(data);
        if(data){
          return  this.getSimulationItem({
            id: getRandomId("getLawyerComplaint"),
            unread_num: data.num,
            updateTime: data.sendTime,
            userInfo: {
              name: "投诉处罚",
              img: require("@/pages/message-notice/imgs/<EMAIL>")
            },
            lastMsg: `<span style="color: #EB4738">[重要]</span>${this.getUserInfo.realName}律师您好,您在与${data.userName}的咨询中得到了投诉`,
            other: {
              msgType: "getLawyerComplaint",
              dataId: data.userComplaintId
            }
          });
        }
        return {};
      });
    },
    /* 获取模拟的FriendsList*/
    getSimulationItem({ id, updateTime, lastMsg, unread_num, userInfo = {}, tagConfig = {}, other = {} }){
      return{
        id,
        userInfo,
        lastMsg,
        lastMsgType: 1,
        unread_num,
        updateTime,
        top: 0,
        tagConfig,
        ...other,
        swipeDisabled: true
      };
    },
    handleTop(data){
      if(data.msgType) return;
      /* 是否置顶，0.取消置顶，1，置顶*/
      http.conversationTop({
        imSessionId: data.id,
        top: data.top === 1 ? 0 : 1
      }).then((e) => {
        if (e.code !== 0 && e.message) {
          uni.showToast({
            title: e.message,
            icon: "none"
          });
        }
      });
    },
    /* 获取好友列表*/
    getFriendsList() {
      if(this.isUpdate) return Promise.reject();
      this.isUpdate = true;
      return http.getSessionListFormat({
        type: 5
      }).then((data) => {
        this.reset();
        findsAllList = (data || []).map(item => {
          const json = parseJSON(item);
          /* 置顶的数据进入缓存*/
          if (json.top === 1) {
            topFindsList[json.id] = json.id;
          }
          this.imNoReadNum +=  json.unread_num;
          return {
            ...json,
            tagConfig: {
              text: this.getCaseTag(json)
            }
          };
        });
        this.nextPage();
        return this.findsList;
      }).finally(() => {
        this.isUpdate = false;
      });
    },
    /* 获取案源tag*/
    getCaseTag(data){
      const { type, ext = {} } = data || {};
      const { createFrom, serverWay } = ext;
      /* 想找是否是案源->咨询->问答*/
      const tagType = type === IM_CASE_SOURCE_TYPE.CASE_SOURCE ? serverWay : (type === IM_CASE_SOURCE_TYPE.CONSULTATION ? createFrom : 0);
      return (IM_CONVERSATION_TAG_MAP[type] && IM_CONVERSATION_TAG_MAP[type][tagType]) || "";
    },
    /* 获取好友 获得的好友有可能还没有分页出来*/
    getFriendsListByType(toC){
      let findIndex = 0;
      findIndex = this.findsList.findIndex(item => item.id === toC);
      /* 没有在渲染的findsList 就找全部的list*/
      if(findIndex === -1){
        findIndex = findsAllList.findIndex(item => item.id === toC);
        /* 如果找到了 就将数据push到渲染列表里面*/
        if(findIndex > -1 && findsAllList[findIndex]){
          const data = findsAllList.splice(findIndex, 1)[0];
          if (!isNull(data)){
            this.findsList.push(data);
            findIndex = this.findsList.length - 1;
          }
        }
      }
      return findIndex;
    },
    /* 更新好友列表*/
    updateFriendsList({ msgType, msg, toC }){
      const findIndex = this.getFriendsListByType(toC);
      /* 查看当前列表里面有无会话*/
      if (findIndex > -1) {
        console.log("命中列表好友开始更新========", findIndex, { msgType, msg, toC });
        const item = this.findsList[findIndex];
        item.lastMsgType = msgType;
        item.lastMsg = msg;
        /* 消息数量加1*/
        this.$set(item, "unread_num", item.unread_num + 1);
        this.imNoReadNum += 1;
        this.spliceFindsList(findIndex);
        return false;
      }
      console.log("新增好友");
      /* 到达这一步代表是新增好友 需要重新请求列表*/
      this.getFriendsList();
    },
    /* 插入好友*/
    spliceFindsList(index){
      const item = { ...(this.findsList[index] || {}) };
      let insertIndex = 0;
      if (isObjNull(item)) return false;
      /* 将最新消息的好友 追加到第一个*/
      this.findsList.splice(index, 1);
      /* 当前更新的会话 不是置顶会话 就排在置顶会话后 否则就到第一个*/
      if (!topFindsList[item.id] && !isObjNull(topFindsList)) {
        insertIndex = Object.keys(topFindsList).length;
      }
      this.findsList.splice(insertIndex, 0, item);
      return insertIndex;
    },
    /* 分页*/
    nextPage(start = 0, deleteCount = 15){
      const list = findsAllList.splice(start, deleteCount);
      if(!isArrNull(list)){
        this.findsList = [...this.findsList, ...list];
        console.log("分页", this.findsList);
        return;
      }
      console.log("分页结束", findsAllList.length, this.findsList.length);
    },
    handleClick(data) {
      /* 单独处理 系统消息*/
      if (data.msgType) {
        /* 案源消息*/
        if (data.msgType === "orderCenterCaseSourceV2Clues") {
          turnPages({
            path: "/pages/sub/im/caseSource/index"
          });
          return;
        }
        if (data.msgType === "getLawyerComplaint") { /* 律师投诉消息*/
          /* 投诉数量大于1 跳转系统消息  否则跳转投诉详情*/
          if (data.unread_num > 1) {
            turnToSystemMessagesPage({
              type: SYSTEM_MSG_TYPE.SYSTEM
            });
            return;
          }
          turnToDetailsOfTheComplaint({
            id: data.dataId
          });
        }
        return;
      }
      /* 跳转到聊天页面*/
      turnToImPage({
        id: data.id
      });
    },
    clearConverReadAll(){
      http.clearConverReadAll().then(() => {
        this.getFriendsList();
      });
    },
    /* 重置 */
    reset() {
      topFindsList = {};
      findsAllList = [];
      const resetData = this.$options.data();
      this.findsList = resetData.findsList;
      this.imNoReadNum = resetData.imNoReadNum;
      return resetData;
    }
  }
};
</script>

<style lang="scss" scoped>
.system-notifications{
  background: white;
  .system-item{
    .icon-container{
      margin: 0 auto;
    }
    .badge{
      position: absolute;
      top: 2px;
      right: 2px;
    }
  }
}
.messages-list{
  padding-top: 12px;
  box-shadow: inset 0px 0px 0px 0px #EEEEEE;
  .message-item{
    background: white;
    padding: 16px 14px 16px 16px;
    border-bottom: 0.5px solid #EEEEEE;
    position: relative;
    .avatar{
      .icon{
        width: 44px;
        height: 44px;
        border-radius: 22px 22px 22px 22px;
        overflow: hidden;
      }
      .box{
        min-width: 16px;
        min-height: 16px;
        background-color: white;
        border-radius: 15px;
        right: -4px;
        top: -4px;
      }
    }
    .content{
      padding-left: 12px;
      flex: 1;
      .top{
        .user-name{
          font-size: 16px;
          font-weight: 400;
          color: #333333;
        }
        .tag{
          margin-left: 8px;
          font-size: 10px;
          font-weight: 400;
          color: #3887F5;
          padding: 1px 3px;
          border-radius: 2px 2px 2px 2px;
          border: 0.5px solid #A0CFFB;
          background: #EBF3FE;
        }
        .date{
          font-size: 12px;
          font-weight: 400;
          color: #999999;
        }
      }
      .msg{
        padding-top: 4px;
        width: 289px;
        font-size: 14px;
        font-weight: 400;
        color: #666666;
      }
    }
    .badge{
      position: absolute;
      font-size: 8px;
      color: #A0CFFB;
      top: 2px;
      right: 2px;
    }
  }
}
.placeholder-tip{
  .placeholder-container{
    padding-top: 114px;
    text-align: center;
    .tip-img{
      width: 240px;
      height: 180px;
    }
    .title{
      padding-top: 16px;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
    .tip{
      padding-top: 16px;
      font-size: 14px;
      font-weight: 400;
      color: #666666;
    }
    .btn{
      width: 160px;
      line-height: 32px;
      text-align: center;
      background: #3887F5;
      border-radius: 68px 68px 68px 68px;
      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
      margin: 24px auto 0;
    }
  }
}
</style>
