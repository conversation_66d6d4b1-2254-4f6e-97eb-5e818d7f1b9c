<template>
  <div>
    <div class="tabs">
      <u-tabs
        :activeStyle="{
          color: '#222222',
          fontSize: '28rpx',
          fontWeight: 500,
        }"
        :inactiveStyle="{
          color: '#999999',
          fontSize: '28rpx',
        }"
        :list="tabList"
        itemStyle="padding-left: 46rpx; padding-right: 46rpx; height: 88rpx;"
        lineColor="#3887F5"
        lineWidth="14"
        @change="changeTab"
      />
    </div>
    <div
      v-for="(item) in rightsList"
      :key="item.goodsId"
      class="mg-tp-12"
    >
      <clue-rights-card
        :data="item"
        showDetail
      />
    </div>
    <u-safe-bottom />
  </div>
</template>

<script>
import ClueRightsCard from "@/pages/pay/clue/consume/components/ClueRightsCard.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import UTabs from "@/uview-ui/components/u-tabs/u-tabs.vue";
import { lawyerCluePackBuyHistoryByClueType } from "@/api/clue";
import { dataDetailList } from "@/api";

export default {
  name: "PayClueRights",
  components: { UTabs, USafeBottom, ClueRightsCard },
  data() {
    return {
      tabList: [],
      /** 权益列表 */
      rightsList: [],
    };
  },
  created() {
    this.getTabList();
  },
  methods: {
    /** 获取权益列表 */
    getRightsList(goodsType) {
      lawyerCluePackBuyHistoryByClueType({
        goodsType,
      }).then((res) => {
        this.rightsList = res.data;
      });
    },
    getTabList() {
      dataDetailList({
        groupCode: "LAWYER_CLUE_PACK_TYPE",
      }).then((res) => {
        this.tabList = res.data.map((item) => ({
          name: item.label,
          value: item.value,
        }));

        // 默认选中第一个
        this.getRightsList(this.tabList[0].value);
      });
    },
    changeTab(item) {
      console.log(item);
      this.getRightsList(item.value);
    },
  },
};
</script>

<style lang="scss" scoped>
.tabs {
  background: #ffffff;
  padding: 0 16px;
}
</style>
