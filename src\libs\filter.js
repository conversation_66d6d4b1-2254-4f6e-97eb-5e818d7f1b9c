import { isNull } from "@/libs/basics-tools";

export const amountFilter = (value) => {
  return isNull(value) ? 0 : (value / 100).toFixed(2);
};

/**
 * 金额保留一位小数，如果是整数则不显示小数点
 * @param value
 * @returns {number|number}
 */
export const amountFilterOne = (value) => {
  return isNull(value) ? 0 : Number((value / 100).toFixed(1));
};

/*
* 整数就不显示后面2位 小数就正常显示
*  */
export const amountFilterTwo = (value) => {
  return Number(amountFilter(value));
};
/**
 * 省略后面的小数
 * @param value
 * @returns {number|string}
 */
export const amountIsOmittedFilter = (value) => {
  return isNull(value) ? 0 : parseInt(String((value / 100)));
};

export const formatYearDate = (val) => {
  let valStr = String(val);
  let day = valStr.substring(valStr.length - 2);
  let month = valStr.substring(valStr.length - 2, valStr.length - 4);
  let year = valStr.substring(0, 4);
  return year + "年" + month + "月" + day + "日";
};
export const formatTime = (val) => {
  let valNum = Number(val);
  let timeStr = "";
  if((valNum / 3600) < 1){
    timeStr = valNum / 60 + "分钟";
  }else if((valNum / 3600) < 24){
    timeStr = valNum / 3600 + "小时";
  }else{
    timeStr = valNum / (3600 * 24) + "天";
  }
  return timeStr;
};

export const formatTimeTwo = (val) => {
  let valNum = Number(val || 0);
  let timeStr = "";
  if(valNum < 60){
    return  valNum + "秒";
  } else if((valNum / 3600) < 1){
    timeStr = (String(valNum / 60 )).split(".")[0] + "分钟";
  }else if((valNum / 3600) < 24){
    timeStr = (String(valNum / 3600)).split(".")[0] + "小时";
  }else{
    timeStr = (String(valNum / (3600 * 24))).split(".")[0]  + "天";
  }
  return timeStr;
};
/**
 *
 * @param val
 * @returns {string|{unit: string, time: string}}
 */
export const formatTimeTwoUnit = (val) => {
  let valNum = Number(val || 0);
  let timeStr = "";
  let unit = "";
  if(valNum < 60){
    unit = "秒";
    timeStr = valNum;
    // return  valNum + '秒'
  } else if((valNum / 3600) < 1){
    unit = "分钟";
    timeStr = (String(valNum / 60 )).split(".")[0];
  }else if((valNum / 3600) < 24){
    unit = "小时";
    timeStr = (String(valNum / 3600)).split(".")[0];
  }else{
    unit = "天";
    timeStr = (String(valNum / (3600 * 24))).split(".")[0];
  }
  return {
    time: timeStr,
    unit
  };
};
