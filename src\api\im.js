import { requestCommon } from "@/libs/axios.js";

/** 消息中心投诉是否显示 */
export const lawyerComplaintWarnMsg = (data) =>
  requestCommon.post("/paralegal/lawyer/complaint/warnMsg", data);

/** 查询该律师历史的购买记录明细V2 */
export const orderCenterCaseSourceV2Clues = (data) =>
  requestCommon.post("/paralegal/orderCenter/caseSourceV2Clues", data);

/** 用户未读消息数查询 */
export const coreMessageNoRead = (data) =>
  requestCommon.post("/core/message/noRead", data);

/* 获取律师已开启的一对一付费服务列表*/
export const serviceManegeGetLawyerAllServiceList = (data) => requestCommon.post("/info/serviceManege/getLawyerAllServiceList", data);

/** 用户消息列表查询 */
export const coreMessageList = (data) =>
  requestCommon.post("/core/message/list", data);

/** 用户所有消息已读 */
export const coreMessageAllRead = (data) =>
  requestCommon.post("/core/message/all/read", data);

/** 投诉详情 */
export const lawyerComplaintDetail = (data) =>
  requestCommon.post(`/paralegal/lawyer/complaint/detail/${data.userComplaintId}`, data);

/** 快捷回复查询 */
export const quickReplyGet = (data) =>
  requestCommon.post("/info/quickReply/get", data);

/** 快捷回复新增 */
export const quickReplyInsert = (data) =>
  requestCommon.post("/info/quickReply/insert", data);

/** 快捷回复修改 */
export const quickReplyUpdate = (data) =>
  requestCommon.post("/info/quickReply/update", data);

/* 律师是否同意暴露电话*/
export const lawyerOfferPhone = (params) =>
  requestCommon.post(`/paralegal/caseSourceServerV2/lawyerOfferPhone/${params.caseSourceServerV2Id}/${params.type}`);

/* 新获取小号或真实号码*/
export const getNewServerCall = (params) => requestCommon.post(`/paralegal/caseSourceServerV2/getNewServerCall/${params.caseSourceServerV2Id}`);

/* 获取服务信息*/
export const getServerInfo = (caseSourceServerV2Id) => requestCommon.post(`/paralegal/caseSourceServerV2/getServerInfo/${caseSourceServerV2Id}`);


/* 获取法律意见书*/
export const lawWrittenOpinionGetLawWrittenOpinion = (params) => requestCommon.post("/paralegal/lawWrittenOpinion/getLawWrittenOpinion", params);
/* 发送法律意见书*/
export const lawWrittenOpinionSend = (params) => requestCommon.post("/paralegal/lawWrittenOpinion/send", params);

/* 保存律师案源评分（新）*/
export const caseSourceServerV2SaveScoreNew = (params) => requestCommon.post("/paralegal/caseSourceServerV2/saveScore/new", params);


/* 律师完成服务是否显示提醒评价push*/
export const lawyerCaseSourceDiscussPush = (params) => requestCommon.post(`/paralegal/lawyerCaseSource/discussPush/${params.caseSourceServerV2Id}`);
/* 发送提醒用户进行评价的push*/
export const lawyerCaseSourceSendDiscussPush = (params) => requestCommon.post(`/paralegal/lawyerCaseSource/sendDiscussPush/${params.caseSourceServerV2Id}`);

/* im查询创建和限制*/
export const imCreate = (params) => requestCommon.post("/core/im/create", params);


/* 交换电话或者微信*/
export const lawyerExchangeInfo = (params) => requestCommon.post("/lawyer/lawyer/exchangeInfo", params);


/* 同意或者拒绝交换*/
export const lawyerAgreeExchangeInfo = (params) => requestCommon.post("/lawyer/lawyer/agreeExchangeInfo", params);

/* 查询im聊天创建*/
export const imGetImInfo = (params) => requestCommon.post("/core/im/getImInfo", params);

/* 查询im聊天创建次数*/
export const imGetImLimit = (params) => requestCommon.post("/core/im/getImLimit", params);
