<template>
  <theme-layout-card :theme="theme">
    <p class="tag">
      用户问题
    </p>
    <p>{{ customExtsinExt.question }}</p>
  </theme-layout-card>
</template>

<script>

import ThemeLayoutCard from "@/pages/sub/im/components/Cards/ThemeLayoutCard/index.vue";
import cardProps, { customComputed } from "@/pages/sub/im/mixins/card-props.js";

export default {
  name: "UserQuestionsCard",
  components: { ThemeLayoutCard },
  mixins: [cardProps, customComputed]
};
</script>

<style scoped lang="scss">
.tag{
  background: linear-gradient(133deg, #769CFF 0%, #4377FF 100%);
  border-radius: 4px 4px 4px 4px;
  font-size: 12px;
  font-weight: 400;
  display: inline-block;
  color: #FFFFFF;
  padding: 2px 4px;
  margin-bottom: 4px;
}
</style>
