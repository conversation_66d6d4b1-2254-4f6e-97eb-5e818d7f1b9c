<template>
  <login-layout>
    <div class="px-[12px]">
      <div class="pt-[12px]">
        <collaboration-basics :data="data" />
      </div>
      <div class="pt-[12px]">
        <!-- 自己协作item     -->
        <collaborate-my-item
          v-if="isMyCollaboration"
          :data="data"
          @copyWeiChat="copyWeiChat"
        />
        <!--  他人协作item    -->
        <collaborate-with-others-item
          v-else
          :data="data"
        />
      </div>
      <div class="pt-[12px]">
        <reminder-for-collaboration-details />
      </div>
      <!-- 他人发布的协作按钮   -->
      <app-bottom v-if="!isMyCollaboration">
        <div class="px-[16px] py-[8px]">
          <!--  他人协作并且自己未接单展示      -->
          <div
            v-if="isPendingOrders && !isOwnJd"
            class="flex items-center justify-center font-bold text-[16px] text-[#FFFFFF] h-[44px] bg-[#3887F5] rounded-[22px]"
            @click="handleClick('grabTheOrderNow')"
          >
            立即抢单
          </div>
          <!--  他人协作并且自己抢了单      -->
          <div
            v-if="isOwnJd"
            class="flex justify-between"
          >
            <div
              class="flex items-center flex-1 justify-center font-bold text-[16px] text-[#FFFFFF] h-[44px] bg-[#3887F5] rounded-[22px]"
              @click="handleClick('contactALawyer')"
            >
              <img
                class="block w-[24px] h-[24px] mr-[4px]"
                alt=""
                src="@/pages/sub/collaboration-details/imgs/Component1.png"
              >
              联系律师
            </div>
            <div
              class="flex items-center flex-1 justify-center font-bold text-[16px] text-[#FFFFFF] h-[44px] bg-[#22BF7E] rounded-[22px] ml-[13px]"
              @click="handleClick('handleWechatBind')"
            >
              <img
                class="block w-[24px] h-[24px] mr-[4px]"
                alt=""
                src="@/pages/sub/collaboration-details/imgs/btn_icon_weixin.png"
              >
              获取微信
            </div>
          </div>
        </div>
      </app-bottom>
      <!-- 自己发布的协作 按钮   -->
      <app-bottom
        v-if="isMyCollaboration && isPendingOrders && isArrNull(jdLawyerList)"
      >
        <div class="px-[16px] py-[8px] flex justify-center items-center">
          <!--   自己的协作并且未接单并且没有律师抢单      -->
          <div
            class="flex items-center justify-center w-[100px] h-[44px] rounded-[22px] border-[1px] border-solid border-[#CCCCCC] font-bold text-[16px] text-[#666666] mr-[12px] shrink-0"
            @click="handleClick('turnOffPublishingCollaboration')"
          >
            关闭
          </div>
          <app-button-share class="flex-1">
            <div
              class="flex items-center justify-center h-[46px] bg-[#22BF7E] rounded-[68px] text-[16px] text-[#FFFFFF]"
            >
              <i class="iconfont icon-a-fenxiang1 !text-[24px] mr-[4px]" />
              <div>
                分享到社群
              </div>
            </div>
          </app-button-share>
        </div>
      </app-bottom>
    </div>
    <get-lawyer-we-chat-popup
      v-model="getLawyerWeChatPopupStatus"
      :wechatPopupData="wechatPopupData"
    />
  </login-layout>
</template>

<script>
import CollaborationBasics from "@/pages/sub/collaboration-details/components/collaborationBasics.vue";
import ReminderForCollaborationDetails from "@/pages/sub/collaboration-details/components/reminderForCollaborationDetails.vue";
import AppBottom from "@/components/AppComponents/AppBottom/index.vue";
import {
  lawyerCollaborationClose,
  lawyerCollaborationDetailById,
  lawyerCollaborationGetPhoneByJdRecordId,
  lawyerCollaborationGrabOrder,
} from "@/api/collaboration";
import { COOPERATION_RECEIVE_STATUS, COOPERATION_TYPE_OBJ, SHARE_TYPE } from "@/enum";
import CollaborateWithOthersItem from "@/pages/sub/collaboration-details/components/collaborateWithOthersItem.vue";
import CollaborateMyItem from "@/pages/sub/collaboration-details/components/collaborateMyItem.vue";
import { isArrNull, isNull } from "@/libs/basics-tools";
import LoginLayout from "@/components/login/LoginLayout.vue";
import {
  isNotLogin,
  requestShareAppMessage,
  whetherToLogIn,
} from "@/libs/tools";
import {
  turnToCaseSourceSquare,
  turnToLawyerAuthResultPage,
} from "@/libs/turnPages";
import { SHARE_DICTIONARY } from "@/libs/config";
import GetLawyerWeChatPopup from "@/pages/sub/collaboration-details/components/LawyerWeChatPopup.vue";
import AppButtonShare from "@/components/AppComponents/AppButtonShare/index.vue";
import { contentShare } from "@/api/user";

export default {
  name: "CollaborationDetails",
  components: {
    LoginLayout,
    CollaborateMyItem,
    CollaborateWithOthersItem,
    AppBottom,
    ReminderForCollaborationDetails,
    CollaborationBasics,
    GetLawyerWeChatPopup,
    AppButtonShare,
  },
  data() {
    return {
      // 获取微信弹窗状态
      getLawyerWeChatPopupStatus: false,
      data: {
        statusLabel: { label: "-", color: "#999999" },
        jdLawyerList: [],
        typeLabel: "-",
      },
      query: {},
      /** 微信号弹窗数据 */
      wechatPopupData: {
        lawyerName: "",
        lawyerWeChatId: "",
      },
    };
  },
  onLoad(query) {
    this.query = query;
    this.getDetail();
  },
  async onShareAppMessage() {
    this.$store.commit("share/SET_SHARE_CALLBACK", () => {
      contentShare({
        id: this.data.id,
        type: SHARE_TYPE.collaboration,
      });
    });

    return await requestShareAppMessage(SHARE_DICTIONARY.COOPERATIONDETAIL);
  },
  computed: {
    /* 是否是待接单*/
    isPendingOrders() {
      return this.data.status === 1;
    },
    /* 是否是自己接单*/
    isOwnJd() {
      return this.data.isOwnJd;
    },
    /* 是不是自己的协作*/
    isMyCollaboration() {
      return this.data.isOwn;
    },
    /* 抢单律师*/
    jdLawyerList() {
      return this.data.jdLawyerList || [];
    },
  },
  methods: {
    isArrNull,
    getDetail() {
      if (isNull(this.query.id)) return;
      lawyerCollaborationDetailById({ id: this.query.id })
        .then((res) => {
          const data = res.data || {};
          this.data = {
            ...data,
            statusLabel: COOPERATION_RECEIVE_STATUS[data.status] || {
              label: "-",
              color: "#999999",
            },
            jdLawyerList: data.jdLawyerList || [],
            typeLabel: this.collaborationStatus(data),
          };
        })
        .catch(() => {
          /* 已关闭会走报错 刷新下list */
          setTimeout(() => {
            turnToCaseSourceSquare({
              type: "flushed",
            });
          }, 1000);
        });
    },
    /* 协作状态 文案*/
    collaborationStatus(data) {
      return (
        {
          [COOPERATION_TYPE_OBJ.CASE_COLLABORATION.value]:
            COOPERATION_TYPE_OBJ.CASE_COLLABORATION.label,
          [COOPERATION_TYPE_OBJ.CHECK_FILES_IN_DIFFERENT_PLACES.value]:
            COOPERATION_TYPE_OBJ.CHECK_FILES_IN_DIFFERENT_PLACES.label,
        }[data.type] || "-"
      );
    },
    /* 立即抢单*/
    grabTheOrderNow() {
      if (isNull(this.query.id)) return;
      lawyerCollaborationGrabOrder({
        id: this.query.id,
      }).then(() => {
        uni.showToast({
          title: "抢单成功",
          icon: "none",
          duration: 2000,
        });
        this.getDetail();
      });
    },
    /* 联系律师*/
    contactALawyer() {
      if (!this.isOwnJd) return;
      lawyerCollaborationGetPhoneByJdRecordId({
        jdRecordId: this.data.jdRecordId,
      }).then((res) => {
        const data = res.data;
        uni.makePhoneCall({
          phoneNumber: data.lawyerPhone,
        });
      });
    },
    /* 关闭自己发布的协作*/
    turnOffPublishingCollaboration() {
      if (isNull(this.query.id)) return;
      lawyerCollaborationClose({
        id: this.query.id,
      }).then(() => {
        uni.showToast({
          title: "关闭成功",
          icon: "success",
          duration: 2000,
        });
        this.getDetail();
      });
    },
    getWechatId(id){
      lawyerCollaborationGetPhoneByJdRecordId({
        jdRecordId: id,
      }).then(({ data }) => {
        if(!data.weChatId) {
          uni.showToast({
            title: "对方律师未关联微信号，无法获取，请电话联系",
            icon: "none",
            duration: 2000,
          });
          return;
        }

        this.wechatPopupData.lawyerWeChatId = data.weChatId;
        this.getLawyerWeChatPopupStatus = true;
      });
    },
    handleWechatBind(){
      this.wechatPopupData = {
        lawyerName: this.data.fbLawyerName,
      };

      this.getWechatId(this.data.jdRecordId);
    },
    copyWeiChat(data){
      this.wechatPopupData = {
        lawyerName: data.jdLawyerName,
      };

      this.getWechatId(data.id);
    },
    /* 点击事件派发 判断登录认证状态 */
    handleClick(fnKey) {
      const isLogin = !isNotLogin();
      if (this[fnKey]) {
        whetherToLogIn(() => {
          /* 如果之前没有登录 刷新一下页面数据 */
          if (!isLogin) {
            this.getDetail();
          }
          turnToLawyerAuthResultPage({
            callback: () => {
              if (isLogin) {
                this[fnKey]();
              }
            },
          });
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
