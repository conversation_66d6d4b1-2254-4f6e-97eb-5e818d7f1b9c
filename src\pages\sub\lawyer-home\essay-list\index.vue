<template>
  <div class="essay-list">
    <div class="sticky-fixed">
      <div class="sticky-header">
        <div class="position-relative">
          <div class="tabs-mark-white" />
          <u-tabs
            :activeStyle="activeStyle"
            :current="tabCurrent"
            :inactiveStyle="inactiveStyle"
            :lineWidth="14"
            :list="tabList"
            itemStyle="padding-left: 19px; padding-right: 19px; height: 44px;"
            lineColor="#3887F5"
            @change="changeTab"
          />
        </div>
      </div>
    </div>
    <div
      v-for="i in list"
      :key="i.id"
      class="essay-item-c"
    >
      <essay-item :data="i" />
    </div>
    <div
      v-if="requestStatus && isArrNull(list)"
      class="placeholder flex flex-column flex-align-center"
    >
      <img
        alt=""
        class="placeholder-icon"
        src="../img/<EMAIL>"
      >
      暂无相关文章
    </div>
  </div>
</template>

<script>
import EssayItem from "@/components/essay-item/index.vue";
import UTabs from "@/uview-ui/components/u-tabs/u-tabs.vue";
import { dataDictionary } from "@/api";
import { articleV2LawyerPage } from "@/api/lawyer.js";
import { isArrNull } from "@/libs/basics-tools.js";

export default {
  name: "EssayList",
  components: { UTabs, EssayItem },
  data() {
    return {
      requestStatus: false,
      activeStyle: {
        color: "#333333",
        fontSize: "15px",
        fontWeight: 500,
      },
      inactiveStyle: {
        color: "#999999",
        fontSize: "14px",
      },
      pageTotal: 0,
      typeValue: null,
      /** tab数据 */
      tabList: [],
      tabCurrent: 0,
      list: [],
      lawyerId: "",
      pageParams: {
        /** 页码 */
        currentPage: 1,
        /** 每页条数 */
        pageSize: 20,
      },
    };
  },
  onLoad(query) {
    this.typeValue = query.typeValue || null;

    this.lawyerId = query.lawyerId;
  },
  onHide() {
    clearTimeout(this.timer);
  },
  mounted() {
    dataDictionary({ groupCode: "LAWYER_SPECIALITY" }).then(({ data = [] }) => {
      this.tabList = data.map((item) => ({
        name: item.label,
        value: item.value,
      }));

      this.tabList.unshift({
        name: "全部",
        value: null,
      });
    });
    this.getList();
  },
  onReachBottom() {
    this.scrollToLower();
  },
  methods: {
    isArrNull,
    /** 更改标签后重新获取数据 */
    changeTab(item) {
      this.pageParams = this.$options.data().pageParams;
      this.typeValue = item.value;
      this.list = [];
      this.getList();
    },

    scrollToLower() {
      if (
        this.pageParams.currentPage * this.pageParams.pageSize >=
        this.pageTotal
      )
        return;

      this.pageParams.currentPage++;
      this.getList();
    },
    getList() {
      this.requestStatus = false;
      articleV2LawyerPage({
        typeValue: this.typeValue === null ? "" : Number(this.typeValue),
        lawyerId: this.lawyerId,
        ...this.pageParams,
      })
        .then(({ data = {} }) => {
          const { records = [] } = data;
          this.list = [...this.list, ...records];

          this.tabCurrent = this.tabList.findIndex(
            (item) => item.value === this.typeValue
          );

          this.pageTotal = data.total || 0;
        })
        .finally(() => {
          this.requestStatus = true;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.essay-item-c {
  padding: 12px 12px 0;
}

.essay-list {
  padding-top: 40px;
}

.sticky-fixed {
  position: fixed;
  top: 0;
  background: #ffffff;
  right: 0;
  left: 0;
  z-index: 999;
}

.sticky-header {
  // #ifdef MP-BAIDU
  position: relative;
  z-index: 999999;
  // #endif
}

.placeholder {
  padding-top: 112px;
  text-align: center;

  .placeholder-icon {
    width: 240px;
    height: 180px;
  }

  font-size: 14px;
  font-weight: 400;
  color: #999999;
}

.popup-wr,
.popup-wr-btm {
  position: relative;

  .img-main {
    width: 100%;
  }

  .img-btn {
    position: absolute;
    width: 287px;
    height: 54px;
    top: 154px;
    left: 20px;
  }
}

.popup-wr-btm {
  .img-main {
    height: 262px;
  }

  .img-btn {
    width: 311px;
    top: 164px;
    left: 32px;
  }
}
</style>
