<template>
  <div class="details-of-the-complaint">
    <div class="notice-bar flex flex-align-center">
      <i class="iconfont icon-xianxingtubiao-2" />
      <p class="desc flex-1">
        添加容服微信，遇到问题及时解决
      </p>
      <div
        class="to-add flex flex-align-center"
        @click="turnToServiceCenterPage"
      >
        去添加
        <i class="iconfont icon-erjiyoujiantou" />
      </div>
    </div>
    <div class="text-container">
      <div class="flex text-item">
        <p class="label">
          投诉用户：
        </p>
        <p class="desc">
          {{ detail.userName }}
        </p>
      </div>
      <div class="flex text-item">
        <p class="label">
          订单金额：
        </p>
        <p class="desc">
          ¥{{ amountFilter(detail.amount) }}
        </p>
      </div>
      <div class="flex text-item">
        <p class="label">
          投诉内容：
        </p>
        <p class="desc">
          {{ detail.detail }}
        </p>
      </div>
    </div>
    <div
      class="btn"
      @click="turnToImPage({id:detail.imSessionId})"
    >
      查看会话
    </div>
  </div>
</template>

<script>


import { lawyerComplaintDetail } from "@/api/im.js";
import { amountFilter } from "@/libs/filter.js";
import { turnToImPage, turnToServiceCenterPage } from "@/libs/turnPages.js";

export default {
  name: "DetailsOfTheComplaint",
  methods: { turnToImPage, turnToServiceCenterPage, amountFilter },
  data() {
    return {
      detail: {}
    };
  },
  onLoad({ id }){
    lawyerComplaintDetail({
      userComplaintId: id
    }).then(({ data = {} }) => {
      this.detail = data;
    });
  }
};
</script>

<style scoped lang="scss">
.details-of-the-complaint{
  min-height: 100vh;
  background: white;
  .notice-bar{
    height: 40px;
    background: #FAF2E8;
    padding: 0 16px;
    .iconfont{
      font-size: 16px;
      color: #F78C3E;
    }
    .desc{
      font-size: 14px;
      font-weight: 400;
      color: #F78C3E;
      padding-left: 8px;
    }
    .to-add{
      font-size: 14px;
      font-weight: 400;
      color: #F78C3E;
    }
  }
  .text-container{
    .text-item{
      padding: 12px 16px;
    }
    .label{
      min-width: 70px;
      font-size: 14px;
      font-weight: 400;
      color: #666666;
    }
    .desc{
      padding-left: 16px;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
    }
  }
  .btn{
    position: fixed;
    bottom: constant(safe-area-inset-bottom, 0);
    bottom: env(safe-area-inset-bottom, 0);
    width: 343px;
    text-align: center;
    line-height: 44px;
    background: #3887F5;
    border-radius: 68px 68px 68px 68px;
    font-size: 16px;
    font-weight: 500;
    color: #FFFFFF;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
