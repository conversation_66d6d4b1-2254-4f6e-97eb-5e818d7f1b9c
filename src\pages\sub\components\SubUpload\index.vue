<template>
  <div
    :class="[{'opacity-30': disabled}]"
    @click.stop="handleSelect"
  >
    <slot />
  </div>
</template>

<script>
import { chooseFile } from "@/uview-ui/components/u-upload/utils";
import { fileUpload } from "@/libs/fileUpload";

export default {
  name: "SubUpload",
  props: {
    /* 选择文件类型 图片选择还是视频 还是文件 文件只有微信*/
    type: {
      type: String,
      default: "image",
      validator(value) {
        return ["image", "video", "file", "media"].includes(value);
      }
    },
    extension: {
      type: Array,
      default: () => []
    },
    /* 文件大小限制 KB*/
    fileSizeLimit: {
      type: Number,
      default: 10
    },
    /* 上传地址*/
    url: {
      type: String,
      default: process.env.VUE_APP_ENV_BASE_URL + "/core/upload/image"
    },
    //   禁止上传
    disabled: {
      type: Boolean,
      default: false
    },
    //   wx.chooseMedia使用下列props
    // 文件类型
    // image	只能拍摄图片或从相册选择图片
    // video	只能拍摄视频或从相册选择视频
    // mix	可同时选择图片和视频
    mediaType: {
      type: Array,
      default: () => ["image", "video"]
    },
    //   图片和视频选择的来源
    capture: {
      type: Array,
      // album	从相册选择camera	使用相机拍摄
      default: () => ["album", "camera"]
    },
    // 拍摄视频最长拍摄时间，单位秒。时间范围为 3s 至 60s 之间。不限制相册。
    maxDuration: {
      type: Number,
      default: 10
    },
    // 是否压缩所选文件，基础库2.25.0前仅对 mediaType 为 image 时有效，2.25.0及以后对全量 mediaType 有效
    // ['original', 'compressed']
    sizeType: {
      type: Array,
      default: () => ["original", "compressed"]
    },
    // 仅在 sourceType 为 camera 时生效，使用前置或后置摄像头
    // back	使用后置摄像头
    //  front	使用前置摄像头
    camera: {
      type: String,
      default: "back"
    },
    // 是否需要上传loading
    isLoading: {
      type: Boolean,
      default: false
    }

  },
  data() {
    return {
      // 上传状态 方式多次点击
      uploadStatus: false
    };
  },

  methods: {
    /* 选择文件后的成功回调*/
    selectSuccess(e){
      const [file] = e;
      console.log(e);
      if(file.size > this.fileSizeLimit * 1024){
        // 大于等于1M 提示就按照M来提示 否则就是kb
        const fileSizeLimitToast = this.fileSizeLimit > 1024 ? `文件大小超过${(this.fileSizeLimit / 1024).toFixed(0)}M` : `文件大小超过${this.fileSizeLimit}KB`;
        uni.showToast({
          title: fileSizeLimitToast,
          icon: "none"
        });
        this.uploadStatus = false;
        return;
      }
      this.fileUpload(file);
    },
    // 上传文件
    fileUpload(file){
      if(this.isLoading){
        uni.showLoading({
          title: "上传中...."
        });
      }
      return   fileUpload({
        file: file,
        url: this.url
      }).then(({ data = "" }) => {
        this.$emit("uploadSuccess", data);
      }).catch(() => {
        this.$emit("uploadError");
      }).finally(() => {
        this.uploadStatus = false;
        if(this.isLoading) uni.hideLoading();
      });
    },
    /* 选择文件后的失败回调*/
    selectError(e){
      console.log(e);
      this.uploadStatus = false;
      uni.showToast({
        title: "选择失败",
        icon: "none"
      });
    },
    /* 选择图片*/
    selectImage(){
      // #ifdef MP-WEIXIN
      this.selectMedia({
        mediaType: ["image"]
      });
      // #endif

      // #ifndef MP-WEIXIN
      console.log("this.type");
      chooseFile({
        accept: "image",
        multiple: false
      }).then(this.selectSuccess).catch(this.selectError);
      // #endif
    },
    /* 选择视频*/
    selectVideo(){
      chooseFile({
        accept: "video",
        multiple: false
      }).then(this.selectSuccess).catch(this.selectError);
    },
    /* 选择文件*/
    selectFile(){
      chooseFile({
        accept: "file",
        multiple: false,
        extension: this.extension
      }).then(this.selectSuccess).catch(this.selectError);
    },
    selectMedia(data){
      chooseFile({
        accept: "media",
        multiple: false,
        ...data
      }).then(this.selectSuccess).catch(this.selectError);
    },
    handleSelect(){
      if (this.disabled) {
        return;
      }
      if (this.uploadStatus) {
        uni.showToast({
          title: "文件正在上传中...",
          icon: "none"
        });
        return;
      }
      this.$emit("handleSelect");
      this.uploadStatus = true;
      switch (this.type) {
      case "image":
        this.selectImage();
        break;
      case "video":
        this.selectVideo();
        break;
      case "file":
        this.selectFile();
        break;
      case "media":
        this.selectMedia({
          capture: this.capture,
          maxDuration: this.maxDuration,
          sizeType: this.sizeType,
          camera: this.camera,
          mediaType: this.mediaType
        });
        break;
      }
    }
  }
};
</script>

<style scoped lang="scss">

</style>