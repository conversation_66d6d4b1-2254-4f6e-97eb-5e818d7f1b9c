<template>
  <div class="case-pay">
    <div class="price-content">
      <div class="title">
        竞价费用
      </div>
      <div>
        <span class="unit">¥</span>
        <span class="price">{{ priceNumber(getTotal) }}</span>
      </div>
    </div>
    <div class="content-wrapper">
      <div
        class="line"
        @click="couponsClick"
      >
        <span>现金券</span>
        <span
          v-if="couponList.length"
          class="coupons"
        >-¥{{ priceNumber(couponInfo.spec) }} <span class="iconfont icon-erjiyoujiantou" /></span>
        <span
          v-else
          class="coupons no-data"
        >暂无可用优惠券</span>
      </div>
    </div>
    <div class="content-wrapper">
      <div class="line">
        <span class="flex flex-align-center"><img
          class="icon"
          src="@/pages/caseSource/img/wallet.png"
          alt=""
        >充值余额(¥{{ priceNumber(balanceV2Response.rechargeAccountBalance) }})</span>
        <span
          class="right"
          @click="handleSelect(1)"
        >
          <img
            v-if="selectValue===1"
            class="icon"
            src="@/pages/caseSource/img/payselect.png"
            alt=""
          >
          <img
            v-else
            class="icon"
            src="@/pages/caseSource/img/zx_nocheck.png"
            alt=""
          >
        </span>
      </div>
      <div class="line">
        <span class="flex flex-align-center"><img
          class="icon"
          src="@/pages/caseSource/img/gift.png"
          alt=""
        >
          赠送余额(¥{{ priceNumber(balanceV2Response.giftAccountBalance) }})
        </span>
        <span
          class="right"
          @click="handleSelect(2)"
        >
          <img
            v-if="selectValue===2"
            class="icon"
            src="@/pages/caseSource/img/payselect.png"
            alt=""
          >
          <img
            v-else
            class="icon"
            src="@/pages/caseSource/img/zx_nocheck.png"
            alt=""
          >
        </span>
      </div>
        <div class="line">
          <img v-if="selectValue===2" mode="widthFix" class="img-tip" src="@/pages/caseSource/img/<EMAIL>" alt="">
        </div>
    </div>
    <div
      class="recharge"
      @click="()=>turnToRechargePage({active:'2'})"
    >
      去充值 <span class="iconfont icon-erjiyoujiantou" />
    </div>
    <div class="fixed-wrapper">
      <div
        class="btn"
        @click="sure"
      >
        确认锁定 ¥{{ priceNumber(payPrice) }}
      </div>
      <div
        class="xy-line flex flex-align-center"
        @click="isCheck=!isCheck"
      >
        <img
          v-if="!isCheck"
          class="check"
          src="@/pages/caseSource/img/zx_nocheck.png"
          alt=""
        >
        <img
          v-else
          class="check"
          src="@/pages/caseSource/img/xiyichecked.png"
          alt=""
        >
        勾选即代表同意
        <span
          class="color-text"
          @click="clickXy1"
        >《服务协议》</span>
        <span
          class="color-text"
          @click="clickXy2"
        >《法临币充值协议》</span>
      </div>
      <u-safe-bottom />
    </div>
    <coupon-popup
      :show.sync="couponShow"
      :list="couponList"
      :couponInfoProp="couponInfo"
      @selectedCoupon="selectedCoupon"
    />

    <app-popup
      mode="center"
      round="16"
      :show="moneyShow"
      :safeAreaInsetBottom="false"
      bgColor="transparent"
    >
      <div class="money-wrapper">
        <div>
          <img
            class="img"
            src="@/pages/caseSource/img/<EMAIL>"
            alt=""
          >
        </div>
        <div class="title">
          {{ way ? '当前余额不足，无法独享抢单哦~' : '当前余额不足，无法抢单哦~' }}
        </div>
        <div
          class="btn"
          @click="turnToRechargePage({active:'2'})"
        >
          去充值
        </div>
      </div>
      <div class="close-icon">
        <span
          class="iconfont icon-cuowu"
          @click="moneyShow=false"
        />
      </div>
    </app-popup>
    <u-safe-bottom />
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { listAndBalance, orderCenterSourceDetail } from "@/api";
import {
  turnToCaseSourceDetails,
  turnToCaseSourceSquare,
  turnToImPage,
  turnToRechargePage,
  turnToWebViewPage
} from "@/libs/turnPages";
import { priceNumber } from "@/libs/tool";
import CouponPopup from "@/components/couponPopup/index.vue";
import { caseSourceGrabOrder, pageCoupon } from "@/api/order";
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { WebAddress } from "@/enum";

export default {
  components: { AppPopup, USafeBottom, CouponPopup },
  data() {
    return {
      detail: {},
      balanceV2Response: {},
      selectValue: null,
      isCheck: false,
      couponList: [],
      couponShow: false,
      errorShow: false,
      moneyShow: false,
      couponInfo: {
        id: null,
        spec: 0,
      },
      way: null, // 1 独享
    };
  },
  onLoad(query) {
    this.way = query.way === "1" ? 1 : null;
    this.getDetail(query.id);

    pageCoupon({ status: 0, type: 3 }).then(({ data = {} }) => {
      this.couponList = data.records || [];
      if(this.couponList[0]) {
        this.couponInfo = this.couponList[0]
      }
    });
  },
  onHide() {
    this.couponShow = false;
    this.errorShow = false;
    this.moneyShow = false;
  },
  computed: {
    getTotal() {
      return this.way ? this.detail.fixedAmount : this.detail.biddingNum;
    },
    payPrice() {
      const res = this.getTotal - this.couponInfo.spec;
      return res > 0 ? res : 0;
    }
  },
  methods: {
    turnToCaseSourceSquare,
    priceNumber,
    turnToRechargePage,
    // 获取详情
    getDetail(id) {
      orderCenterSourceDetail(id).then(res => {
        this.detail = res.data;
        listAndBalance().then(res => {
          this.balanceV2Response = res.data.balanceV2Response;
          // 默认选中
          if(this.balanceV2Response.rechargeAccountBalance  >= this.payPrice) {
            this.handleSelect(1);
          } else {
            this.handleSelect(2);
          }
        });
      });
    },
    // 选择账户
    handleSelect(value) {
      if(value === 1 && this.balanceV2Response.rechargeAccountBalance  < this.payPrice ){
        this.moneyShow = true;
        return;
      }
      if(value === 2 && this.balanceV2Response.giftAccountBalance < this.payPrice){
        this.moneyShow = true;
        return;
      }
      this.selectValue = value;
    },
    // 确认支付
    sure() {
      if(!this.isCheck){
        this.$u.toast("请勾选服务协议");
        return;
      }
      if(this.selectValue === null){
        this.moneyShow = true;
        return;
      }
      caseSourceGrabOrder({
        couponLawyerRecordId: this.couponInfo.id,
        walletAccountType: this.selectValue,
        caseSourceV2Id: this.detail.id,
        way: this.way ? 1 : null,
      })
        .then(({ data = {} }) => {
          turnToImPage({id: data.imSessionId})
        })
        .catch(() => {
          // this.errorShow = true;
        });

      this.$u.toast("支付成功");
    },
    // 选中优惠券
    selectedCoupon(item) {
      this.couponInfo = item;
    },
    // 优惠券
    couponsClick() {
      if(this.couponList.length > 0) {
        this.couponShow = true;
      }
    },
    // 服务协议
    clickXy1 () {
      turnToWebViewPage({ src: WebAddress.lawyer_service_convention });
    },
    // 法币充值协议
    clickXy2 () {
      turnToWebViewPage({ src: WebAddress.lawyer_legal_currency_recharge_protocol });
    }
  },
};
</script>

<style lang="scss" scoped>
.case-pay{
  padding: 0 24px;
  .price-content{
    padding: 51px 0 56px;
    text-align: center;
    font-weight: 500;
    color: #333333;
    .title{
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      margin-bottom: 8px;
    }
    .unit{
      font-size: 16px;
    }
    .price{
      font-size: 40px;
    }
  }
  .content-wrapper{
    padding: 12px 16px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    margin-bottom: 12px;
    .line{
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      .coupons{
        font-weight: 500;
        color: #EB4738;
        .iconfont{
          color: #CFCFCF;
          margin-left: 4px;
        }
        &.no-data{
          font-weight: normal;
          color: #ccc;
        }
      }
      .icon{
        width: 18px;
        height: 18px;
        margin-right: 8px;
      }
      .right{
        .icon{
          width: 16px;
          height: 16px;
        }
      }
      & + .line{
        margin-top: 24px;
      }
      &:last-child{
        margin-top: 0;
      }
      .img-tip{
        width: 252px;
        height: 21px;
        margin-top: 9px;
      }
    }
  }
  .recharge{
    font-size: 16px;
    font-weight: 500;
    color: #3887F5;
    margin-top: 26px;
    text-align: center;
    .iconfont{
      margin-left: 4px;
    }
  }
  .fixed-wrapper{
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 0 24px;
    box-sizing: border-box;
    .btn{
      line-height: 44px;
      font-size: 16px;
      font-weight: 500;
      color: #FFFFFF;
      background: #3887F5;
      border-radius: 22px 22px 22px 22px;
      margin-bottom: 24px;
      text-align: center;
    }
    .xy-line{
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      white-space: nowrap;
      .check{
        width: 16px;
        height: 16px;
        margin-right: 8px;
        flex-shrink: 0;
      }
    }
  }

  .money-wrapper{
    background: #fff;
    text-align: center;
    border-radius: 16px;
    width: 295px;
    padding-bottom: 24px;
    .img{
      width: 170px;
      height: 170px;
      margin: 24px 0 16px;
    }
    .title{
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      margin-bottom: 12px;
    }
    .btn{
      display: inline-block;
      margin-top: 24px;
      line-height: 44px;
      font-size: 16px;
      font-weight: 500;
      color: #FFFFFF;
      background: #3887F5;
      border-radius: 68px 68px 68px 68px;
      width: 237px;
    }
  }
  .close-icon{
    text-align: center;
    color: #fff;
    margin-top: 24px;
    .iconfont{
      font-size: 24px;
    }
  }
}
</style>
