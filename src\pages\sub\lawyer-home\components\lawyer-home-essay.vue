<template>
  <div class="lawyer-home-essay">
    <div class="essay-header flex flex-space-between flex-align-center">
      <p class="text-[16px] font-bold text-[#333333]">
        最新文章
      </p>
      <div
        class="flex flex-align-center"
        @click="toArticleList({lawyerId:lawyerInfo.id})"
      >
        <p class="all-text">
          全部
        </p>
        <img
          alt=""
          class="show-all-icon"
          src="../img/arrow.png"
        >
      </div>
    </div>
    <div
      v-for="i in list"
      :key="i.id"
      class="essay"
    >
      <essay-item :data="i" />
    </div>
  </div>
</template>

<script>
import EssayItem from "@/components/essay-item/index.vue";
import { articleV2LawyerPage } from "@/api/lawyer.js";
import { isObjNull } from "@/libs/basics-tools.js";
import { toArticleList } from "@/libs/turnPages.js";

export default {
  name: "LawyerHomeEssay",
  components: { EssayItem },
  props: {
    lawyerInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      list: []
    };
  },
  watch: {
    lawyerInfo: {
      handler(value) {
        if (isObjNull(value)) return;
        articleV2LawyerPage({
          lawyerId: this.lawyerInfo.id,
          pageNum: 1,
          pageSize: 5,
        }).then(({ data }) => {
          const { total = 0, records = [] } = data;
          this.$store.commit("lawyerHome/SET_ARTICLE_COUNT", total);
          this.list = records;
        });
      },
      deep: true,
      immediate: true,
    },
  },
  methods: { toArticleList },
};
</script>

<style lang="scss" scoped>
  .lawyer-home-essay{
    width: 343px;
    margin: 0 auto;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    .essay-header{
      height: 46px;
      padding: 0 16px;
      .title{
        font-size: 16px;
        font-weight: 500;
        color: #333333;
      }
      .all-text{
        font-size: 13px;
        font-weight: 400;
        color: #666666;
      }
      .show-all-icon{
        width:16px;
        height: 16px;
      }
    }
  }
  .essay{
    &:not(:last-child){
        padding-bottom: 8px;
        position: relative;
        &:after{
            content: "";
            position: absolute;
            width: 319px;
            height: 0.5px;
            background: #F5F5F5;
            bottom: 4px;
            left: 50%;
            transform: translateX(-50%);
        }
    }
  }
</style>
