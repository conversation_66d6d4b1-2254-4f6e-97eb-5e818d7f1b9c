<template>
  <div class="opinion-book">
    <img
      alt=""
      class="top-img"
      mode="widthFix"
      src="../imgs/<EMAIL>"
    >
    <div class="book">
      <div
        v-if="opinionBook.problemDesc"
        class="book-item"
      >
        <div class="book-item-title">
          问题描述
        </div>
        <div class="book-item-content">
          {{ opinionBook.problemDesc }}
        </div>
      </div>
      <div
        v-if="opinionBook.referenceLaw"
        class="book-item"
      >
        <div class="book-item-title">
          参考法条
        </div>
        <div class="book-item-content">
          {{ opinionBook.referenceLaw }}
        </div>
      </div>
      <div
        v-if="opinionBook.consultSuggest"
        class="book-item"
      >
        <div class="book-item-title">
          咨询建议
        </div>
        <div class="book-item-content">
          {{ opinionBook.consultSuggest }}
        </div>
      </div>
    </div>
    <img
      alt=""
      class="bottom-img"
      mode="widthFix"
      src="../imgs/<EMAIL>"
    >
  </div>
</template>

<script>
import { lawWrittenOpinionGetLawWrittenOpinion } from "@/api/im.js";
import { isObjNull } from "@/libs/basics-tools.js";

export default {
  name: "Submissions",
  data() {
    return {
      opinionBook: {}
    };
  },
  onLoad(option) {
    /* 获取意见书*/
    lawWrittenOpinionGetLawWrittenOpinion({
      caseSourceServerId: option.id
    }).then(({ data = {} }) => {
      if(!isObjNull(data)){
        console.log(data);
        this.opinionBook = data;
      }
    });
  }
};
</script>

<style lang="scss" scoped>
.opinion-book {
  min-height: 100vh;
  background: #f0f5ff;
}

.top-img {
  display: block;
  width: 375px;
}

.bottom-img {
  display: block;
  width: 375px;
}

.book {
  width: 351px;
  margin: 0 auto;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 8px;
  opacity: 1;
  padding: 28px 20px 36px 20px;

  &-item {
    &:not(:last-child) {
      margin-bottom: 24px;
    }

    &-title {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: bold;
      color: #333333;

      &::before {
        display: block;
        margin-right: 4px;
        content: " ";
        width: 3px;
        height: 14px;
        background: #3887f5;
        border-radius: 40px;
        opacity: 1;
      }
    }

    &-content {
      margin-top: 12px;
      font-size: 14px;
      font-weight: 400;
      color: #666666;
    }
  }
}
</style>
