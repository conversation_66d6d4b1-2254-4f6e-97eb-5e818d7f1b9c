<template>
  <div class="position-relative">
    <div class="text">
      <div class="text-status">
        {{ statusText }}
      </div>
      <div class="text-tip">
        {{ tipText }}
      </div>
    </div>
    <img
      class="bg-image"
      src="@/pages/pay/img/<EMAIL>"
      alt=""
    >
  </div>
</template>

<script>
export default {
  name: "PayRefundHeader",
  props: {
    /** 状态文案 */
    statusText: {
      type: String,
      default: "申请退单",
    },
    /** 提示文案 */
    tipText: {
      type: String,
      default: "请如实填写原因方便平台尽快为您处理",
    },
  },
};
</script>

<style scoped lang="scss">
.text {
  padding: 23px 16px;

  &-status {
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
  }

  &-tip {
    margin-top: 5px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.6);
  }
}

.bg-image {
  width: 375px;
  height: 325px;
  box-sizing: border-box;
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
</style>
