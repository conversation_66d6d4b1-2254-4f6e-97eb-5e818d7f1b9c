<template>
  <div class="card">
    <div
      class="card__top"
      @click="turnToCaseSourceDetails"
    >
      <div class="flex flex-align-center flex-space-between">
        <div class="flex flex-align-center">
          <div
            :style="{
              backgroundColor: caseGrade.bg,
            }"
            class="card__left"
          >
            {{ caseGrade.label }}
          </div>
          <div class="card__title">
            {{ data.appealTypeStr }}
          </div>
          <div class="card__type">
            {{ (IM_CONVERSATION_TAG_MAP[IM_CASE_SOURCE_TYPE.CASE_SOURCE])[data.serverWay] }}
          </div>
        </div>
        <div class="card__status">
          {{ statusText }}
        </div>
      </div>
      <div class="card__content text-ellipsis-2">
        {{ data.info }}
      </div>
      <div class="card__tips">
        <div class="card__tips__1">
          {{ data.amountGradeStr }}
        </div>
        <!-- * 这个字段现在没了 -->
        <!--        <div class="card__tips__2">-->
        <!--          诉讼意愿强烈-->
        <!--        </div>-->
      </div>
    </div>
    <div class="card__button">
      <div class="card__button__time">
        {{ data.updateTime }}
      </div>
      <div class="flex flex-align-center">
        <div
          v-if="status === 1"
          class="card__button__left"
          @click="popupVisible = true"
        >
          结束跟进
        </div>
        <div
          class="card__button__right"
          @click="handleToIm"
        >
          查看会话
        </div>
      </div>
    </div>
    <clue-rights-detail-card-popup
      v-model="popupVisible"
      @conform="handleEndFollow"
    />
  </div>
</template>

<script>
import { caseSourceServerV2CompleteCaseServer } from "@/api/clue";
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import ClueRightsDetailCardPopup from "@/pages/pay/clue/rights/components/ClueRightsDetailCardPopup.vue";
import { turnToCaseSourceDetails, turnToImPage } from "@/libs/turnPages";
import { CASE_GRADE } from "@/enum";
import { filterEnum } from "@/libs/tools";
import { IM_CASE_SOURCE_TYPE, IM_CONVERSATION_TAG_MAP } from "@/enum/imCardEnum";

export default {
  name: "ClueRightsDetailCard",
  components: { ClueRightsDetailCardPopup, AppPopup },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      popupVisible: false,
    };
  },
  computed: {
    IM_CASE_SOURCE_TYPE() {
      return IM_CASE_SOURCE_TYPE;
    },
    IM_CONVERSATION_TAG_MAP() {
      return IM_CONVERSATION_TAG_MAP;
    },
    /** 案源相关 */
    caseGrade() {
      return filterEnum(CASE_GRADE, this.data.caseGrade);
    },
    /** 状态 */
    status() {
      return Number(this.data.status);
    },
    /** 状态文案 */
    statusText() {
      switch (this.status) {
      case 1:
        return "跟进中";
      case 3:
        return "已完成";
      case 4:
        return "已关闭";
      default:
        return "";
      }
    },
  },
  methods: {
    /** 结束跟进 */
    handleEndFollow() {
      caseSourceServerV2CompleteCaseServer(this.data.caseSourceServerV2Id).then(
        () => {
          this.popupVisible = false;
          this.$emit("endFollow");
        }
      );
    },
    /** 跳转到im */
    handleToIm() {
      turnToImPage({
        id: this.data.imSessionId,
      });
    },
    /** 跳转到案件详情页 */
    turnToCaseSourceDetails() {
      turnToCaseSourceDetails({
        id: this.data.caseSourceServerV2Id,
        type: "3"
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  width: 351px;
  box-sizing: border-box;
  padding: 16px 16px 0 16px;
  background-color: #fff;
  margin: 0 auto;
  border-radius: 12px;

  &__top {
    padding-bottom: 16px;
    border-bottom: 0.5px solid #eeeeee;
  }

  &__left {
    border-radius: 4px;
    opacity: 1;
    padding: 2px 4px;
    font-size: 12px;
    font-weight: 400;
    color: #ffffff;
  }

  &__title {
    margin-left: 8px;
    font-size: 16px;
    font-weight: bold;
    color: #333333;
  }

  &__type {
    margin-left: 8px;
    padding: 2px 6px;
    background: #ebf3fe;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #3887f5;
  }

  &__status {
    font-size: 14px;
    font-weight: 400;
    color: #999999;
  }

  &__content {
    font-size: 15px;
    font-weight: 400;
    color: #333333;
    margin-top: 15px;
  }

  &__tips {
    display: flex;
    align-items: center;
    margin-top: 12px;

    &__1 {
      padding: 2px;
      background: #f5f5f7;
      border-radius: 4px;
      border: 0.5px solid #cccccc;
      font-size: 12px;
      font-weight: 400;
      color: #333333;
    }

    &__2 {
      padding: 2px;
      margin-left: 4px;
      background: #fcf1ed;
      border-radius: 4px;
      opacity: 1;
      border: 0.5px solid #f9e1d9;
      font-size: 12px;
      font-weight: 400;
      color: #d36d64;
    }
  }

  &__button {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;

    &__time {
      font-size: 12px;
      font-weight: 400;
      color: #999999;
    }

    &__left {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 88px;
      height: 28px;
      border-radius: 18px;
      opacity: 1;
      border: 1px solid #CCCCCC;
      font-size: 14px;
      font-weight: 400;
      color: #666666;
    }

    &__right {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 88px;
      height: 28px;
      border: 1px solid #3887F5;
      border-radius: 18px;
      opacity: 1;
      margin-left: 8px;
      font-size: 14px;
      font-weight: 400;
      color: #3887F5;
    }
  }
}
</style>
