<template>
  <div
    class="problem-input position-relative"
  >
    <textarea
      ref="textarea"
      :maxlength="200"
      :value="value"
      placeholder-style="color: #CCC;font-size: 12px;"
      @blur="handleBlur"
      @focus="handleFocus"
      @input="handleInput"
    />
    <div
      v-if="placeholderShow && !value"
      class="placeholder"
    >
      {{ placeholderText }}
    </div>
    <!-- 计数器 -->
    <div class="counter">
      {{ value.length }}/200
    </div>
  </div>
</template>

<script>
export default {
  name: "ServiceIntroductionTextArea",
  props: {
    value: {
      type: String,
      required: true,
      default: "",
    },
    placeholderText: {
      type: String,
      default:
        "为了律师能更准确的解答，请详细、完整的描述您所遇到的问题，以及您希望得到什么样的帮助（10-200字）",
    },
    backgroundColor: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      placeholderShow: true,
    };
  },
  methods: {
    handleFocus(event) {
      this.placeholderShow = false;
      this.$emit("focus", event);
    },
    handleBlur(event) {
      this.placeholderShow = true;
      this.$emit("blur", event);
    },
    handleInput(event) {
      const { value } = event.target;

      // 如果输入的内容超过200个字，则截取前200个字
      let newValue = value;

      if (value.length > 200) {
        newValue = value.slice(0, 200);
      }

      this.$emit("input", newValue);
    },
  },
};
</script>

<style lang="scss" scoped>
.problem-input {
  width: 327px;
  height: 213px;
  box-sizing: border-box;
  background: #F5F5F7;
  border-radius: 8px;
  opacity: 1;
  margin: 0 auto;
  padding: 12px 12px 0 12px;

  textarea {
    font-weight: 400;
    border: none;
    outline: none;
    box-sizing: border-box;
    resize: none;
    width: 100%;
    height: 70% !important;
    font-size: 14px;
    background: inherit;
    letter-spacing: 1px;
    line-height: 16px;
  }
}

.placeholder {
  left: 12px;
  top: 12px;
  right: 12px;
  bottom: 0;
  position: absolute;
  pointer-events: none;
  font-size: 14px;
  font-weight: 400;
  color: #CCCCCC;
  line-height: 16px;
}

.counter {
  position: absolute;
  right: 12px;
  bottom: 12px;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
}
</style>
