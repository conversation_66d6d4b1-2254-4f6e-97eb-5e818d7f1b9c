<template>
  <div
    v-if="changeLawyerTime"
    class="flex change-lawyer flex-align-center"
  >
    <img
      v-if="authChangeLawyer === 0"
      alt=""
      class="change-lawyer__icon"
      src="@/pages/sub/lawyer-home/pay-lawyer-guide/img/0.png"
      @click="autoChangeLawyer(1)"
    >
    <img
      v-if="authChangeLawyer === 1"
      alt=""
      class="change-lawyer__icon"
      src="@/pages/sub/lawyer-home/pay-lawyer-guide/img/1.png"
      @click="autoChangeLawyer(0)"
    >
    <div class="change-lawyer__text">
      {{ showTipAfterText }}
    </div>
  </div>
</template>

<script>
import { getCommonConfigKey } from "@/api";
import { formatTime } from "@/libs/tool";

export default {
  name: "AutoChangeLawyer",
  props: {
    value: {
      type: Number,
      required: true,
    },
    tipAfterText: {
      type: String,
      default: "若当前律师$time未接单，授权平台为您更换在线律师进行服务",
    },
  },
  data() {
    return {
      /** 换律师时间 */
      changeLawyerTime: "",
    };
  },
  computed: {
    authChangeLawyer: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
    showTipAfterText() {
      return this.tipAfterText.replace("$time", this.changeLawyerTime);
    },
  },
  mounted() {
    this.getChangeLawyerInfo();
  },
  methods: {
    /** 获取自动换律师相关信息 */
    getChangeLawyerInfo() {
      getCommonConfigKey({
        paramName: "no_service_1v1_change_lawyer_time",
      }).then((res) => {
        this.changeLawyerTime = formatTime(res.data.paramValue);
      });

      getCommonConfigKey({
        paramName: "no_service_1v1_change_lawyer_time_default",
      }).then((res) => {
        this.authChangeLawyer = Number(res.data.paramValue);
      });
    },
    /**
     * 点击自动换律师
     * @param {0 | 1} item
     */
    autoChangeLawyer(item) {
      this.authChangeLawyer = item;
    },
  },
};
</script>

<style lang="scss" scoped>
.change-lawyer {
  &__icon {
    width: 16px;
    height: 16px;
    display: block;
    flex-shrink: 0;
  }

  &__text {
    margin-left: 8px;
    font-size: 13px;
    font-weight: 400;
    color: #666666;
  }
}
</style>
