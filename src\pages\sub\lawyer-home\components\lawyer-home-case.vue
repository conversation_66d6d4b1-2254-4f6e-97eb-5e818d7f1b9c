<template>
  <div
    v-if="plaintiffData.count || defendantData.count"
    class="case"
  >
    <p class="case-title">
      案件总数
    </p>
    <div class="charts flex flex-align-center">
      <div class="w-[64px] h-[64px]">
        <qiun-data-charts
          :canvas2d="true"
          :chartData="chartData"
          :opts="opts"
          class="w-[64px] h-[64px]"
          canvasId="X6Vu42jzyO5rtLAw"
          type="ring"
        />
      </div>
      <div class="charts-text">
        <div class="charts-text-top flex flex-align-center">
          <p class="charts-text-top-icon" />
          原告律师 {{
            plaintiffData.rate
          }}<span class="text-999999">（{{ plaintiffData.count }}件）</span>
        </div>
        <div class="charts-text-bottom flex flex-align-center">
          <p class="charts-text-bottom-icon" />
          被告律师 {{
            defendantData.rate
          }}<span class="text-999999">（{{ defendantData.count }}件）</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import QiunDataCharts from "@/pages/sub/lawyer-home/ucharts/components/qiun-data-charts/qiun-data-charts.vue";
import { isObjNull } from "@/libs/basics-tools.js";

export default {
  name: "LawyerHomeCase",
  components: { QiunDataCharts },
  props: {
    lawyerInfo: {
      type: Object,
      default: () => ({}),
    },
    /** 图表数据 */
    caseStatistics: {
      type: [Object, Array],
      default: () => [],
    },
    /** 图表总数 */
    total: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      chartData: {},
      /** 图表配置 */
      opts: {
        rotate: false,
        rotateLock: false,
        color: ["#3887F5", "#FF7272"],
        dataLabel: true,
        legend: {
          show: false,
        },
        title: {
          name: "123",
          fontSize: 10,
          color: "#333",
        },
        subtitle: {
          name: "",
        },
        extra: {
          ring: {
            ringWidth: 16,
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            border: false,
            borderWidth: 3,
            borderColor: "#FFFFFF",
            customRadius: 32,
          },
        },
      },
    };
  },
  computed: {
    /** 原告律师数据 */
    plaintiffData() {
      return this.caseStatistics?.[1] || {};
    },
    /** 被告律师数据 */
    defendantData() {
      return this.caseStatistics?.[0] || {};
    },
  },
  watch: {
    caseStatistics: {
      handler(value) {
        if (isObjNull(value)) return;

        this.getServerData();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    getServerData() {
      this.opts.title.name = this.total;

      console.log(this.plaintiffData, "this.plaintiffData");

      let res = {
        series: [
          {
            data: [
              {
                name: "原告律师",
                value: this.plaintiffData.count || 0,
                labelShow: false,
              },
              {
                name: "被告律师",
                value: this.defendantData.count || 0,
                labelShow: false,
              },
            ],
          },
        ],
      };
      this.chartData = JSON.parse(JSON.stringify(res));
    },
  },
};
</script>

<style lang="scss" scoped>
.case {
  width: 343px;
  margin: 0 auto;
  padding: 12px 16px 8px 16px;
  border-radius: 8px;
  background-color: #fff;
  box-sizing: border-box;
}

.case-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.charts {
  margin-top: 12px;
  padding: 12px 0 12px 38px;

  &-text {
    font-size: 12px;
    font-weight: 400;
    margin-left: 24px;

    &-top {
      color: #3887F5;

      &-icon {
        margin-right: 6px;
        width: 6px;
        height: 6px;
        background: #3887F5;
        opacity: 1;
        border-radius: 50%;
      }
    }

    &-bottom {
      color: #ff7272;
      margin-top: 12px;

      &-icon {
        margin-right: 6px;
        width: 6px;
        height: 6px;
        background: #ff7272;
        opacity: 1;
        border-radius: 50%;
      }
    }
  }
}
</style>
