<template>
  <div>
    <div class="mg-tp-12">
      <clue-rights-card :data="detailInfo" />
    </div>
    <div class="mg-tp-24">
      <div
        v-for="item in caseSourceInfo"
        :key="item.caseSourceServerV2Id"
        class="mg-tp-12"
      >
        <clue-rights-detail-card
          :data="item"
          @endFollow="endFollow"
        />
      </div>
    </div>
    <u-safe-bottom />
  </div>
</template>

<script>
import ClueRightsCard from "@/pages/pay/clue/consume/components/ClueRightsCard.vue";
import ClueRightsDetailCard from "@/pages/pay/clue/rights/components/ClueRightsDetailCard.vue";
import { lawyerCaseSourceGoodsServerPage } from "@/api/clue";
import { getClueDetail } from "@/pages/pay/clue/consume/js";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "PayClueConsume",
  components: { USafeBottom, ClueRightsDetailCard, ClueRightsCard },
  onLoad({ id }) {
    this.lawyerCluePackId = id;
    this.detailInfo = getClueDetail();

    this.getDetailInfo(id);
  },
  data() {
    return {
      /** 详情信息 */
      detailInfo: {},
      /** 案源信息 */
      caseSourceInfo: [],
      lawyerCluePackId: "",
    };
  },
  methods: {
    /** 获取详情信息 */
    getDetailInfo() {
      lawyerCaseSourceGoodsServerPage({
        lawyerCluePackId: this.lawyerCluePackId,
      }).then((res) => {
        this.caseSourceInfo = res.data.records;
      });
    },
    /** 点击结束后触发的事件 */
    endFollow() {
      this.getDetailInfo();
    },
  },
};
</script>

<style lang="scss" scoped></style>
