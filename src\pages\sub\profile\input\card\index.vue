<template>
  <div>
    <div class="title">
      请仔细核对信息，如有问题请点击修改
    </div>
    <div class="mg-tp-16">
      <profile-card-tips
        :value="params.idCardBackPic"
        text="上传身份证正面"
      >
        <app-ocr @success="idCardFrontSuccess">
          <img
            :src="idCardFront"
            alt=""
            class="card-image"
          >
        </app-ocr>
      </profile-card-tips>
    </div>
    <div class="mg-tp-12">
      <profile-card-tips
        :value="params.idCardPosPic"
        text="上传身份证反面"
      >
        <app-ocr
          opposite
          @success="idCardBackSuccess"
        >
          <img
            :src="idCardBack"
            alt=""
            class="card-image"
          >
        </app-ocr>
      </profile-card-tips>
    </div>
    <div class="form">
      <div class="form-title">
        请核实您的身份信息
      </div>
      <u-form
        labelPosition="left"
        labelWidth="58"
      >
        <u-form-item
          borderBottom
          label="姓名"
        >
          <u-input
            v-model="params.realName"
            border="none"
          />
        </u-form-item>
        <u-form-item
          borderBottom
          label="身份证"
        >
          <u-input
            v-model="params.idCard"
            border="none"
          />
        </u-form-item>
      </u-form>
    </div>
    <app-bottom :shadow="false">
      <div class="bottom">
        <div
          class="button"
          @click="onClickBack"
        >
          确认无误
        </div>
      </div>
    </app-bottom>
  </div>
</template>

<script>
import AppBottom from "@/components/AppComponents/AppBottom/index.vue";
import UForm from "@/uview-ui/components/u-form/u-form.vue";
import UFormItem from "@/uview-ui/components/u-form-item/u-form-item.vue";
import store from "@/store";
import ProfileCardTips from "@/pages/sub/profile/input/components/ProfileCardTips.vue";
import AppOcr from "@/components/AppComponents/AppOcr/index.vue";

export default {
  name: "ProfileInputCard",
  components: { AppOcr, ProfileCardTips, UFormItem, UForm, AppBottom },
  computed: {
    params() {
      return store.state.lawyerVerify.lawyerVerify;
    },
    /** 身份证正面 */
    idCardFront() {
      return (
        this.params.idCardBackPic ||
        require("@/pages/sub/profile/input/img/Frame1321314969.png")
      );
    },
    /** 身份证反面 */
    idCardBack() {
      return (
        this.params.idCardPosPic ||
        require("@/pages/sub/profile/input/img/Frame1321314976.png")
      );
    },
  },
  methods: {
    /** 正面上传成功 */
    idCardFrontSuccess(data) {
      this.params.idCardBackPic = data.imageUrl;
      this.params.realName = data.name;
      this.params.idCard = data.id;
    },
    /** 反面上传成功 */
    idCardBackSuccess(data) {
      this.params.idCardPosPic = data.imageUrl;
    },
    /** 点击返回上一页 */
    onClickBack() {
      uni.navigateBack();
    },
  },
};
</script>

<style>
page {
  background-color: #fff;
}
</style>

<style lang="scss" scoped>
@import "@/styles/formStyles";

.form {
  padding: 0 16px;
  margin-top: 16px;

  &-title {
    padding: 12px 0;
    font-size: 16px;
    font-weight: bold;
    color: #111111;
  }
}

.title {
  padding: 14px 16px;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  background: #f2f2f2;
}

.card-image {
  display: block;
  margin: 0 auto;
  width: 280px;
  height: 175px;
}

.bottom {
  padding: 10px 16px;
}

.button {
  width: 343px;
  height: 44px;
  margin: 0 auto;
  box-sizing: border-box;
  background: #3887f5;
  border-radius: 68px;
  opacity: 1;
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
