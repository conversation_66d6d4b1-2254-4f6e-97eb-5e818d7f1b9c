<template>
  <div>
    <canvas
      id="qrcode"
      canvas-id="qrcode"
      style="width: 64px; height: 64px; position: fixed; top: 0; right: 9999px"
    />
    <app-popup
      :safeAreaInsetBottom="false"
      :show="show"
      bgColor="transparent"
      mode="center"
    >
      <canvas
        id="canvas"
        canvas-id="canvas"
        style="width: 343px; height: 424px"
      />

      <div
        class="w-[260px] h-[40px] bg-[#FFFFFF] rounded-[70px] mt-[24px] text-[16px] text-[#333333] mx-auto flex items-center"
      >
        <div
          class="flex-1 flex items-center justify-center h-full"
          @click="handleCancel"
        >
          <img
            alt=""
            class="w-[20px] h-[20px] block"
            src="../img/close1.png"
          >
          <div class="ml-[8px]">
            取消
          </div>
        </div>
        <div class="h-[16px] w-[1px] bg-[#EEEEEE]" />
        <div
          class="flex-1 flex items-center justify-center h-full"
          @click="handleSave"
        >
          <img
            alt=""
            class="w-[20px] h-[20px] block"
            src="../img/1111.png"
          >
          <div class="ml-[8px]">
            保存图片
          </div>
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
import * as Painter from "@/pages/sub/lawyer-home/components/painter-core/painter";
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { generateToWeChat } from "@/api";
import UQRCode from "uqrcodejs";

/**
 * ! 经过测试 基础库得大于等于 3.3.4 canvas 才能正常显示
 */
export default {
  name: "LawyerHomeShareCard",
  components: { AppPopup },
  props: {
    lawyerInfo: {
      type: Object,
      default: () => {},
    },
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      context: null,
      /** 链接是否生成错误 */
      urlError: false,
    };
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  watch: {
    lawyerInfo: {
      handler(val) {
        if (val.id) {
          this.init();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    /** 生成链接 */
    generateUrl() {
      return generateToWeChat({
        path: "/pages/sub/lawyer-home/index",
        query: `id=${this.lawyerInfo.id}`,
      })
        .then((res) => {
          return res.data;
        })
        .catch(() => {
          this.urlError = true;
          return "";
        });
    },
    /**
     * 生成分享二维码
     * @returns {Promise<string>}
     */
    initQrcode() {
      return new Promise(async (resolve, reject) => {
        try {
          const data = await this.generateUrl();

          // 获取uQRCode实例
          const qr = new UQRCode();
          // 设置二维码内容
          qr.data = data;
          // 设置二维码大小，必须与canvas设置的宽高一致
          qr.size = 64;
          // 调用制作二维码方法
          qr.make();
          // 设置uQRCode实例的canvas上下文
          qr.canvasContext = uni.createCanvasContext("qrcode", this);
          qr.canvasContext.height = 64;
          qr.canvasContext.width = 64;
          // 调用绘制方法将二维码图案绘制到 canvas上
          // 源码中 then 是在调用draw后150ms触发
          qr.drawCanvas().then(() => {
            // 该方法必须在 canvas 显示的情况下才能生成图片
            uni.canvasToTempFilePath(
              {
                canvasId: "qrcode",
                destWidth: 64,
                destHeight: 64,
                success: (res) => {
                  resolve(res.tempFilePath);
                },
                fail: (error) => {
                  reject(error);

                  console.error(`二维码保存图片失败, ${JSON.stringify(error)}`);
                },
              },
              this
            );
          });
        } catch (e) {
          reject(e);
        }
      });
    },
    async init() {
      Painter.initInjection({
        loadImage: (url) => {
          return new Promise((resolve) => {
            uni.getImageInfo({
              src: url,
              success: (res) => {
                resolve({
                  img: res.path,
                  width: res.width,
                  height: res.height,
                });
              },
              fail: (error) => {
                // 如果图片坏了，则直接置空，防止坑爹的 canvas 画崩溃了
                resolve({
                  img: "",
                  width: 0,
                  height: 0,
                });
                console.error(
                  `getImageInfo ${url} failed, ${JSON.stringify(error)}`
                );
              },
            });
          });
        },
      });

      this.context = uni.createCanvasContext("canvas", this);

      console.log(this.context, "context");

      let qrcode;

      try {
        qrcode = await this.initQrcode();
      } catch (e) {
        console.log(e);
      }

      const pen = new Painter.Pen(this.context, {
        background: require("@/pages/sub/lawyer-home/img/bg2x.png"),
        width: "343px",
        height: "424px",
        borderRadius: "12px",
        views: [
          {
            type: "image",
            url: this.lawyerInfo.imgUrl,
            css: {
              top: "20px",
              left: "20px",
              width: "88px",
              height: "88px",
              borderRadius: "68px",
            },
          },
          {
            type: "image",
            url: require("@/pages/sub/lawyer-home/img/Frame13213155332x.png"),
            css: {
              top: "84px",
              left: "84px",
              width: "24px",
              height: "24px",
            },
          },
          {
            type: "text",
            text: this.lawyerInfo.realName,
            css: {
              fontSize: "20px",
              left: "20px",
              top: "120px",
              color: "#222222",
            },
          },
          {
            type: "inlineText",
            textList: [
              {
                text: this.lawyerInfo.workCityName,
                css: {
                  fontSize: "12px",
                  color: "#999999",
                },
              },
              {
                text: `  执业${this.lawyerInfo.workTime}年`,
                css: {
                  fontSize: "12px",
                  color: "#999999",
                },
              },
            ],
            css: {
              left: "20px",
              top: "152px",
              textAlign: "left",
            },
          },
          {
            type: "text",
            text: this.lawyerInfo.lawyerOffice || "未填写",
            css: {
              fontSize: "12px",
              left: "20px",
              top: "181px",
              color: "#444444",
            },
          },
          {
            type: "image",
            url: require("@/pages/sub/lawyer-home/img/Frame13213155212x.png"),
            css: {
              top: "213px",
              left: "20px",
              width: "56px",
              height: "20px",
            },
          },
          {
            type: "text",
            text: this.lawyerInfo.lawyerProfile,
            css: {
              fontSize: "13px",
              width: "311px",
              lineHeight: "20px",
              left: "20px",
              top: "235px",
              color: "#666666",
              maxLines: 3,
            },
          },
          {
            type: "image",
            url: require("@/pages/sub/lawyer-home/img/icon2x.png"),
            css: {
              top: "305px",
              left: "20px",
              width: "16px",
              height: "16px",
            },
          },
          {
            type: "text",
            text: this.lawyerInfo.score?.toFixed?.(1) || 0,
            css: {
              fontSize: "15px",
              left: "38px",
              top: "305px",
              color: "#F2AF30",
            },
          },
          {
            type: "text",
            text: "服务人数",
            css: {
              fontSize: "15px",
              left: "77px",
              top: "305px",
              color: "#666666",
            },
          },
          {
            type: "text",
            text: this.lawyerInfo.serviceNum,
            css: {
              fontSize: "15px",
              left: "140px",
              top: "305px",
              color: "#3887F5",
            },
          },
          {
            type: "text",
            text: "识别二维码",
            css: {
              fontSize: "12px",
              left: "20px",
              top: "370px",
              color: "#999999",
            },
          },
          {
            type: "text",
            text: "即可查看我的详细资料",
            css: {
              fontSize: "12px",
              left: "20px",
              top: "390px",
              color: "#999999",
            },
          },
          this.urlError
            ? {
              type: "text",
              text: "链接生成错误，联系后端",
              css: {
                top: "380px",
                left: "200px",
                width: "120px",
              },
            }
            : {
              type: "image",
              url: qrcode,
              css: {
                top: "342px",
                left: "260px",
                width: "63px",
                height: "63px",
              },
            },
        ],
      });

      pen.paint(() => {
        this.context.draw();
        console.log("渲染结束了");
      });
    },
    /**
     * 点击取消
     */
    handleCancel() {
      this.show = false;
    },
    /** 保存图片 */
    handleSave() {
      uni.canvasToTempFilePath(
        {
          canvasId: "canvas",
          // * 3倍分辨率防止导出的图片模糊
          destWidth: 1029,
          destHeight: 1272,
          success: (res) => {
            uni.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                uni.showToast({
                  title: "保存成功",
                  icon: "success",
                });

                this.show = false;
              },
              fail: (error) => {
                uni.showToast({
                  title: "保存失败",
                  icon: "none",
                });
                console.error(
                  `saveImageToPhotosAlbum failed, ${JSON.stringify(error)}`
                );

                this.show = false;
              },
            });
          },
          fail: (error) => {
            uni.showToast({
              title: "保存失败",
              icon: "none",
            });
            console.error(`生成图片失败 ${JSON.stringify(error)}`);

            this.show = false;
          },
        },
        this
      );
    },
  },
};
</script>
