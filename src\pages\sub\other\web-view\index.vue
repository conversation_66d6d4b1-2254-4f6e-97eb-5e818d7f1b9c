<template>
  <div>
    <web-view :src="src" />
  </div>
</template>

<script>

import { queryStringToObject } from "@/libs/tools";
import { getUserTokenStorage } from "@/libs/token";
import { axiosBaseHeadersConfig } from "@/libs/config";
import { queryToString } from "@/libs/turnPages";

export default {
  name: "OtherWebView",
  data() {
    return {
      src: "",
      query: {}
    };
  },
  onLoad(options = {}) {
    this.query = options;
  },
  onShow() {
    const { src, isToken = false } = this.query;
    let path = src;
    if(isToken){
      const [url, urlParams = ""] = path.split("?");
      const data = queryStringToObject(urlParams);
      path = url + queryToString({
        ...data, token: getUserTokenStorage(),
        osVersion: axiosBaseHeadersConfig.osVersion,
        /* 加一个时间搓 防止缓存 */
        cacheTime: Date.now(),
      });
    }
    this.src = path;
    console.log(path, "src");
  },
};
</script>

<style lang="scss" scoped></style>
