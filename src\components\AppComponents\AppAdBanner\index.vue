<template>
  <swiper
    :autoplay="true"
    :interval="3000"
    circular
    class="w-full h-full block"
  >
    <swiper-item
      v-for="item in data"
      :key="item.id"
    >
      <img
        :src="item.imageUrl"
        alt=""
        class="w-full h-full block"
        @click="jump(item.addressUrl)"
      >
    </swiper-item>
  </swiper>
</template>

<script>
export default {
  name: "AppAdBanner",
  props: {
    data: {
      type: Array,
      default: () => [],
      required: true,
    },
  },
  methods: {
    jump(addressUrl) {
      if (addressUrl)
        uni.navigateTo({
          url: addressUrl,
        });
    },
  },
};
</script>
