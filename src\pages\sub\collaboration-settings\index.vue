<template>
  <login-layout>
    <tabs
      :list="MY_COLLABORATION_TABS()"
      :activeTabIndex="1"
      mode="router"
    />
    <div class="px-[16px]">
      <div class="px-[16px] mt-[12px] rounded-[12px] bg-[#FFFFFF]">
        <div class="py-[12px]">
          <p class="font-bold text-[16px] text-[#333333]">
            协作服务类型
          </p>
          <p class="text-[12px] text-[#999999] pt-[8px]">
            (系统默认类型如下，您可基于能提供的服务进行手动更改)
          </p>
        </div>
        <div
          v-for="(i, index) in popupList"
          :key="i.key"
          class="py-[12px] flex border-0 border-t-[1px] border-[#EEEEEE] border-solid"
          @click.stop="handleSelect(index)"
        >
          <p class="text-[14px] text-[#666666]">
            {{ i.label }}
          </p>
          <p
            class="text-[14px] text-[#333333] px-[16px] flex-1 text-ellipsis-2"
          >
            {{ getLabels(i.list, formData[i.key]) }}
          </p>
          <i
            class="iconfont icon-erjiyoujiantou !text-[#666666] !text-[16px]"
          />
        </div>
      </div>
      <div class="px-[16px] mt-[12px] rounded-[12px] bg-[#FFFFFF]">
        <div class="py-[12px] flex items-center">
          <p class="font-bold text-[16px] text-[#333333]">
            账号设置
          </p>
        </div>
        <div
          class="py-[12px] flex items-center justify-between border-0 border-t-[1px] border-[#EEEEEE] border-solid"
        >
          <div class="flex-1">
            <p class="text-[14px] text-[#666666]">
              注册手机号
            </p>
          </div>
          <div class="text-[14px] text-[#333333]">
            {{ phoneNumber }}
          </div>
        </div>
        <div
          class="py-[12px] flex items-center justify-between border-0 border-t-[1px] border-[#EEEEEE] border-solid"
          @click="handleAddPhone"
        >
          <div class="flex-1">
            <p class="text-[14px] text-[#666666]">
              新增手机号
            </p>
          </div>
          <div class="flex items-center">
            <div
              class="text-[14px]"
              :class="[otherPhone ? 'text-[#333333]' : 'text-[#CCCCCC]']"
            >
              {{ otherPhone || "点击填写" }}
            </div>
            <i
              class="iconfont icon-erjiyoujiantou !text-[#666666] !text-[16px] ml-[4px]"
            />
          </div>
        </div>
        <div
          class="py-[12px] flex items-center justify-between border-0 border-t-[1px] border-[#EEEEEE] border-solid"
          @click="handleWechatBind"
        >
          <div class="flex-1">
            <p class="text-[14px] text-[#666666]">
              微信绑定
            </p>
          </div>
          <div class="flex items-center">
            <div
              class="text-[14px]"
              :class="[wechatId ? 'text-[#333333]' : 'text-[#CCCCCC]']"
            >
              {{ wechatId || "点击填写" }}
            </div>
            <i
              class="iconfont icon-erjiyoujiantou !text-[#666666] !text-[16px] ml-[4px]"
            />
          </div>
        </div>
      </div>
      <popup-checked
        :show.sync="popupState"
        :list="popupListData"
        :value="formData[popupData.key]"
        @submit="handleSelectType"
      >
        <div class="pt-[48px]">
          <p class="font-bold text-[16px] flex justify-center text-[#333333]">
            请选择您“<span class="text-[#3887F5]">{{ popupData.title }}</span>”可提供的服务类型，可多选
          </p>
          <div class="px-[16px] pt-[12px] pb-[24px]">
            <p
              class="text-[12px] text-[#EB4738] text-align-center py-[8px] bg-[#FDF1ED] rounded-[8px]"
            >
              提示：我们将根据您的设置，在异地找律师筛选时进行展示
            </p>
          </div>
        </div>
      </popup-checked>
    </div>
    <other-phone-bind-popup v-model="otherPhoneBindPopup" />
  </login-layout>
</template>

<script>
import { dataDetailList } from "@/api";
import {
  lawyerCollaborationConfigQuery,
  lawyerCollaborationConfigUpdate,
} from "@/api/collaboration";
import { MY_COLLABORATION_TABS } from "@/enum/collaboration";
import PopupChecked from "@/pages/sub/components/PopupChecked/index.vue";
import Tabs from "@/components/AppTabs/index.vue";
import { mapGetters } from "vuex";
import { whetherToBindWechat } from "@/libs/tools";
import LoginLayout from "@/components/login/LoginLayout.vue";
import OtherPhoneBindPopup from "@/pages/sub/collaboration-settings/components/OtherPhoneBindPopup.vue";

export default {
  name: "CollaborationSettings",
  components: { LoginLayout, Tabs, PopupChecked, OtherPhoneBindPopup },
  data() {
    return {
      popupState: false,
      otherPhoneBindPopup: false,
      popupList: [
        {
          label: "异地查档领域",
          key: "ydcdList",
          list: [],
          title: "异地查档",
        },
        {
          label: "案件协作领域",
          key: "ajxzList",
          list: [],
          title: "案件协作",
        },
      ],
      popupIndex: 0,
      formData: {
        collaborationPhoneAllow: 0,
        personalWebsiteAllow: 0,
        ydcdList: [],
        ajxzList: [],
      },
    };
  },
  computed: {
    ...mapGetters({
      userInfo: "user/getUserInfo",
    }),
    /* 当前弹窗弹出的数据 */
    popupData() {
      return this.popupList[this.popupIndex];
    },
    /* 弹窗展示的list */
    popupListData() {
      return this.popupData.list;
    },
    phoneNumber() {
      return this.userInfo.phone;
    },
    wechatId() {
      return this.userInfo.wechatId;
    },
    otherPhone() {
      return this.userInfo.otherPhone;
    },
  },
  mounted() {
    /* 获取查档类型列表*/
    dataDetailList({
      groupCode: "LAWYER_COLLABORATION_CD_TYPE",
    }).then(({ data = [] }) => {
      this.popupList[0].list = data;
    });

    /* 获取协作类型列表*/
    dataDetailList({
      groupCode: "LAWYER_COLLABORATION_XZ_TYPE",
    }).then(({ data = [] }) => {
      this.popupList[1].list = data;
    });

    lawyerCollaborationConfigQuery().then(({ data }) => {
      this.formData = data;
    });
  },
  methods: {
    handleWechatBind() {
      whetherToBindWechat(null, {
        edit: true,
      });
    },
    MY_COLLABORATION_TABS() {
      return MY_COLLABORATION_TABS;
    },
    /* 弹窗 */
    handleSelect(index) {
      this.popupState = true;
      this.popupIndex = index;
    },
    getLabels(list = [], data = []) {
      return list
        .filter((item) => data.includes(item.value))
        .map((item) => item.label)
        .join("、");
    },
    /* form修改 */
    formUpdate() {
      lawyerCollaborationConfigUpdate(this.formData);
    },
    handleSelectType(data) {
      this.formData[this.popupData.key] = data;
      this.formUpdate();
    },
    handleAddPhone() {
      this.otherPhoneBindPopup = true;
    },
  },
};
</script>

<style scoped lang="scss"></style>
