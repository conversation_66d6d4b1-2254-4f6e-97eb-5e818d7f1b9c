<template>
  <theme-layout-card :theme="theme">
    <p class="desc">
      {{ customExts.title }}<span class="phone">{{ customExts.phone }}</span>
    </p>
  </theme-layout-card>
</template>

<script>

import ThemeLayoutCard from "@/pages/sub/im/components/Cards/ThemeLayoutCard/index.vue";
import cardProps, { customComputed } from "@/pages/sub/im/mixins/card-props.js";

export default {
  name: "ContactInfoLawyerCard",
  components: { ThemeLayoutCard },
  mixins: [cardProps, customComputed]
};
</script>

<style scoped lang="scss">
.desc{
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  .phone{
    color: #366EFF;
  }
}
</style>
