<template>
  <login-layout>
    <div class="min-h-[100vh] bg-[#FFFFFF]">
      <div class="bg-[#3A4965] px-[16px] pt-[16px] pb-[30px]">
        <div class="flex items-center justify-between pb-[24px]">
          <p class="text-[22px] text-[#FFFFFF]">
            {{ details.typeLabel }}
          </p>
          <p
            class="text-[14px] text-[#D7D7D7]"
            @click="toAnswerSpecification"
          >
            解答示例
          </p>
        </div>
        <div class="text-[16px] text-[#FFFFFF] pb-[24px] break-all">
          {{ details.detail }}
        </div>
        <div class="flex items-center">
          <img
            :src="details.userHeadImg"
            alt=""
            class="w-[36px] h-[36px] rounded-[16px]"
          >
          <div class="pl-[8px]">
            <p class="text-[14px] text-[#CCCCCC]">
              {{ details.userName }}
            </p>
            <p class="text-[12px] text-[#999999]">
              {{ details.createTime }} | {{ details.regionName }}
            </p>
          </div>
        </div>
      </div>
      <div
        class="bg-[#FFFFFF] rounded-tl-[16px] p-[16px] rounded-br-none rounded-tr-[16px] rounded-bl-none mt-[-16px] overflow-hidden"
      >
        <!--   有其他来律师解答   -->
        <div
          v-if="!isArrNull(lawyerHeadImgs)"
          class="relative bg-[#EEEEEE] rounded-[8px] px-[12px] py-[16px]"
        >
          <img
            alt=""
            class="w-[48px] h-[48px] absolute absolute-y-center right-[14px]"
            src="@/pages/sub/imgs/<EMAIL>"
          >
          <div
            class="flex items-center font-bold text-[14px] text-[#333333] pb-[8px]"
          >
            <div class="flex items-center pr-[4px]">
              <img
                v-for="(i, index) in lawyerHeadImgs"
                :key="index"
                :src="i"
                alt=""
                class="w-[20px] border-[#FFFFFF] border-[1px] border-solid rounded-[20px] h-[20px] ml-[-6px] first-of-type:ml-[0]"
              >
            </div>
            {{ details.rushAnswerCount }}位律师已解答
          </div>
          <p class="text-[12px] text-[#999999] max-w-[252px]">
            法临为保护平台律师的知识产权及用户的隐私，律师之间互相不能查看解答内容
          </p>
        </div>
        <!--   没有其他律师解答   -->
        <div v-else>
          <img
            alt=""
            class="block w-full h-[79px]"
            src="@/pages/sub/imgs/<EMAIL>"
            @click.stop="changeInstructionsState"
          >
        </div>
        <!--  自己解答     -->
        <div
          v-if="hasRushAnswer"
          class="pt-[16px]"
        >
          <div class="flex items-center">
            <img
              :src="replie.lawyerHeadImg"
              alt=""
              class="w-[40px] h-[40px] rounded-[20px]"
            >
            <div class="pl-[12px]">
              <p class="font-bold text-[15px] text-[#333333]">
                {{ replie.lawyerName }}律师
              </p>
              <p class="text-[12px] text-[#999999] pt-[4px]">
                {{ replie.lawyerWorkOffice }}
              </p>
            </div>
          </div>
          <div class="pt-[16px] text-[14px] text-[#666666] break-all">
            {{ replie.content }}
          </div>
        </div>
        <!--  自己没有解答的提示语   -->
        <p
          v-else
          class="text-[14px] text-[#999999] text-align-center pt-[61px]"
        >
          您暂未解答，快来帮Ta
        </p>
      </div>
      <div
        v-if="!hasRushAnswer"
        class="fixed bottom-0 absolute-x-center"
      >
        <img
          alt=""
          class="w-[359px] h-[74px]"
          src="@/pages/sub/imgs/<EMAIL>"
          @click.stop="toAnswer"
        >
        <u-safe-bottom />
      </div>
      <u-safe-bottom />
      <qa-instructions :show.sync="instructionsState" />
    </div>
  </login-layout>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import QaInstructions from "@/pages/sub/components/QaInstructions/index.vue";
import { toAnswerReply, toAnswerSpecification, turnToLawyerAuthResultPageToLogin } from "@/libs/turnPages";
import { isArrNull, isNull } from "@/libs/basics-tools";
import { qaMessageDetail } from "@/api/qa";
import LoginLayout from "@/components/login/LoginLayout.vue";

export default {
  name: "Index",
  components: { LoginLayout, QaInstructions, USafeBottom },
  data() {
    return {
      /* 解答说明状态 */
      instructionsState: false,
      details: {},
      id: "",
    };
  },
  onLoad({ id = "" }) {
    this.id = id;
  },
  onShow() {
    if (isNull(this.id)) return;
    qaMessageDetail({
      qaMessageId: this.id,
      detailRequestType: 1,
    }).then(({ data = {} }) => {
      this.details = data;
    });
  },
  computed: {
    lawyerHeadImgs() {
      const lawyerHeadImg = this.details.lawyerHeadImg || "";
      if (isNull(lawyerHeadImg)) return [];
      return lawyerHeadImg.split(",");
    },
    /* 自己有没有解答 */
    hasRushAnswer() {
      return (
        !isNull(this.details.hasRushAnswer) && this.details.hasRushAnswer === 1
      );
    },
    /* 自己回复的内容 */
    replie() {
      /* 没有解答 */
      if (!this.hasRushAnswer) return {};
      const replies = this.details.replies;
      if (isNull(replies) || isArrNull(replies)) return {};
      return replies[0];
    },
  },
  methods: {
    isNull,
    isArrNull,
    toAnswerSpecification,
    changeInstructionsState() {
      this.instructionsState = !this.instructionsState;
    },
    toAnswer() {
      turnToLawyerAuthResultPageToLogin(() => {
        toAnswerReply(this.details);
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
