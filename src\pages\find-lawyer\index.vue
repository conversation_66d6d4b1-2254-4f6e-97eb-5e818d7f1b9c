<template>
  <login-layout>
    <div>
      <lawyer-list v-if="activeTabIndex===0">
        <app-tabs
          :activeTabIndex.sync="activeTabIndex"
          :list="list"
        />
      </lawyer-list>
      <group-list v-if="activeTabIndex===1">
        <app-tabs
          :activeTabIndex.sync="activeTabIndex"
          :list="list"
        />
      </group-list>
    </div>
  </login-layout>
</template>

<script>
import LoginLayout from "@/components/login/LoginLayout.vue";
import LawyerList from "@/pages/find-lawyer/components/LawyerList.vue";
import AppTabs from "@/components/AppTabs/index.vue";
import GroupList from "@/pages/find-lawyer/components/GroupList.vue";

export default {
  name: "FindLawyer",
  components: { GroupList, AppTabs, LawyerList, LoginLayout },
  data() {
    return {
      list: [
        {
          label: "找律师",
          value: 1
        }, {
          label: "同行群",
          value: 2
        }
      ],
      activeTabIndex: 1,
    };
  },
  onShow(){
    this.$store.dispatch("user/updateRemainContactLawyer");
    this.$store.dispatch("user/updateLawyerGroupConfig");
  },
  onReachBottom() {
  },
  onPullDownRefresh() {
  }
};
</script>

<style lang="scss" scoped>

</style>
