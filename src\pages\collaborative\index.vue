<template>
  <login-layout>
    <div class="relative  h-[142px]">
      <img
        alt=""
        class="absolute w-full h-[142px] top-0 left-0 right-0"
        src="@/img/<EMAIL>"
      >
      <div
        class="relative z-1 pl-[20px]"
        @click.stop="toFind<PERSON><PERSON>yer()"
      >
        <p class="font-bold text-[20px] text-[#1D3C5B] pt-[20px] pb-[8px]">
          按需求找律师
        </p>
        <p class="text-[14px] text-[#2E5271] flex items-center">
          搜索同城、异地律师，直接建立联系
          <i class="iconfont icon-shuangjiantou text-[#2E5271] icon-an" />
        </p>
      </div>
    </div>
    <div class="pl-[16px] pt-[16px] bg-[#F5F5F7] relative z-2 mt-[-48px] rounded-tl-[20px] rounded-br-none rounded-tr-[20px] rounded-bl-none">
      <div class="flex pb-[12px] items-end">
        <p class="font-bold text-[16px] text-[#000000]">
          接单广场
        </p>
        <p class="pl-[4px] text-[13px] text-[#666666]">
          发布需求平台推送全国律师，同城异地都可接单
        </p>
      </div>
      <div class="flex pb-[5px] items-center">
        <div class=" flex items-center pr-[3px]">
          <img
            v-for="(i,index) in (statistics.avatars||[])"
            :key="index"
            :src="i"
            alt=""
            class="w-[20px] border-[#FFFFFF] border-[1px] border-solid rounded-[20px] h-[20px] ml-[-6px] first-of-type:ml-[0]"
          >
        </div>
        <p class="flex items-center text-[14px] text-[#333333]">
          今日发布<span class="text-[#3887F5]">{{ statistics.count }}</span>条异地协作需求
        </p>
      </div>
    </div>
    <u-sticky>
      <search-tab-bar @searchChange="searchChange" />
    </u-sticky>
    <collaboration-no-data
      v-if="isArrNull(list)&&isRequest"
      @handleClick="toPublishCollaboration"
    />
    <div v-else>
      <div
        v-for="i in list"
        :key="i.id"
        class="px-[12px] pt-[12px] first:pt-0"
      >
        <collaboration-item
          :data="i"
          needMatched
          @handleClick="handleClick"
          @share="handleShare"
        />
      </div>
    </div>
    <!-- 回到顶部按钮 -->
    <div
      v-if="scrollTop > 400"
      class="fixed bottom-[30px] right-[12px] z-10"
    >
      <div 
        class="w-[50px] h-[50px] bg-[#FFFFFF] [box-shadow:0px_0px_8px_0px_rgba(0,0,0,0.14)] rounded-full flex items-center justify-center"
        @click="handleBackToTop"
      >
        <img
          class="block w-[24px] h-[24px]"
          src="@/pages/collaborative/img/fdh6jv.png"
          alt=""
        >
      </div>
    </div>
    
    <!-- 发布按钮区域 -->
    <div class="fixed bottom-[96px] right-0 flex flex-col items-end">
      <img
        v-if="!hasPublishedCollaboration"
        class="block w-[156px] h-[30px] mb-[8px]"
        alt=""
        src="@/pages/collaborative/img/963r6l.png"
      >
      <div class="w-[56px] h-[56px] flex items-center justify-center bg-[linear-gradient(_116deg,_#71B5FF_0%,_#2676E4_100%)] [box-shadow:0px_4px_8px_0px_rgba(59,139,246,0.2)] rounded-[68px] box-border mr-[12px]">
        <div
          class="font-bold text-[14px] text-[#FFFFFF] [text-shadow:0px_1px_6px_#2969C0]"
          @click="toPublishCollaboration"
        >
          发布
        </div>
      </div>
    </div>
  </login-layout>
</template>

<script>
import { lawyerCollaborationQueryPubCount } from "@/api";
import {
  lawyerCollaborationCooperativeSquarePage,
  lawyerCollaborationMoment
} from "@/api/collaboration";
import LoginLayout from "@/components/login/LoginLayout.vue";
import { isArray, isArrNull } from "@/libs/basics-tools";
import { amountFilterTwo } from "@/libs/filter";
import { shareAppMessage } from "@/libs/tools";
import {
  toCollaborationDetails,
  toFindLawyer,
  toPublishCollaboration,
  turnToLawyerAuthResultPageToLogin
} from "@/libs/turnPages";
import CollaborationItem from "@/pages/index/components/collaborationItem.vue";
import CollaborationNoData from "@/pages/index/components/collaborationNoData.vue";
import SearchTabBar from "@/pages/index/components/searchTabBar.vue";
import USticky from "@/uview-ui/components/u-sticky/u-sticky.vue";

export default {
  name: "Collaborative",
  components: {
    USticky,
    LoginLayout,
    CollaborationNoData,
    CollaborationItem,
    SearchTabBar
  },
  data() {
    return {
      query: {
        currentPage: 1,
        pageSize: 20
      },
      list: [],
      /** 是否请求过 */
      isRequest: false,
      isLastPage: false,
      /* 统计数据 */
      statistics: {
        count: 0, //	否	Integer	协作数量
        avatars: []// 否	List	律师头像列表
      },
      /** 
       * 用户是否发布过协作
       * https://lanhuapp.com/web/#/item/project/product?tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&pid=a5456a1a-f92e-4584-a941-12bac5769f74&versionId=33fc7099-35b5-4d6b-8ad8-b326e1d01656&docId=0c78db2c-1bc7-4e96-b6f0-ca2c0cb896f7&docType=axure&pageId=3848c979f477469cbacdf0d12998f1fe&image_id=0c78db2c-1bc7-4e96-b6f0-ca2c0cb896f7&parentId=7e91a4b8-4e51-4e3f-810f-4962d93d68fd
       */
      hasPublishedCollaboration: false,
      /** 页面滚动距离 */
      scrollTop: 0,
      shareData: {}
    };
  },
  mounted(){
    this.resetData();
    this.getData();

    lawyerCollaborationMoment().then(({ data = {} }) => {
      this.statistics = data;
    });

    // 检查用户是否发布过协作
    this.checkUserPublishedCollaboration();
  },
  onReachBottom() {
    this.query.currentPage++;
    this.getData();
  },
  onPullDownRefresh() {
    console.log("refresh");
    this.resetData();
    this.getData().finally(() => {
      uni.stopPullDownRefresh();
    });
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
  onShareAppMessage() {
    return shareAppMessage({
      title: `【${this.shareData.regionName}】${this.shareData.bizTypeLabel}，预算${amountFilterTwo(this.shareData.amount)}元，${this.shareData.info}`,
    });
  },
  methods: {
    isArray,
    toFindLawyer,
    toPublishCollaboration(){
      turnToLawyerAuthResultPageToLogin(() => {
        toPublishCollaboration({
          showHistoryPopup: "1"
        });
      });
    },
    isArrNull,
    handleClick({ data, isPendingOrders }){
      if(isPendingOrders){
        toCollaborationDetails(data);
      }
    },
    handleShare(data){
      this.shareData = data;
    },
    searchChange(data){
      this.query  = {
        ...this.query,
        ...data
      };
      this.resetData();
      this.getData();
    },
    /** 请求数据 */
    getData() {

      if (this.isLastPage) {
        return Promise.resolve();
      }

      return lawyerCollaborationCooperativeSquarePage({
        ...this.query
      }).then(res => {
        this.list = [...this.list, ...res.data.records];

        if (this.list.length >= res.data.total) {
          this.isLastPage = true;
        }
        return this.list;
      })
        .finally(() => {
          this.isRequest = true;
        });
    },
    /** 重置数据 */
    resetData() {
      this.list = [];
      this.query.currentPage = 1;
      this.isLastPage = false;
      this.isRequest = false;
    },
    /** 检查用户是否发布过协作 */
    checkUserPublishedCollaboration() {
      lawyerCollaborationQueryPubCount().then(({ data }) => {
        this.hasPublishedCollaboration = data.count > 0;
      }).catch(() => {
        this.hasPublishedCollaboration = false;
      });
    },
    /** 回到顶部并刷新数据 */
    handleBackToTop() {
      // 先滚动到顶部
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
      // 然后重置并获取数据
      this.resetData();
      this.getData();
    },
  }
};
</script>
<style lang="scss" scoped>
.icon-an{
  -webkit-animation: zoom 1s infinite;
  -o-animation: zoom 1s infinite;
  animation: zoom 1s infinite;
}
/*缩放动画*/
@keyframes  zoom {
  0% {
    transform: scale(1);
  }
  50%{
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

</style>
