import { INDICATOR_NAME } from "@/libs/config.js";
import { bigdataLawyerIndex } from "@/api/lawyer.js";

export default {
  data() {
    return {
      INDICATOR_NAME,
      /** 选择的tab */
      current: INDICATOR_NAME.caseReason,
      tabList: [
        {
          label: "案由分布",
          key: INDICATOR_NAME.caseReason,
        },
        {
          label: "常去法院",
          key: INDICATOR_NAME.courtName,
        },
        {
          label: "案件标的",
          key: INDICATOR_NAME.caseAmt,
        },
        {
          label: "近期案件数",
          key: INDICATOR_NAME.recentCase,
        },
      ],
      all: {
        /* 案由分布*/
        [INDICATOR_NAME.caseReason]: [],
        /* 常去法院*/
        [INDICATOR_NAME.courtName]: [],
        /* 案件标的*/
        [INDICATOR_NAME.caseAmt]: [],
        /* 近期案件数*/
        [INDICATOR_NAME.recentCase]: [],
      },
    };
  },
  methods: {
    getCaseStatisticsAll(list = []) {
      if (!this.lawyerInfo.realName || !this.lawyerInfo.lawyerOffice)
        return Promise.resolve({});
      const all = {};
      const promiseAll = Promise.all(
        list.map(({ key }) =>
          bigdataLawyerIndex({
            lawyerName: this.lawyerInfo.realName,
            lawfirmName: this.lawyerInfo.lawyerOffice,
            indexKey: key,
          }).then((res) => {
            all[key] = res.data.indexElements || [];
            return all[key];
          })
        )
      );
      return promiseAll.then(() => {
        this.$store.commit("lawyerHome/CALL_TITLE_CALLBACK");
        return all;
      });
    },
    changeTabOne(key) {
      this.current = key;
    },
  },
};
