<template>
  <div class="content">
    <div class="title">
      上传证件
    </div>
    <div class="tips">
      我们承诺将严格保护您的隐私，上传的证件照仅作审核使用
      证件照不会对外传播。<span class="text-eb4738">“请勿故意遮挡或模糊执业证信息”</span>
    </div>
    <div class="upload">
      <profile-card-tips
        :value="params.certificatePic"
        hide
      >
        <header-update-basic v-model="params.certificatePic">
          <template #default="{ header }">
            <div class="w-screen">
              <img
                v-if="header"
                :src="header"
                alt=""
                class="image"
              >
              <img
                v-else
                alt=""
                class="image"
                src="../img/39.png"
              >
            </div>
          </template>
        </header-update-basic>
      </profile-card-tips>
    </div>
    <div class="bottom-text">
      请确保您上传的图片能清晰显示所有信息 以便工作人员加快审核进度
    </div>
    <app-bottom :shadow="false">
      <div
        v-if="params.certificatePic"
        class="bottom"
        @click="onClickBack"
      >
        <div class="button">
          提交
        </div>
      </div>
      <header-update-basic
        v-else
        v-model="params.certificatePic"
      >
        <div class="w-screen">
          <div class="bottom">
            <div class="button">
              选择照片/拍照
            </div>
          </div>
        </div>
      </header-update-basic>
    </app-bottom>
  </div>
</template>

<script>
import ProfileCardTips from "@/pages/sub/profile/input/components/ProfileCardTips.vue";
import store from "@/store";
import HeaderUpdateBasic from "@/pages/sub/profile/lawyer/components/HeaderUpdateBasic.vue";
import AppBottom from "@/components/AppComponents/AppBottom/index.vue";

export default {
  name: "ProfileInputProfession",
  components: { AppBottom, HeaderUpdateBasic, ProfileCardTips },
  computed: {
    params() {
      return store.state.lawyerVerify.lawyerVerify;
    },
  },
  methods: {
    /** 点击返回上一页 */
    onClickBack() {
      uni.navigateBack();
    },
  }
};
</script>

<style>
page {
  background-color: #fff;
}
</style>

<style lang="scss" scoped>
.content {
  text-align: center;
}

.title {
  margin-top: 32px;
  font-size: 24px;
  font-weight: bold;
  color: #111111;
}

.upload {
  margin-top: 50px;
}

.image {
  margin: 0 auto;
  width: 343px;
  height: 190px;
  border-radius: 13px;
  display: block;
}

.tips {
  width: 343px;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  margin: 11px auto 0 auto;
}

.bottom-text {
  width: 252px;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  margin: 24px auto 0 auto;
}

.button {
  width: 343px;
  height: 44px;
  margin: 0 auto;
  box-sizing: border-box;
  background: #3887f5;
  border-radius: 68px;
  opacity: 1;
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
