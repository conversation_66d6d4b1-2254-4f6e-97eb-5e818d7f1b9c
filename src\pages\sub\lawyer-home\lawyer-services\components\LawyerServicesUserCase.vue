<template>
  <div class="card">
    <div class="title">
      用户案例
    </div>
    <div>
      <div
        v-for="(item,index) in userCaseData"
        :key="index"
        class="item"
      >
        <div class="info">
          <img
            alt=""
            class="header"
            src="https://oss.imlaw.cn/test/image/core/2023/10/17/8ac80d31ed92410db9db997596c349f2.png"
          >
          <div class="name">
            {{ item.nickName }}
          </div>
        </div>
        <div class="content">
          {{ item.content }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { dataDictionary } from "@/api";

export default {
  name: "LawyerServicesUserCase",
  props: {
    scene: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      /** 用户案例数据 */
      userCaseData: [],
    };
  },
  mounted() {
    this.getUserCaseData();
  },
  methods: {
    /** 获取用户案例数据 */
    getUserCaseData() {
      dataDictionary({ groupCode: `lawyer_user_${this.scene}` }).then(
        ({ data = [] }) => {
          this.userCaseData = data.map((item) => {
            try {
              return JSON.parse(item.remark);
            } catch (e) {
              return {};
            }
          });
        }
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  box-sizing: border-box;
  margin: 0 auto;
  width: 351px;
  background-color: #fff;
  border-radius: 8px;
  opacity: 1;
  padding: 12px;
}

.title {
  padding-bottom: 12px;
  border-bottom: 1px solid #EEEEEE;
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.item {
  padding: 12px 0;
  border-bottom: 1px solid #EEEEEE;

  &:last-child {
    padding-bottom: 0;
    border-bottom: none;
  }

  .info {
    display: flex;
    align-items: center;

    .header {
      display: block;
      width: 20px;
      height: 20px;
    }

    .name {
      margin-left: 4px;
      font-size: 12px;
      font-weight: 400;
      color: #666666;
    }
  }

  .content {
    margin-top: 12px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
  }
}

</style>
