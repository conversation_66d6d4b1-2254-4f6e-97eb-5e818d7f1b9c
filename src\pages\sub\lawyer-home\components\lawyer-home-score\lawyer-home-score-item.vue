<template>
  <div>
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <img
          :src="data.imgUrl || require('@/pages/sub/lawyer-home/img/avator.png')"
          alt=""
          mode="aspectFill"
          class="w-[27px] h-[27px] rounded-full block mr-[8px]"
        >
        <div>
          <div class="text-[10px] text-[#999999]">
            {{ data.nickName }}
          </div>
          <p class="text-[11px] text-[#999999] mt-[4px]">
            于{{ time }}购买了<span class="text-[#3887F5]">{{
              data.serviceName || ""
            }}</span>后评价
          </p>
        </div>
      </div>
      <div class="flex items-center">
        <img
          alt=""
          class="w-[14px] h-[14px] block mr-[2px]"
          src="../../../../../components/login/imgs/<EMAIL>"
        >
        <div class="text-[13px] font-bold text-[#F2AF30]">
          {{ data.score.toFixed(1) }}
        </div>
      </div>
    </div>
    <p
      :class="{ 'text-ellipsis-2': !showAll }"
      class="text-[14px] text-[#222222] mt-[12px]"
    >
      {{ content }}
    </p>
    <div
      v-if="labels.length > 0"
      class="flex flex-wrap"
    >
      <div
        v-for="item in labels"
        :key="item.labelId"
        class="px-[10px] box-border h-[19px] bg-[#F5F5F7] rounded-[68px] text-[11px] text-[#666666] flex items-center justify-center mt-[8px] mr-[8px]"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from "dayjs";

export default {
  name: "LawyerHomeScoreItem",
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    showAll: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    /** 评价内容 */
    content() {
      return this.data.content || "" || "暂无评价内容";
    },
    labels() {
      return this.data.labels || [];
    },
    /** 评价时间 */
    time() {
      return dayjs(this.data?.createTime).format("YYYY.MM月DD日") || "";
    },
  },
};
</script>
