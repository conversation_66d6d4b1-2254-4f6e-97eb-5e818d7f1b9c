<template>
  <app-popup
    :safeAreaInsetBottom="false"
    :show="show"
    mode="center"
    bgColor="#fff"
    round="16"
    @close="close"
  >
    <div class="case-prompt">
      <div class="title">
        温馨提示
      </div>
      <div class="info">
        该案源为{{ currentCase }}案源，<span class="color-text">升级线索会员</span>后可免费锁定，或使用法临币原价锁定
      </div>
      <div class="bg-wrapper">
        <div
          v-for="item in list"
          :key="item.typeName"
          class="text-line"
        >
          <span class="label">{{ item.typeName }}：</span>
          <div
            v-for="(li,index) in item.caseSourceList"
            :key="index"
            class="text"
            :class="'type'+li"
          >
            {{ getText(li) }}
          </div>
        </div>
      </div>
      <div class="btn-wr flex flex-space-between">
        <div
          class="btn default"
          @click="toGetCaseSource"
        >
          原价锁定
        </div>
        <div class="btn primary" @click="turnToRechargePage">
          去充值 <span class="tag">免费抢单</span>
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { getGoodsTypeGrabScope } from "@/api";
import {turnToCaseSourcePayPage, turnToRechargePage} from "@/libs/turnPages";

export default {
  components: { AppPopup },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    currentCase: {
      type: String,
      default: ""
    },
    detail: {
      type: Object,
      default: ""
    },
  },
  data() {
    return {
      list: []
    };
  },
  mounted() {
    getGoodsTypeGrabScope().then(({ data = [] }) => {
      this.list = data;
    });
  },
  methods: {
    turnToRechargePage,
    getText(item){
      const caseGrade = this.$store.getters["commonData/getCaseGrade"];
      return caseGrade.find(li => li.value === item).label;
    },
    close(){
      this.$emit("update:show", false);
    },
    toGetCaseSource() {
      turnToCaseSourcePayPage({
        id: this.detail.id
      });
    }
  }

};
</script>

<style lang="scss" scoped>
.case-prompt{
  width: 311px;
  .title{
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    margin-top: 24px;
    text-align: center;
  }
  .info{
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    margin-bottom: 20px;
    margin-top: 16px;
    padding: 0 16px;
    text-align: center;
    .color-text{
      color: #3887F5;
    }
  }
  .bg-wrapper{
    width: 279px;
    margin: 0 auto;
    background: #F4F8FE;
    border-radius: 8px 8px 8px 8px;
    padding: 16px;
    box-sizing: border-box;
    .text-line{
      display: flex;
      align-items: center;
      & + .text-line{
        margin-top: 16px;
      }
      .label{
        font-size: 14px;
        font-weight: 400;
        color: #333333;
      }
      .text{
        width: 32px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        border-radius: 4px 4px 4px 4px;
        font-size: 12px;
        font-weight: 400;
        & + .text{
          margin-left: 5px;
        }
        &.type8{
          border: 1px solid #F78C3E;
          color: #F78C3E;
        }
        &.type2{
          border: 1px solid #22BF7E;
          color: #22BF7E;
        }
        &.type1{
          border: 1px solid #DCAD6B;
          color: #DCAD6B;
        }
        &.type7{
          border: 1px solid #6676F2;
          color: #6676F2;
        }
      }
    }
  }
  .btn-wr{
    margin-top: 40px;
    padding: 0 24px 16px;
    .btn{
      width: 126px;
      height: 44px;
      line-height: 44px;
      text-align: center;
      border-radius: 68px 68px 68px 68px;
      border: 1px solid #CCCCCC;
      font-size: 16px;
      font-weight: 400;
      color: #333333;
      &.primary{
        background: #3887F5;
        color: #FFFFFF;
        border-color: #3887F5;
        position: relative;
        .tag{
          width: 72px;
          line-height: 20px;
          background: linear-gradient(90deg, #FA700D 0%, #F34747 100%);
          border-radius: 10px 10px 10px 2px;
          position: absolute;
          right: 25px;
          top: -10px;
          font-size: 14px;
          font-weight: 400;
          color: #FFFFFF;
          text-align: center;
        }
      }
    }
  }
}
</style>
