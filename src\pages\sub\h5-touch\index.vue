<template>
  <app-popup
    :show="dialogShow"
    bgColor="transparent"
    mode="center"
  >
    <div class="block w-[311px] h-[355px] position-relative">
      <img
        alt=""
        class="background-image"
        src="@/pages/sub/h5-touch/img/Frame8731.png"
      >
      <img
        :src="qrCode"
        alt=""
        class="w-[160px] h-[160px] rounded-[12px] absolute absolute-x-center top-[137px]"
        showMenuByLongpress
        @longtap="longTap"
      >
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { getCommonConfigKey } from "@/api";

export default {
  name: "H5Touch",
  components: { AppPopup },
  data() {
    return {
      dialogShow: true,
      /** 二维码 */
      qrCode: ""
    };
  },
  created() {
    this.getQrCode();
  },
  methods: {
    /** 请求链接二维码 */
    getQrCode() {
      getCommonConfigKey({ paramName: "customer_qr_code" }).then(({ data }) => {
        this.qrCode = data.paramValue;
      });
    },
    /** 长按二维码埋点 */
    longTap() {
    }
  }
};
</script>
