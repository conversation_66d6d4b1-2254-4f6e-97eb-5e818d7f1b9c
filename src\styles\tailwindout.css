.visible {
  visibility: visible
}

.invisible {
  visibility: hidden
}

.collapse {
  visibility: collapse
}

.static {
  position: static
}

.\!fixed {
  position: fixed !important
}

.fixed {
  position: fixed
}

.absolute {
  position: absolute
}

.relative {
  position: relative
}

.sticky {
  position: -webkit-sticky;
  position: sticky
}

.\!left-\[4px\] {
  left: 4px !important
}

.\!top-\[43px\] {
  top: 43px !important
}

.-bottom-\[1px\] {
  bottom: -1px
}

.-bottom-\[60px\] {
  bottom: -60px
}

.-left-\[16px\] {
  left: -16px
}

.-right-\[1px\] {
  right: -1px
}

.-right-\[4px\] {
  right: -4px
}

.-top-\[11px\] {
  top: -11px
}

.-top-\[16px\] {
  top: -16px
}

.-top-\[19px\] {
  top: -19px
}

.-top-\[20px\] {
  top: -20px
}

.-top-\[32px\] {
  top: -32px
}

.-top-\[36px\] {
  top: -36px
}

.-top-\[9px\] {
  top: -9px
}

.bottom-0 {
  bottom: 0px
}

.bottom-\[0\] {
  bottom: 0
}

.bottom-\[12px\] {
  bottom: 12px
}

.bottom-\[16px\] {
  bottom: 16px
}

.bottom-\[24px\] {
  bottom: 24px
}

.bottom-\[3px\] {
  bottom: 3px
}

.bottom-\[66px\] {
  bottom: 66px
}

.left-0 {
  left: 0px
}

.left-\[12px\] {
  left: 12px
}

.left-\[160px\] {
  left: 160px
}

.left-\[16px\] {
  left: 16px
}

.left-\[1px\] {
  left: 1px
}

.left-\[20px\] {
  left: 20px
}

.left-\[30px\] {
  left: 30px
}

.left-\[50px\] {
  left: 50px
}

.left-\[6px\] {
  left: 6px
}

.left-\[71px\] {
  left: 71px
}

.right-0 {
  right: 0px
}

.right-\[-4px\] {
  right: -4px
}

.right-\[10px\] {
  right: 10px
}

.right-\[12px\] {
  right: 12px
}

.right-\[14px\] {
  right: 14px
}

.right-\[16px\] {
  right: 16px
}

.right-\[25px\] {
  right: 25px
}

.right-\[4px\] {
  right: 4px
}

.right-\[72px\] {
  right: 72px
}

.right-\[8px\] {
  right: 8px
}

.top-0 {
  top: 0px
}

.top-\[10px\] {
  top: 10px
}

.top-\[137px\] {
  top: 137px
}

.top-\[145px\] {
  top: 145px
}

.top-\[19px\] {
  top: 19px
}

.top-\[24px\] {
  top: 24px
}

.top-\[32px\] {
  top: 32px
}

.top-\[36px\] {
  top: 36px
}

.top-\[3px\] {
  top: 3px
}

.top-\[411px\] {
  top: 411px
}

.top-\[42px\] {
  top: 42px
}

.top-\[4px\] {
  top: 4px
}

.top-\[50px\] {
  top: 50px
}

.top-\[6px\] {
  top: 6px
}

.top-\[82px\] {
  top: 82px
}

.top-\[94px\] {
  top: 94px
}

.top-\[9px\] {
  top: 9px
}

.-z-10 {
  z-index: -10
}

.z-10 {
  z-index: 10
}

.z-20 {
  z-index: 20
}

.z-30 {
  z-index: 30
}

.z-50 {
  z-index: 50
}

.z-\[-1\] {
  z-index: -1
}

.z-\[1000\] {
  z-index: 1000
}

.order-1 {
  -webkit-box-ordinal-group: 2;
  -webkit-order: 1;
          order: 1
}

.order-2 {
  -webkit-box-ordinal-group: 3;
  -webkit-order: 2;
          order: 2
}

.order-3 {
  -webkit-box-ordinal-group: 4;
  -webkit-order: 3;
          order: 3
}

.m-1 {
  margin: 0.25rem
}

.m-2 {
  margin: 0.5rem
}

.m-36 {
  margin: 9rem
}

.m-5 {
  margin: 1.25rem
}

.mx-\[12px\] {
  margin-left: 12px;
  margin-right: 12px
}

.mx-\[14px\] {
  margin-left: 14px;
  margin-right: 14px
}

.mx-\[16px\] {
  margin-left: 16px;
  margin-right: 16px
}

.mx-\[4px\] {
  margin-left: 4px;
  margin-right: 4px
}

.mx-\[8px\] {
  margin-left: 8px;
  margin-right: 8px
}

.mx-auto {
  margin-left: auto;
  margin-right: auto
}

.my-\[10px\] {
  margin-top: 10px;
  margin-bottom: 10px
}

.my-\[12px\] {
  margin-top: 12px;
  margin-bottom: 12px
}

.my-\[16px\] {
  margin-top: 16px;
  margin-bottom: 16px
}

.my-\[8px\] {
  margin-top: 8px;
  margin-bottom: 8px
}

.\!mt-\[16px\] {
  margin-top: 16px !important
}

.-mt-\[16px\] {
  margin-top: -16px
}

.mb-\[10px\] {
  margin-bottom: 10px
}

.mb-\[12px\] {
  margin-bottom: 12px
}

.mb-\[16px\] {
  margin-bottom: 16px
}

.mb-\[20px\] {
  margin-bottom: 20px
}

.mb-\[57px\] {
  margin-bottom: 57px
}

.mb-\[8px\] {
  margin-bottom: 8px
}

.ml-\[-6px\] {
  margin-left: -6px
}

.ml-\[12px\] {
  margin-left: 12px
}

.ml-\[16px\] {
  margin-left: 16px
}

.ml-\[21px\] {
  margin-left: 21px
}

.ml-\[2px\] {
  margin-left: 2px
}

.ml-\[4px\] {
  margin-left: 4px
}

.ml-\[6px\] {
  margin-left: 6px
}

.ml-\[8px\] {
  margin-left: 8px
}

.mr-\[12px\] {
  margin-right: 12px
}

.mr-\[13px\] {
  margin-right: 13px
}

.mr-\[16px\] {
  margin-right: 16px
}

.mr-\[24px\] {
  margin-right: 24px
}

.mr-\[2px\] {
  margin-right: 2px
}

.mr-\[4px\] {
  margin-right: 4px
}

.mr-\[5px\] {
  margin-right: 5px
}

.mr-\[6px\] {
  margin-right: 6px
}

.mr-\[8px\] {
  margin-right: 8px
}

.mt-\[-16px\] {
  margin-top: -16px
}

.mt-\[-48px\] {
  margin-top: -48px
}

.mt-\[-71px\] {
  margin-top: -71px
}

.mt-\[10px\] {
  margin-top: 10px
}

.mt-\[12px\] {
  margin-top: 12px
}

.mt-\[13px\] {
  margin-top: 13px
}

.mt-\[14px\] {
  margin-top: 14px
}

.mt-\[16px\] {
  margin-top: 16px
}

.mt-\[20px\] {
  margin-top: 20px
}

.mt-\[23px\] {
  margin-top: 23px
}

.mt-\[24px\] {
  margin-top: 24px
}

.mt-\[28px\] {
  margin-top: 28px
}

.mt-\[2px\] {
  margin-top: 2px
}

.mt-\[32px\] {
  margin-top: 32px
}

.mt-\[36px\] {
  margin-top: 36px
}

.mt-\[40px\] {
  margin-top: 40px
}

.mt-\[4px\] {
  margin-top: 4px
}

.mt-\[5px\] {
  margin-top: 5px
}

.mt-\[6px\] {
  margin-top: 6px
}

.mt-\[8px\] {
  margin-top: 8px
}

.box-border {
  box-sizing: border-box
}

.block {
  display: block
}

.inline-block {
  display: inline-block
}

.inline {
  display: inline
}

.flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex
}

.table {
  display: table
}

.grid {
  display: grid
}

.list-item {
  display: list-item
}

.hidden {
  display: none
}

.\!h-\[17px\] {
  height: 17px !important
}

.\!h-\[52px\] {
  height: 52px !important
}

.h-1 {
  height: 0.25rem
}

.h-\[100px\] {
  height: 100px
}

.h-\[101px\] {
  height: 101px
}

.h-\[104px\] {
  height: 104px
}

.h-\[106px\] {
  height: 106px
}

.h-\[108px\] {
  height: 108px
}

.h-\[120px\] {
  height: 120px
}

.h-\[124px\] {
  height: 124px
}

.h-\[12px\] {
  height: 12px
}

.h-\[13px\] {
  height: 13px
}

.h-\[142px\] {
  height: 142px
}

.h-\[144px\] {
  height: 144px
}

.h-\[146px\] {
  height: 146px
}

.h-\[14px\] {
  height: 14px
}

.h-\[158px\] {
  height: 158px
}

.h-\[15px\] {
  height: 15px
}

.h-\[160px\] {
  height: 160px
}

.h-\[168px\] {
  height: 168px
}

.h-\[16px\] {
  height: 16px
}

.h-\[172px\] {
  height: 172px
}

.h-\[174px\] {
  height: 174px
}

.h-\[17px\] {
  height: 17px
}

.h-\[180px\] {
  height: 180px
}

.h-\[18px\] {
  height: 18px
}

.h-\[190px\] {
  height: 190px
}

.h-\[19px\] {
  height: 19px
}

.h-\[200px\] {
  height: 200px
}

.h-\[20px\] {
  height: 20px
}

.h-\[211px\] {
  height: 211px
}

.h-\[216px\] {
  height: 216px
}

.h-\[22px\] {
  height: 22px
}

.h-\[232px\] {
  height: 232px
}

.h-\[23px\] {
  height: 23px
}

.h-\[249px\] {
  height: 249px
}

.h-\[24px\] {
  height: 24px
}

.h-\[253px\] {
  height: 253px
}

.h-\[25px\] {
  height: 25px
}

.h-\[26px\] {
  height: 26px
}

.h-\[27px\] {
  height: 27px
}

.h-\[28px\] {
  height: 28px
}

.h-\[29px\] {
  height: 29px
}

.h-\[30px\] {
  height: 30px
}

.h-\[324px\] {
  height: 324px
}

.h-\[32px\] {
  height: 32px
}

.h-\[355px\] {
  height: 355px
}

.h-\[360px\] {
  height: 360px
}

.h-\[36px\] {
  height: 36px
}

.h-\[38px\] {
  height: 38px
}

.h-\[390px\] {
  height: 390px
}

.h-\[397px\] {
  height: 397px
}

.h-\[39px\] {
  height: 39px
}

.h-\[3px\] {
  height: 3px
}

.h-\[40px\] {
  height: 40px
}

.h-\[42px\] {
  height: 42px
}

.h-\[43px\] {
  height: 43px
}

.h-\[44px\] {
  height: 44px
}

.h-\[46px\] {
  height: 46px
}

.h-\[48px\] {
  height: 48px
}

.h-\[490px\] {
  height: 490px
}

.h-\[4px\] {
  height: 4px
}

.h-\[52px\] {
  height: 52px
}

.h-\[54px\] {
  height: 54px
}

.h-\[56px\] {
  height: 56px
}

.h-\[59px\] {
  height: 59px
}

.h-\[5px\] {
  height: 5px
}

.h-\[60px\] {
  height: 60px
}

.h-\[64px\] {
  height: 64px
}

.h-\[65px\] {
  height: 65px
}

.h-\[70px\] {
  height: 70px
}

.h-\[724px\] {
  height: 724px
}

.h-\[72px\] {
  height: 72px
}

.h-\[74px\] {
  height: 74px
}

.h-\[79px\] {
  height: 79px
}

.h-\[80px\] {
  height: 80px
}

.h-\[82px\] {
  height: 82px
}

.h-\[84px\] {
  height: 84px
}

.h-\[88px\] {
  height: 88px
}

.h-\[8px\] {
  height: 8px
}

.h-\[90px\] {
  height: 90px
}

.h-\[96px\] {
  height: 96px
}

.h-full {
  height: 100%
}

.h-screen {
  height: 100vh
}

.max-h-\[463px\] {
  max-height: 463px
}

.min-h-\[100vh\] {
  min-height: 100vh
}

.min-h-\[143px\] {
  min-height: 143px
}

.min-h-\[20vh\] {
  min-height: 20vh
}

.min-h-\[28px\] {
  min-height: 28px
}

.min-h-\[48px\] {
  min-height: 48px
}

.min-h-\[500px\] {
  min-height: 500px
}

.\!w-\[106px\] {
  width: 106px !important
}

.\!w-\[43px\] {
  width: 43px !important
}

.\!w-\[52px\] {
  width: 52px !important
}

.w-\[100px\] {
  width: 100px
}

.w-\[106px\] {
  width: 106px
}

.w-\[107px\] {
  width: 107px
}

.w-\[110px\] {
  width: 110px
}

.w-\[124px\] {
  width: 124px
}

.w-\[128px\] {
  width: 128px
}

.w-\[12px\] {
  width: 12px
}

.w-\[130px\] {
  width: 130px
}

.w-\[138px\] {
  width: 138px
}

.w-\[140px\] {
  width: 140px
}

.w-\[14px\] {
  width: 14px
}

.w-\[15px\] {
  width: 15px
}

.w-\[160px\] {
  width: 160px
}

.w-\[164px\] {
  width: 164px
}

.w-\[167px\] {
  width: 167px
}

.w-\[16px\] {
  width: 16px
}

.w-\[171px\] {
  width: 171px
}

.w-\[175px\] {
  width: 175px
}

.w-\[180px\] {
  width: 180px
}

.w-\[18px\] {
  width: 18px
}

.w-\[190px\] {
  width: 190px
}

.w-\[19px\] {
  width: 19px
}

.w-\[1px\] {
  width: 1px
}

.w-\[200px\] {
  width: 200px
}

.w-\[203px\] {
  width: 203px
}

.w-\[208px\] {
  width: 208px
}

.w-\[20px\] {
  width: 20px
}

.w-\[224px\] {
  width: 224px
}

.w-\[22px\] {
  width: 22px
}

.w-\[236px\] {
  width: 236px
}

.w-\[237px\] {
  width: 237px
}

.w-\[240px\] {
  width: 240px
}

.w-\[24px\] {
  width: 24px
}

.w-\[250px\] {
  width: 250px
}

.w-\[260px\] {
  width: 260px
}

.w-\[263px\] {
  width: 263px
}

.w-\[26px\] {
  width: 26px
}

.w-\[276px\] {
  width: 276px
}

.w-\[27px\] {
  width: 27px
}

.w-\[280px\] {
  width: 280px
}

.w-\[28px\] {
  width: 28px
}

.w-\[296px\] {
  width: 296px
}

.w-\[30px\] {
  width: 30px
}

.w-\[311px\] {
  width: 311px
}

.w-\[317px\] {
  width: 317px
}

.w-\[326px\] {
  width: 326px
}

.w-\[32px\] {
  width: 32px
}

.w-\[335px\] {
  width: 335px
}

.w-\[343px\] {
  width: 343px
}

.w-\[34px\] {
  width: 34px
}

.w-\[351px\] {
  width: 351px
}

.w-\[359px\] {
  width: 359px
}

.w-\[36px\] {
  width: 36px
}

.w-\[375px\] {
  width: 375px
}

.w-\[37px\] {
  width: 37px
}

.w-\[39px\] {
  width: 39px
}

.w-\[3px\] {
  width: 3px
}

.w-\[40px\] {
  width: 40px
}

.w-\[44px\] {
  width: 44px
}

.w-\[48px\] {
  width: 48px
}

.w-\[4px\] {
  width: 4px
}

.w-\[52px\] {
  width: 52px
}

.w-\[54px\] {
  width: 54px
}

.w-\[56px\] {
  width: 56px
}

.w-\[5px\] {
  width: 5px
}

.w-\[60px\] {
  width: 60px
}

.w-\[64px\] {
  width: 64px
}

.w-\[66px\] {
  width: 66px
}

.w-\[74px\] {
  width: 74px
}

.w-\[76px\] {
  width: 76px
}

.w-\[7px\] {
  width: 7px
}

.w-\[80px\] {
  width: 80px
}

.w-\[82px\] {
  width: 82px
}

.w-\[84px\] {
  width: 84px
}

.w-\[85px\] {
  width: 85px
}

.w-\[87px\] {
  width: 87px
}

.w-\[88px\] {
  width: 88px
}

.w-\[8px\] {
  width: 8px
}

.w-\[92px\] {
  width: 92px
}

.w-\[96px\] {
  width: 96px
}

.w-full {
  width: 100%
}

.w-screen {
  width: 100vw
}

.max-w-\[252px\] {
  max-width: 252px
}

.max-w-\[44px\] {
  max-width: 44px
}

.max-w-\[80px\] {
  max-width: 80px
}

.flex-1 {
  -webkit-box-flex: 1;
  -webkit-flex: 1 1 0%;
          flex: 1 1 0%
}

.flex-shrink {
  -webkit-flex-shrink: 1;
          flex-shrink: 1
}

.flex-shrink-0 {
  -webkit-flex-shrink: 0;
          flex-shrink: 0
}

.shrink-0 {
  -webkit-flex-shrink: 0;
          flex-shrink: 0
}

.rotate-180 {
  --tw-rotate: 180deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.transform {
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.resize {
  resize: both
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr))
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr))
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr))
}

.flex-col {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column
}

.flex-wrap {
  -webkit-flex-wrap: wrap;
          flex-wrap: wrap
}

.content-start {
  -webkit-align-content: flex-start;
          align-content: flex-start
}

.content-end {
  -webkit-align-content: flex-end;
          align-content: flex-end
}

.items-end {
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end
}

.items-center {
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center
}

.justify-end {
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end
}

.justify-center {
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center
}

.justify-between {
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
          justify-content: space-between
}

.gap-x-\[12px\] {
  -webkit-column-gap: 12px;
          column-gap: 12px
}

.gap-x-\[20px\] {
  -webkit-column-gap: 20px;
          column-gap: 20px
}

.gap-x-\[24px\] {
  -webkit-column-gap: 24px;
          column-gap: 24px
}

.gap-x-\[9px\] {
  -webkit-column-gap: 9px;
          column-gap: 9px
}

.gap-y-\[20px\] {
  row-gap: 20px
}

.gap-y-\[24px\] {
  row-gap: 24px
}

.space-x-\[16px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(16px * var(--tw-space-x-reverse));
  margin-left: calc(16px * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-\[4px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(4px * var(--tw-space-x-reverse));
  margin-left: calc(4px * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-\[8px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(8px * var(--tw-space-x-reverse));
  margin-left: calc(8px * calc(1 - var(--tw-space-x-reverse)))
}

.space-y-\[12px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(12px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(12px * var(--tw-space-y-reverse))
}

.overflow-auto {
  overflow: auto
}

.overflow-hidden {
  overflow: hidden
}

.overflow-x-auto {
  overflow-x: auto
}

.overflow-y-auto {
  overflow-y: auto
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.text-ellipsis {
  text-overflow: ellipsis
}

.whitespace-pre-line {
  white-space: pre-line
}

.break-all {
  word-break: break-all
}

.rounded-\[100\%\] {
  border-radius: 100%
}

.rounded-\[10px\] {
  border-radius: 10px
}

.rounded-\[12px\] {
  border-radius: 12px
}

.rounded-\[14px\] {
  border-radius: 14px
}

.rounded-\[15px\] {
  border-radius: 15px
}

.rounded-\[16px\] {
  border-radius: 16px
}

.rounded-\[17px\] {
  border-radius: 17px
}

.rounded-\[18px\] {
  border-radius: 18px
}

.rounded-\[20px\] {
  border-radius: 20px
}

.rounded-\[22px\] {
  border-radius: 22px
}

.rounded-\[29px\] {
  border-radius: 29px
}

.rounded-\[2px\] {
  border-radius: 2px
}

.rounded-\[31px\] {
  border-radius: 31px
}

.rounded-\[32px\] {
  border-radius: 32px
}

.rounded-\[34px\] {
  border-radius: 34px
}

.rounded-\[37px\] {
  border-radius: 37px
}

.rounded-\[3px\] {
  border-radius: 3px
}

.rounded-\[40px\] {
  border-radius: 40px
}

.rounded-\[46px\] {
  border-radius: 46px
}

.rounded-\[47px\] {
  border-radius: 47px
}

.rounded-\[49px\] {
  border-radius: 49px
}

.rounded-\[4px\] {
  border-radius: 4px
}

.rounded-\[50px\] {
  border-radius: 50px
}

.rounded-\[60px\] {
  border-radius: 60px
}

.rounded-\[68px\] {
  border-radius: 68px
}

.rounded-\[6px\] {
  border-radius: 6px
}

.rounded-\[70px\] {
  border-radius: 70px
}

.rounded-\[8px\] {
  border-radius: 8px
}

.rounded-full {
  border-radius: 9999px
}

.rounded-bl-\[19px\] {
  border-bottom-left-radius: 19px
}

.rounded-bl-\[1px\] {
  border-bottom-left-radius: 1px
}

.rounded-bl-full {
  border-bottom-left-radius: 9999px
}

.rounded-bl-none {
  border-bottom-left-radius: 0px
}

.rounded-br-\[19px\] {
  border-bottom-right-radius: 19px
}

.rounded-br-\[8px\] {
  border-bottom-right-radius: 8px
}

.rounded-br-none {
  border-bottom-right-radius: 0px
}

.rounded-tl-\[12px\] {
  border-top-left-radius: 12px
}

.rounded-tl-\[16px\] {
  border-top-left-radius: 16px
}

.rounded-tl-\[20px\] {
  border-top-left-radius: 20px
}

.rounded-tl-\[8px\] {
  border-top-left-radius: 8px
}

.rounded-tl-full {
  border-top-left-radius: 9999px
}

.rounded-tl-none {
  border-top-left-radius: 0px
}

.rounded-tr-\[12px\] {
  border-top-right-radius: 12px
}

.rounded-tr-\[16px\] {
  border-top-right-radius: 16px
}

.rounded-tr-\[20px\] {
  border-top-right-radius: 20px
}

.rounded-tr-\[8px\] {
  border-top-right-radius: 8px
}

.rounded-tr-none {
  border-top-right-radius: 0px
}

.border {
  border-width: 1px
}

.border-0 {
  border-width: 0px
}

.border-4 {
  border-width: 4px
}

.border-\[0\.5px\] {
  border-width: 0.5px
}

.border-\[1px\] {
  border-width: 1px
}

.border-\[2px\] {
  border-width: 2px
}

.border-b {
  border-bottom-width: 1px
}

.border-b-\[0\.5px\] {
  border-bottom-width: 0.5px
}

.border-b-\[1px\] {
  border-bottom-width: 1px
}

.border-r-\[1px\] {
  border-right-width: 1px
}

.border-t {
  border-top-width: 1px
}

.border-t-\[1px\] {
  border-top-width: 1px
}

.border-solid {
  border-style: solid
}

.border-dotted {
  border-style: dotted
}

.\!border-\[\#3887F5\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(56 135 245 / var(--tw-border-opacity)) !important
}

.border-\[\#07C160\] {
  --tw-border-opacity: 1;
  border-color: rgb(7 193 96 / var(--tw-border-opacity))
}

.border-\[\#3887F5\] {
  --tw-border-opacity: 1;
  border-color: rgb(56 135 245 / var(--tw-border-opacity))
}

.border-\[\#CCCCCC\] {
  --tw-border-opacity: 1;
  border-color: rgb(204 204 204 / var(--tw-border-opacity))
}

.border-\[\#DDDDDD\] {
  --tw-border-opacity: 1;
  border-color: rgb(221 221 221 / var(--tw-border-opacity))
}

.border-\[\#E3E3E3\] {
  --tw-border-opacity: 1;
  border-color: rgb(227 227 227 / var(--tw-border-opacity))
}

.border-\[\#E8EBFA\] {
  --tw-border-opacity: 1;
  border-color: rgb(232 235 250 / var(--tw-border-opacity))
}

.border-\[\#EEEEEE\] {
  --tw-border-opacity: 1;
  border-color: rgb(238 238 238 / var(--tw-border-opacity))
}

.border-\[\#F78C3E\] {
  --tw-border-opacity: 1;
  border-color: rgb(247 140 62 / var(--tw-border-opacity))
}

.border-\[\#FFECC8\] {
  --tw-border-opacity: 1;
  border-color: rgb(255 236 200 / var(--tw-border-opacity))
}

.border-\[\#FFFFFF\] {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity))
}

.border-\[rgba\(189\2c 147\2c 76\2c 0\.3\)\] {
  border-color: rgba(189,147,76,0.3)
}

.border-\[rgba\(235\2c 71\2c 56\2c 0\.1\)\] {
  border-color: rgba(235,71,56,0.1)
}

.border-\[rgba\(34\2c 191\2c 126\2c 0\.2\)\] {
  border-color: rgba(34,191,126,0.2)
}

.\!bg-\[\#3887F5\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(56 135 245 / var(--tw-bg-opacity)) !important
}

.\!bg-\[\#EBF3FE\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(235 243 254 / var(--tw-bg-opacity)) !important
}

.\!bg-\[\#F5883B_ifhjls\] {
  background-color: #F5883B ifhjls !important
}

.bg-\[\#07C160\] {
  --tw-bg-opacity: 1;
  background-color: rgb(7 193 96 / var(--tw-bg-opacity))
}

.bg-\[\#22BF7E\] {
  --tw-bg-opacity: 1;
  background-color: rgb(34 191 126 / var(--tw-bg-opacity))
}

.bg-\[\#3887F5\] {
  --tw-bg-opacity: 1;
  background-color: rgb(56 135 245 / var(--tw-bg-opacity))
}

.bg-\[\#3A4965\] {
  --tw-bg-opacity: 1;
  background-color: rgb(58 73 101 / var(--tw-bg-opacity))
}

.bg-\[\#C4C4C4\] {
  --tw-bg-opacity: 1;
  background-color: rgb(196 196 196 / var(--tw-bg-opacity))
}

.bg-\[\#D9D9D9\] {
  --tw-bg-opacity: 1;
  background-color: rgb(217 217 217 / var(--tw-bg-opacity))
}

.bg-\[\#EB4738\] {
  --tw-bg-opacity: 1;
  background-color: rgb(235 71 56 / var(--tw-bg-opacity))
}

.bg-\[\#EBF3FE\] {
  --tw-bg-opacity: 1;
  background-color: rgb(235 243 254 / var(--tw-bg-opacity))
}

.bg-\[\#EBF7F0\] {
  --tw-bg-opacity: 1;
  background-color: rgb(235 247 240 / var(--tw-bg-opacity))
}

.bg-\[\#ECECF0\] {
  --tw-bg-opacity: 1;
  background-color: rgb(236 236 240 / var(--tw-bg-opacity))
}

.bg-\[\#EEEEEE\] {
  --tw-bg-opacity: 1;
  background-color: rgb(238 238 238 / var(--tw-bg-opacity))
}

.bg-\[\#F34747\] {
  --tw-bg-opacity: 1;
  background-color: rgb(243 71 71 / var(--tw-bg-opacity))
}

.bg-\[\#F4F9FF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(244 249 255 / var(--tw-bg-opacity))
}

.bg-\[\#F5F5F7\] {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 247 / var(--tw-bg-opacity))
}

.bg-\[\#F78C3E\] {
  --tw-bg-opacity: 1;
  background-color: rgb(247 140 62 / var(--tw-bg-opacity))
}

.bg-\[\#F7F6F6\] {
  --tw-bg-opacity: 1;
  background-color: rgb(247 246 246 / var(--tw-bg-opacity))
}

.bg-\[\#F9FAFF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 255 / var(--tw-bg-opacity))
}

.bg-\[\#FAF2E8\] {
  --tw-bg-opacity: 1;
  background-color: rgb(250 242 232 / var(--tw-bg-opacity))
}

.bg-\[\#FCF1ED\] {
  --tw-bg-opacity: 1;
  background-color: rgb(252 241 237 / var(--tw-bg-opacity))
}

.bg-\[\#FDF1ED\] {
  --tw-bg-opacity: 1;
  background-color: rgb(253 241 237 / var(--tw-bg-opacity))
}

.bg-\[\#FFF3E0\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 243 224 / var(--tw-bg-opacity))
}

.bg-\[\#FFF8EC\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 248 236 / var(--tw-bg-opacity))
}

.bg-\[\#FFFAF0\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 250 240 / var(--tw-bg-opacity))
}

.bg-\[\#FFFFFF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity))
}

.bg-\[\#f5f5f7\] {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 247 / var(--tw-bg-opacity))
}

.bg-\[rgba\(0\2c 0\2c 0\2c 0\.6\)\] {
  background-color: rgba(0,0,0,0.6)
}

.bg-\[rgba\(0\2c 0\2c 0\2c 0\.75\)\] {
  background-color: rgba(0,0,0,0.75)
}

.bg-\[rgba\(235\2c 71\2c 56\2c 0\.05\)\] {
  background-color: rgba(235,71,56,0.05)
}

.bg-\[rgba\(255\2c 255\2c 255\2c 0\.9\)\] {
  background-color: rgba(255,255,255,0.9)
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity))
}

.bg-\[linear-gradient\(180deg\2c _\#E5EFFF_0\%\2c _rgba\(229\2c 239\2c 255\2c 0\)_100\%\)\] {
  background-image: -webkit-linear-gradient(top, #E5EFFF 0%, rgba(229,239,255,0) 100%);
  background-image: linear-gradient(180deg, #E5EFFF 0%, rgba(229,239,255,0) 100%)
}

.bg-\[linear-gradient\(_109deg\2c _\#FFB759_0\%\2c _\#FF7721_100\%\)\] {
  background-image: -webkit-linear-gradient( 341deg, #FFB759 0%, #FF7721 100%);
  background-image: linear-gradient( 109deg, #FFB759 0%, #FF7721 100%)
}

.bg-\[linear-gradient\(_116deg\2c _\#71B5FF_0\%\2c _\#2676E4_100\%\)\] {
  background-image: -webkit-linear-gradient( 334deg, #71B5FF 0%, #2676E4 100%);
  background-image: linear-gradient( 116deg, #71B5FF 0%, #2676E4 100%)
}

.bg-\[linear-gradient\(_133deg\2c _\#769CFF_0\%\2c _\#4377FF_100\%\)\] {
  background-image: -webkit-linear-gradient( 317deg, #769CFF 0%, #4377FF 100%);
  background-image: linear-gradient( 133deg, #769CFF 0%, #4377FF 100%)
}

.bg-\[linear-gradient\(_147deg\2c _\#FFFFFF_0\%\2c _\#FFF9F6_100\%\)\] {
  background-image: -webkit-linear-gradient( 303deg, #FFFFFF 0%, #FFF9F6 100%);
  background-image: linear-gradient( 147deg, #FFFFFF 0%, #FFF9F6 100%)
}

.bg-\[linear-gradient\(_151deg\2c _\#FFFFFF_0\%\2c _\#F4F9FF_100\%\)\] {
  background-image: -webkit-linear-gradient( 299deg, #FFFFFF 0%, #F4F9FF 100%);
  background-image: linear-gradient( 151deg, #FFFFFF 0%, #F4F9FF 100%)
}

.bg-\[linear-gradient\(_180deg\2c _\#EBF3FF_0\%\2c _\#E5EFFF_100\%\)\] {
  background-image: -webkit-linear-gradient( top, #EBF3FF 0%, #E5EFFF 100%);
  background-image: linear-gradient( 180deg, #EBF3FF 0%, #E5EFFF 100%)
}

.bg-\[linear-gradient\(_270deg\2c _\#FFECE5_0\%\2c _\#FEFBFA_36\%\2c _\#FFFFFF_100\%\)\] {
  background-image: -webkit-linear-gradient( right, #FFECE5 0%, #FEFBFA 36%, #FFFFFF 100%);
  background-image: linear-gradient( 270deg, #FFECE5 0%, #FEFBFA 36%, #FFFFFF 100%)
}

.bg-\[linear-gradient\(_90deg\2c _\#79D9A6_0\%\2c _\#57C398_100\%\)\] {
  background-image: -webkit-linear-gradient( left, #79D9A6 0%, #57C398 100%);
  background-image: linear-gradient( 90deg, #79D9A6 0%, #57C398 100%)
}

.bg-\[linear-gradient\(_90deg\2c _\#FA700D_0\%\2c _\#F34747_100\%\)\] {
  background-image: -webkit-linear-gradient( left, #FA700D 0%, #F34747 100%);
  background-image: linear-gradient( 90deg, #FA700D 0%, #F34747 100%)
}

.bg-\[linear-gradient\(_90deg\2c _\#FE6128_0\%\2c _\#FF8213_100\%\)\] {
  background-image: -webkit-linear-gradient( left, #FE6128 0%, #FF8213 100%);
  background-image: linear-gradient( 90deg, #FE6128 0%, #FF8213 100%)
}

.bg-\[linear-gradient\(_90deg\2c _rgba\(255\2c 255\2c 255\2c 0\.2\)_0\%\2c _\#FFFFFF_30\%\)\] {
  background-image: -webkit-linear-gradient( left, rgba(255,255,255,0.2) 0%, #FFFFFF 30%);
  background-image: linear-gradient( 90deg, rgba(255,255,255,0.2) 0%, #FFFFFF 30%)
}

.bg-\[linear-gradient\(_91deg\2c _\#EBF1FF_0\%\2c _\#DDEDFF_100\%\)\] {
  background-image: -webkit-linear-gradient( 359deg, #EBF1FF 0%, #DDEDFF 100%);
  background-image: linear-gradient( 91deg, #EBF1FF 0%, #DDEDFF 100%)
}

.p-1 {
  padding: 0.25rem
}

.p-\[10px\] {
  padding: 10px
}

.p-\[12px\] {
  padding: 12px
}

.p-\[12px_8px_12px_16px\] {
  padding: 12px 8px 12px 16px
}

.p-\[16px\] {
  padding: 16px
}

.p-\[16px_16px_32px_16px\] {
  padding: 16px 16px 32px 16px
}

.p-\[24px\] {
  padding: 24px
}

.p-\[32px_16px_16px_16px\] {
  padding: 32px 16px 16px 16px
}

.p-\[8px\] {
  padding: 8px
}

.px-\[10px\] {
  padding-left: 10px;
  padding-right: 10px
}

.px-\[12px\] {
  padding-left: 12px;
  padding-right: 12px
}

.px-\[13px\] {
  padding-left: 13px;
  padding-right: 13px
}

.px-\[16px\] {
  padding-left: 16px;
  padding-right: 16px
}

.px-\[19px\] {
  padding-left: 19px;
  padding-right: 19px
}

.px-\[24px\] {
  padding-left: 24px;
  padding-right: 24px
}

.px-\[26px\] {
  padding-left: 26px;
  padding-right: 26px
}

.px-\[28px\] {
  padding-left: 28px;
  padding-right: 28px
}

.px-\[4px\] {
  padding-left: 4px;
  padding-right: 4px
}

.px-\[5px\] {
  padding-left: 5px;
  padding-right: 5px
}

.px-\[6px\] {
  padding-left: 6px;
  padding-right: 6px
}

.px-\[8px\] {
  padding-left: 8px;
  padding-right: 8px
}

.py-\[10px\] {
  padding-top: 10px;
  padding-bottom: 10px
}

.py-\[12px\] {
  padding-top: 12px;
  padding-bottom: 12px
}

.py-\[14px\] {
  padding-top: 14px;
  padding-bottom: 14px
}

.py-\[16px\] {
  padding-top: 16px;
  padding-bottom: 16px
}

.py-\[20px\] {
  padding-top: 20px;
  padding-bottom: 20px
}

.py-\[24px\] {
  padding-top: 24px;
  padding-bottom: 24px
}

.py-\[2px\] {
  padding-top: 2px;
  padding-bottom: 2px
}

.py-\[3px\] {
  padding-top: 3px;
  padding-bottom: 3px
}

.py-\[4px\] {
  padding-top: 4px;
  padding-bottom: 4px
}

.py-\[5px\] {
  padding-top: 5px;
  padding-bottom: 5px
}

.py-\[6px\] {
  padding-top: 6px;
  padding-bottom: 6px
}

.py-\[8px\] {
  padding-top: 8px;
  padding-bottom: 8px
}

.\!pt-0 {
  padding-top: 0px !important
}

.pb-\[10px\] {
  padding-bottom: 10px
}

.pb-\[12px\] {
  padding-bottom: 12px
}

.pb-\[16px\] {
  padding-bottom: 16px
}

.pb-\[17px\] {
  padding-bottom: 17px
}

.pb-\[20px\] {
  padding-bottom: 20px
}

.pb-\[24px\] {
  padding-bottom: 24px
}

.pb-\[26px\] {
  padding-bottom: 26px
}

.pb-\[30px\] {
  padding-bottom: 30px
}

.pb-\[32px\] {
  padding-bottom: 32px
}

.pb-\[40px\] {
  padding-bottom: 40px
}

.pb-\[5px\] {
  padding-bottom: 5px
}

.pb-\[8px\] {
  padding-bottom: 8px
}

.pl-\[10px\] {
  padding-left: 10px
}

.pl-\[12px\] {
  padding-left: 12px
}

.pl-\[16px\] {
  padding-left: 16px
}

.pl-\[19px\] {
  padding-left: 19px
}

.pl-\[20px\] {
  padding-left: 20px
}

.pl-\[24px\] {
  padding-left: 24px
}

.pl-\[2px\] {
  padding-left: 2px
}

.pl-\[4px\] {
  padding-left: 4px
}

.pl-\[64px\] {
  padding-left: 64px
}

.pl-\[6px\] {
  padding-left: 6px
}

.pl-\[80px\] {
  padding-left: 80px
}

.pl-\[8px\] {
  padding-left: 8px
}

.pr-\[16px\] {
  padding-right: 16px
}

.pr-\[3px\] {
  padding-right: 3px
}

.pr-\[4px\] {
  padding-right: 4px
}

.pr-\[7px\] {
  padding-right: 7px
}

.pr-\[8px\] {
  padding-right: 8px
}

.pt-\[10px\] {
  padding-top: 10px
}

.pt-\[114px\] {
  padding-top: 114px
}

.pt-\[12px\] {
  padding-top: 12px
}

.pt-\[16px\] {
  padding-top: 16px
}

.pt-\[20px\] {
  padding-top: 20px
}

.pt-\[24px\] {
  padding-top: 24px
}

.pt-\[29px\] {
  padding-top: 29px
}

.pt-\[31px\] {
  padding-top: 31px
}

.pt-\[36px\] {
  padding-top: 36px
}

.pt-\[46px\] {
  padding-top: 46px
}

.pt-\[48px\] {
  padding-top: 48px
}

.pt-\[4px\] {
  padding-top: 4px
}

.pt-\[53px\] {
  padding-top: 53px
}

.pt-\[5px\] {
  padding-top: 5px
}

.pt-\[61px\] {
  padding-top: 61px
}

.pt-\[62px\] {
  padding-top: 62px
}

.pt-\[8px\] {
  padding-top: 8px
}

.text-center {
  text-align: center
}

.\!text-\[13px\] {
  font-size: 13px !important
}

.\!text-\[15px\] {
  font-size: 15px !important
}

.\!text-\[16px\] {
  font-size: 16px !important
}

.\!text-\[24px\] {
  font-size: 24px !important
}

.text-\[10px\] {
  font-size: 10px
}

.text-\[11px\] {
  font-size: 11px
}

.text-\[12px\] {
  font-size: 12px
}

.text-\[13px\] {
  font-size: 13px
}

.text-\[14px\] {
  font-size: 14px
}

.text-\[15px\] {
  font-size: 15px
}

.text-\[16px\] {
  font-size: 16px
}

.text-\[18px\] {
  font-size: 18px
}

.text-\[20px\] {
  font-size: 20px
}

.text-\[22px\] {
  font-size: 22px
}

.text-\[24px\] {
  font-size: 24px
}

.text-\[32px\] {
  font-size: 32px
}

.font-\[DIN_Alternate\2c _DIN_Alternate\] {
  font-weight: DIN Alternate, DIN Alternate
}

.font-bold {
  font-weight: 700
}

.font-normal {
  font-weight: 400
}

.italic {
  font-style: italic
}

.ordinal {
  --tw-ordinal: ordinal;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction)
}

.leading-\[20px\] {
  line-height: 20px
}

.leading-\[32px\] {
  line-height: 32px
}

.leading-\[44px\] {
  line-height: 44px
}

.\!text-\[\#3887F5\] {
  --tw-text-opacity: 1 !important;
  color: rgb(56 135 245 / var(--tw-text-opacity)) !important
}

.\!text-\[\#666666\] {
  --tw-text-opacity: 1 !important;
  color: rgb(102 102 102 / var(--tw-text-opacity)) !important
}

.\!text-\[\#FFFFFF\] {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important
}

.text-\[\#000000\] {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity))
}

.text-\[\#111111\] {
  --tw-text-opacity: 1;
  color: rgb(17 17 17 / var(--tw-text-opacity))
}

.text-\[\#1D3C5B\] {
  --tw-text-opacity: 1;
  color: rgb(29 60 91 / var(--tw-text-opacity))
}

.text-\[\#1D7FE1\] {
  --tw-text-opacity: 1;
  color: rgb(29 127 225 / var(--tw-text-opacity))
}

.text-\[\#222222\] {
  --tw-text-opacity: 1;
  color: rgb(34 34 34 / var(--tw-text-opacity))
}

.text-\[\#22BF7E\] {
  --tw-text-opacity: 1;
  color: rgb(34 191 126 / var(--tw-text-opacity))
}

.text-\[\#2E5271\] {
  --tw-text-opacity: 1;
  color: rgb(46 82 113 / var(--tw-text-opacity))
}

.text-\[\#323233\] {
  --tw-text-opacity: 1;
  color: rgb(50 50 51 / var(--tw-text-opacity))
}

.text-\[\#333333\] {
  --tw-text-opacity: 1;
  color: rgb(51 51 51 / var(--tw-text-opacity))
}

.text-\[\#3887F5\] {
  --tw-text-opacity: 1;
  color: rgb(56 135 245 / var(--tw-text-opacity))
}

.text-\[\#444444\] {
  --tw-text-opacity: 1;
  color: rgb(68 68 68 / var(--tw-text-opacity))
}

.text-\[\#666666\] {
  --tw-text-opacity: 1;
  color: rgb(102 102 102 / var(--tw-text-opacity))
}

.text-\[\#7D1E2A\] {
  --tw-text-opacity: 1;
  color: rgb(125 30 42 / var(--tw-text-opacity))
}

.text-\[\#8791A3\] {
  --tw-text-opacity: 1;
  color: rgb(135 145 163 / var(--tw-text-opacity))
}

.text-\[\#8A8D94\] {
  --tw-text-opacity: 1;
  color: rgb(138 141 148 / var(--tw-text-opacity))
}

.text-\[\#8F5E64\] {
  --tw-text-opacity: 1;
  color: rgb(143 94 100 / var(--tw-text-opacity))
}

.text-\[\#999999\] {
  --tw-text-opacity: 1;
  color: rgb(153 153 153 / var(--tw-text-opacity))
}

.text-\[\#9F6310\] {
  --tw-text-opacity: 1;
  color: rgb(159 99 16 / var(--tw-text-opacity))
}

.text-\[\#BD934C\] {
  --tw-text-opacity: 1;
  color: rgb(189 147 76 / var(--tw-text-opacity))
}

.text-\[\#CCCCCC\] {
  --tw-text-opacity: 1;
  color: rgb(204 204 204 / var(--tw-text-opacity))
}

.text-\[\#D36D64\] {
  --tw-text-opacity: 1;
  color: rgb(211 109 100 / var(--tw-text-opacity))
}

.text-\[\#D7D7D7\] {
  --tw-text-opacity: 1;
  color: rgb(215 215 215 / var(--tw-text-opacity))
}

.text-\[\#E7455B\] {
  --tw-text-opacity: 1;
  color: rgb(231 69 91 / var(--tw-text-opacity))
}

.text-\[\#EB4738\] {
  --tw-text-opacity: 1;
  color: rgb(235 71 56 / var(--tw-text-opacity))
}

.text-\[\#EEEEEE\] {
  --tw-text-opacity: 1;
  color: rgb(238 238 238 / var(--tw-text-opacity))
}

.text-\[\#F2AF30\] {
  --tw-text-opacity: 1;
  color: rgb(242 175 48 / var(--tw-text-opacity))
}

.text-\[\#F34747\] {
  --tw-text-opacity: 1;
  color: rgb(243 71 71 / var(--tw-text-opacity))
}

.text-\[\#F44945\] {
  --tw-text-opacity: 1;
  color: rgb(244 73 69 / var(--tw-text-opacity))
}

.text-\[\#F5E6BC\] {
  --tw-text-opacity: 1;
  color: rgb(245 230 188 / var(--tw-text-opacity))
}

.text-\[\#F6513B\] {
  --tw-text-opacity: 1;
  color: rgb(246 81 59 / var(--tw-text-opacity))
}

.text-\[\#F78C3E\] {
  --tw-text-opacity: 1;
  color: rgb(247 140 62 / var(--tw-text-opacity))
}

.text-\[\#F9FBE8\] {
  --tw-text-opacity: 1;
  color: rgb(249 251 232 / var(--tw-text-opacity))
}

.text-\[\#FA700D\] {
  --tw-text-opacity: 1;
  color: rgb(250 112 13 / var(--tw-text-opacity))
}

.text-\[\#FF7E28\] {
  --tw-text-opacity: 1;
  color: rgb(255 126 40 / var(--tw-text-opacity))
}

.text-\[\#FF8A36\] {
  --tw-text-opacity: 1;
  color: rgb(255 138 54 / var(--tw-text-opacity))
}

.text-\[\#FFFFFF\] {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity))
}

.underline {
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline
}

.line-through {
  -webkit-text-decoration-line: line-through;
          text-decoration-line: line-through
}

.opacity-100 {
  opacity: 1
}

.opacity-\[0\.6\] {
  opacity: 0.6
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.outline {
  outline-style: solid
}

.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.blur {
  --tw-blur: blur(8px);
  -webkit-filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
          filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.filter {
  -webkit-filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
          filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.transition {
  -webkit-transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, -webkit-text-decoration-color, -webkit-transform, -webkit-filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, -webkit-text-decoration-color, -webkit-transform, -webkit-filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-transform, -webkit-filter, -webkit-backdrop-filter;
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-transition-duration: 150ms;
          transition-duration: 150ms
}

.ease-in-out {
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1)
}

.ease-out {
  -webkit-transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
          transition-timing-function: cubic-bezier(0, 0, 0.2, 1)
}

.\[box-shadow\:0px_4px_6px_0px_rgba\(203\2c 220\2c 238\2c 0\.6\)\] {
  box-shadow: 0px 4px 6px 0px rgba(203,220,238,0.6)
}

.\[box-shadow\:0px_4px_8px_0px_rgba\(56\2c 135\2c 245\2c 0\.3\)\] {
  box-shadow: 0px 4px 8px 0px rgba(56,135,245,0.3)
}

.\[box-shadow\:0px_4px_8px_0px_rgba\(59\2c 139\2c 246\2c 0\.2\)\] {
  box-shadow: 0px 4px 8px 0px rgba(59,139,246,0.2)
}

.\[text-shadow\:0px_0px_6px_rgba\(229\2c 47\2c 76\2c 0\.6\)\] {
  text-shadow: 0px 0px 6px rgba(229,47,76,0.6)
}

.\[text-shadow\:0px_1px_6px_\#2969C0\] {
  text-shadow: 0px 1px 6px #2969C0
}

.\[text-shadow\:0px_2px_2px_\#FDBBA2\] {
  text-shadow: 0px 2px 2px #FDBBA2
}

.\[word-wrap\:break-word\] {
  word-wrap: break-word
}

.first\:pt-0:first-child {
  padding-top: 0px
}

.last\:mb-0:last-child {
  margin-bottom: 0px
}

.last\:border-b-0:last-child {
  border-bottom-width: 0px
}

.last\:pb-0:last-child {
  padding-bottom: 0px
}

.first-of-type\:ml-\[0\]:first-of-type {
  margin-left: 0
}

.last-of-type\:mr-\[0\]:last-of-type {
  margin-right: 0
}

.\[\&\:not\(\:first-child\)\]\:mt-\[16px\]:not(:first-child) {
  margin-top: 16px
}

.\[\&\:not\(\:last-child\)\]\:mb-\[12px\]:not(:last-child) {
  margin-bottom: 12px
}

.\[\&\:not\(\:last-child\)\]\:mr-\[8px\]:not(:last-child) {
  margin-right: 8px
}

.\[\&\:not\(\:last-child\)\]\:border-b-\[0\.5px\]:not(:last-child) {
  border-bottom-width: 0.5px
}

.\[\&\:not\(\:last-child\)\]\:border-r-\[1px\]:not(:last-child) {
  border-right-width: 1px
}
