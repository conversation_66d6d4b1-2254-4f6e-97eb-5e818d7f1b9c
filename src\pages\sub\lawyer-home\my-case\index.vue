<template>
  <div>
    <div
      class="w-[375px] h-[232px] bg-[linear-gradient(_180deg,_#EBF3FF_0%,_#E5EFFF_100%)] p-[16px_16px_32px_16px] box-border"
    >
      <div class="flex items-center">
        <img
          alt=""
          class="w-[22px] block"
          mode="widthFix"
          src="img/Frame1321315587.png"
        >
        <div class="ml-[8px] font-bold text-[22px] text-[#333333]">
          {{ caseSourceData.typeLabel }}
        </div>
      </div>
      <div class="mt-[24px] h-[88px] text-[16px] text-[#333333]">
        {{ caseSourceData.info }}
      </div>
      <div class="mt-[24px] text-[12px] text-[#8791A3]">
        {{ caseSourceData.createTime }}
      </div>
    </div>
    <div
      class="w-[375px] relative -mt-[16px] bg-[#FFFFFF] rounded-tl-[16px] rounded-br-none rounded-tr-[16px] rounded-bl-none pt-[36px]"
    >
      <img
        alt=""
        class="absolute left-[16px] top-0 block w-[22px]"
        mode="widthFix"
        src="img/Frame1321315588.png"
      >
      <img
        alt=""
        class="w-[190px] h-[190px] block mx-auto"
        src="img/00000_iSpt.png"
      >
      <div class="text-[13px] text-[#999999] w-[276px] mx-auto">
        目前已有多位律师查看您的问题，正在为您匹配最擅长此案情类型的律师接单，请耐心等候.....
      </div>
    </div>
    <div
      v-if="timeIsNight"
      class="fixed left-0 bottom-0"
    >
      <div
        class="w-[375px] bg-[#FAF2E8] flex items-center py-[8px] px-[16px] text-[13px] text-[#F78C3E] box-border"
      >
        {{ text }}
      </div>
      <u-safe-bottom />
    </div>
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { userCaseSourceDetail } from "@/api/my-consultation-new";
import nightTime from "@/mixins/nightTime";

export default {
  name: "MyCase",
  components: { USafeBottom },
  mixins: [nightTime],
  data() {
    return {
      caseSourceData: {},
    };
  },
  onLoad(query) {
    const { id } = query;

    userCaseSourceDetail(id).then((res) => {
      this.caseSourceData = res.data;
    });
  },
  computed: {
    text() {
      return this.timeIsNight
        ? "当前时段律师平均接单时间较长，当前时段律师平均接单时间较长，接单时间为3分钟，如仍未接单，可换时段咨询。"
        : "目前已有多位律师查看您的问题，正在为您匹配最擅长此案情类型的律师接单，请耐心等候.....";
    },
  },
};
</script>

<style>
page {
  background: #fff;
}
</style>
