<template>
  <div>
    <AppPopup
      :closeOnClickOverlay="closeOnClickOverlay"
      :duration="0"
      :safeAreaInsetBottom="false"
      :show="show"
      :zIndex="99997"
      mode="bottom"
      @close="close"
      @open="open"
    >
      <div class="login-wrapper">
        <img
          alt=""
          class="login-wrapper-ahead"
          src="@/components/login/imgs/login-ahead.png"
        >
        <div class="text-wr">
          <img
            alt=""
            class="textimg"
            src="./imgs/logo.png"
          >
          <div class="text-title">
            <div class="m">
              欢迎登录
            </div>
            <div class="s">
              未注册手机号验证后自动创建法临账号
            </div>
          </div>
        </div>
        <div class="btn-wr">
          <!-- ! 测试环境下才显示 -->
          <div
            v-if="isTest || isProductTest"
          >
            <input
              v-model="phoneNumber"
              :maxlength="11"
              placeholder="请输入手机号"
              type="text"
            >
            <div class="mg-tp-4">
              <button
                v-if="isProductTest"
                @click="btnHandleCodeClick"
              >
                获取验证码
              </button>
            </div>
            <div class="mg-tp-4">
              <input
                v-if="isProductTest"
                v-model="phoneNumberCode"
                :maxlength="4"
                placeholder="请输入验证码"
                type="text"
              >
            </div>
            <div class="mg-tp-4">
              <button @click="btnHandleClick">
                登陆
              </button>
            </div>
          </div>
          <button
            class="btn"
            open-type="getPhoneNumber"
            @getphonenumber="getPhoneNumberHandler"
          >
            一键登录
          </button>
          <div class="protocol flex flex-align-center">
            <img
              v-if="check"
              alt=""
              class="check-img"
              src="./imgs/check-active.png"
              @click="check = false"
            >
            <img
              v-else
              alt=""
              class="check-img"
              src="./imgs/check.png"
              @click="check = true"
            >
            <text @click="check = !check">
              我已阅读并同意
            </text>
            <text
              class="color-text"
              @click="protocolClick(1)"
            >
              《隐私政策》
            </text>
            <text
              class="color-text"
              @click="protocolClick(2)"
            >
              《用户服务协议》
            </text>
          </div>
        </div>
        <!--        <div-->
        <!--          v-if="!check"-->
        <!--          class="l-mask"-->
        <!--          @click="protocolDialogClick"-->
        <!--        />-->
      </div>
    </AppPopup>
    <AppPopup
      :safeAreaInsetBottom="false"
      :show="protocolDialog"
      :zIndex="99998"
      mode="center"
    >
      <div class="protocol-dialog">
        <div class="flex-1 top">
          <div class="protocol-dialog-title">
            温馨提示
          </div>
          <div class="protocol-dialog-info">
            为更好地保障您的合法权益，请您阅读并同意
            <text
              class="color-text"
              @click="protocolClick(1)"
            >
              《隐私政策》
            </text>
            <text
              class="color-text"
              @click="protocolClick(2)"
            >
              《用户服务协议》
            </text>
          </div>
        </div>
        <div class="protocol-dialog-button">
          <div
            class="protocol-dialog-btn"
            @click="protocolDialog = false"
          >
            拒绝
          </div>
          <button
            class="protocol-dialog-btn"
            open-type="getPhoneNumber"
            @click="btnHandleClick(true)"
            @getphonenumber="getPhoneNumberHandler"
          >
            同意
          </button>
        </div>
      </div>
    </AppPopup>
    <protocol-pop-up
      v-model="protocolModal"
      :protocolTitle="protocolTitle"
      :protocolContent="protocolContent"
    />
  </div>
</template>

<script>
import LoginMixin from "@/mixins/login.js";
import { protocolA, protocolB } from "./js/protocol";
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { checkAppLoginAndGetCode } from "@/libs/token";
import ProtocolPopUp from "@/components/protocolPopUp/index.vue";

export default {
  components: { ProtocolPopUp,  AppPopup },
  mixins: [LoginMixin],
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    needTransformationPath: {
      type: [Boolean, String],
      default: false,
    },
    /** 点击遮罩是否关闭弹窗 */
    closeOnClickOverlay: {
      type: Boolean,
      default: true,
    },
    /** 是否显示安全区域 */
    showSafe: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      check: false,
      protocolDialog: false,
      protocolModal: false,
      protocolTitle: "",
      protocolContent: "",
      serverShow: false,
      //  {popupType: 5, type: 3, ext: {caseSourceV2Id: 21821}}
      loginServerData: {},
      /** 手机号 */
      phoneNumber: "",
      /** 验证码 */
      phoneNumberCode: "",
    };
  },
  computed: {
    isLogin() {
      return !!this.$store.state.user.token;
    },
    isTest() {
      return (
        process.env.VUE_APP_ENV_TEST === "true"
      );
    },
    isProductTest() {
      return (
        process.env.VUE_APP_ENV_PRODUCT_TEST === "true"
      );
    }
  },
  watch: {
    show(val) {
      if (!val) {
        this.check = false;
        this.protocolDialog = false;
        this.protocolModal = false;
      }
    },
  },
  methods: {
    checkAppLoginAndGetCode,
    // 登录成功的回调，LoginMixin
    loginSuccess() {
      this.close();
      this.$store.getters["user/getLoginCallback"] &&
        this.$store.getters["user/getLoginCallback"]();
    },
    btnHandleCodeClick(){
      this.handleCodeClick({
        phone: this.phoneNumber,
      });
    },
    btnHandleClick(flag) {
      this.check = flag;
      // #ifdef MP-TOUTIAO || MP-WEIXIN
      this.protocolDialog = false;
      // #endif

      // 方法在 LoginMixin 中
      this.handleClick({
        phone: this.phoneNumber,
        code: this.phoneNumberCode || "9999"
      });
    },
    close() {
      this.protocolDialog = false;
      this.protocolModal = false;
      this.check = false;
      this.$store.commit("popupState/SET_LOGIN_POPUP_STATE", false);
    },
    open() {
      // 获取授权 code
      checkAppLoginAndGetCode();
    },
    protocolClick(type) {
      this.protocolTitle = type === 1 ? "法临隐私政策" : "用户服务协议";
      this.protocolModal = true;
      this.protocolContent = type === 1 ? protocolA : protocolB;
    },
    // 弹出协议提示
    protocolDialogClick() {
      this.protocolDialog = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.color-text {
  color: #3887f5;
}

.login-wrapper {
  width: 375px;
  background: #ffffff;
  border-radius: 16px 16px 0 0;
  position: relative;
  box-sizing: border-box;
  padding: 40px 0;

  &-ahead {
    width: 375px;
    height: 90px;
    position: absolute;
    top: -78px;
    left: 0;
    display: block;
    z-index: -1;
  }

  .text-wr {
    .textimg {
      display: block;
      margin: 0 auto;
      width: 48px;
      height: 48px;
    }

    .text-title {
      text-align: center;

      .m {
        font-size: 18px;
        font-weight: bold;
        color: #333333;
        margin-top: 12px;
      }

      .s {
        font-size: 12px;
        font-weight: 400;
        color: #999999;
        margin-top: 4px;
      }
    }
  }

  .btn-wr {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-top: 32px;
    width: 100%;
    //padding-bottom: 50px;
    .btn {
      width: 311px;
      height: 44px;
      background: #3887f5;
      border-radius: 22px 22px 22px 22px;
      opacity: 0.99;
      color: #fff;
      line-height: 44px;
      text-align: center;
    }

    .protocol {
      margin-top: 24px;
      font-size: 12px;
      font-weight: 400;
      color: #999999;

      .check-img {
        display: block;
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
    }
  }

  .l-mask {
    position: absolute;
    width: 375px;
    height: 64px;
    left: 0;
    top: 56px;
  }
}

.protocol-dialog {
  width: 311px;
  height: 174px;
  background: #ffffff;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .top {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  &-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    text-align: center;
  }

  &-info {
    text-align: center;
    font-size: 14px;
    color: #333;
    margin-top: 12px;
    padding: 0 24px;
  }

  &-button {
    height: 60px;
    display: flex;
    justify-content: center;
    font-size: 14px;
    padding-top: 8px;

    .protocol-dialog-btn {
      text-align: center;
      width: 126px;
      height: 36px;
      line-height: 36px;
      border-radius: 68px 68px 68px 68px;

      &:nth-child(1) {
        color: #333;
        margin-right: 12px;
        border: 1px solid #cccccc;
      }

      &:nth-child(2) {
        color: #fff;
        background: #ccc;
        font-size: 14px;
      }
    }
  }
}
</style>
