<template>
  <div>
    <div class="flex">
      <div class="position-relative flex-shrink-0">
        <img
          :src="data.imgUrl"
          mode="aspectFill"
          alt=""
          class="w-[52px] h-[52px] rounded-[50px] border-[1px] border-solid border-[#07C160] block"
          @click.stop="toLawyerHome"
        >
        <div
          v-if="data.online"
          class="w-[40px] h-[17px] bg-[#07C160] rounded-[47px] flex items-center justify-center absolute top-[42px] absolute-x-center"
        >
          <div class="font-bold text-[12px] text-[#FFFFFF]">
            在线
          </div>
        </div>
      </div>
      <div class="flex-1 pl-[12px]">
        <div class="flex items-center justify-between">
          <div>
            <div class="font-bold text-[16px] text-[#333333]">
              {{ data.realName }}律师
            </div>
            <div
              v-if="data.workCityName"
              class="flex items-center pt-[13px] text-[13px] text-[#666666]"
            >
              <i class="iconfont icon-dingwei !text-[13px] pr-[4px] text-[#333333]" />
              {{ data.workCityName }}
            </div>
          </div>
          <div
            class="w-[76px] h-[28px] text-[13px] text-[#FFFFFF] bg-[linear-gradient(_116deg,_#71B5FF_0%,_#2676E4_100%)] rounded-[50px] flex items-center justify-center box-border shrink-0"
            @click="callPhone"
          >
            打招呼
          </div>
        </div>
        <div class="flex items-center mt-[8px] space-x-[8px]">
          <div
            v-if="data.workTime"
            class="text-[12px] text-[#666666] px-[4px] box-border h-[23px] bg-[#F7F6F6] rounded-[4px] flex items-center justify-center shrink-0"
          >
            执业{{ data.workTime }}年
          </div>
          <div class="text-[12px] text-[#666666] px-[4px] box-border h-[23px] bg-[#F7F6F6] rounded-[4px] flex items-center justify-center shrink-0">
            执业认证
          </div>
        </div>
        <div class="pt-[8px] leading-[18px]  relative line-clamp-1 text-[12px] text-[#999999]">
          案件协作：{{ xzTypeDesc }}
        </div>
        <div class="pt-[8px] leading-[18px]  relative text-ellipsis text-[12px] text-[#999999]">
          异地查档：{{ cdTypeDesc }}
          <p
            class="right-0 bottom-0 text-[12px] text-[#3887F5] absolute w-[82px] bg-[linear-gradient(_90deg,_rgba(255,255,255,0.2)_0%,_#FFFFFF_30%)] text-align-rt"
            @click.stop="handleSeeMore"
          >
            查看更多>
          </p>
        </div>
        <div class="h-[33px] text-[12px] border-solid flex items-center justify-between border-[#EEEEEE] border-0 border-t-[1px] text-[#999999] mt-[8px]">
          <p>{{ data.lastOnlineTimeDesc }}在线</p>
          <div class="flex items-center">
            <i class="iconfont mt-[2px] icon-liulan !text-[12px] pr-[3px] text-[#999999]" />
            19.99w 浏览
          </div>
        </div>
      </div>
    </div>
    <find-lawyer-popup
      v-model="popup"
      :data="data"
    />
    <lawyer-the-type-of-collaboration
      v-model="seeMore"
      :data="data"
    />
    <!-- 禁止访问主页   -->
    <access-to-the-home-page-is-prohibited
      v-model="accessHomePageState"
    />
  </div>
</template>

<script>
import { BURY_POINT_CHANNEL_TYPE, buryPointChannelBasics } from "@/libs/burypoint";
import { toLawyerHome, turnToImPage, turnToLawyerAuthResultPageToLogin } from "@/libs/turnPages";
import AccessToTheHomePageIsProhibited from "@/pages/find-lawyer/components/AccessToTheHomePageIsProhibited.vue";
import LawyerTheTypeOfCollaboration from "@/pages/find-lawyer/components/LawyerTheTypeOfCollaboration.vue";
import FindLawyerPopup from "@/pages/mine/components/FindLawyerPopup.vue";
import { imGetImInfo } from "@/api/im";

export default { 
  name: "HomeLawyerCard",
  components: { AccessToTheHomePageIsProhibited, LawyerTheTypeOfCollaboration, FindLawyerPopup },
  props: {
    data: {
      type: Object,
      default: () => ({}),
      required: true,
    }
  },
  data(){
    return {
      popup: false,
      seeMore: false,
      /* 禁止访问主页弹窗 */
      accessHomePageState: false
    };
  },
  computed: {
    /* 律师协作 */
    xzTypeDesc(){
      return (this.data.xzTypeDesc || []).join("、");
    },
    /* 查档 */
    cdTypeDesc(){
      return (this.data.cdTypeDesc || []).join("、");
    }
  },
  methods: {
    toLawyerHome(){
      /* 判断是否律师有无设置可以查看主页 */
      /*       if(this.data.personalWebsiteAllow === 0){
        this.accessHomePageState = true;
        return;
      } */
      toLawyerHome({
        id: this.data.id
      });
    },
    handleSeeMore(){
      this.seeMore = true;
    },
    callPhone() {
      buryPointChannelBasics({
        code: "LAWYER_TOOLS_INDEX_PAGE_CONTRACT_NOW_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });

      turnToLawyerAuthResultPageToLogin(() => {
        // 先查询有没有创建过im  创建过直接进去  没有在弹窗
        return imGetImInfo({
          type: 12,
          lawyerId: this.data.id
        }).then(({ data = {} }) => {
          if(!data.imSessionId){
            this.popup = true;
          }else{
            turnToImPage({
              id: data.imSessionId
            });
          }
        });
      });

    }
  }
};
</script>
