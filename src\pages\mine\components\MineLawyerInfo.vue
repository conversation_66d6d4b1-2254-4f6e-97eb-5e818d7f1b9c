<template>
  <div class="info">
    <img
      :src="header"
      alt=""
      class="info__image"
      @click="handleClickHeader"
    >
    <div class="mg-l-12 flex-1">
      <div class="info__title">
        {{ userInfo.realName || "游客" }}
      </div>
      <img
        v-if="certStatus === 2"
        alt=""
        class="info__cert"
        src="@/pages/mine/img/Frame1321314875.png"
      >
      <div
        v-else
        class="info__tips"
        @click="jump"
      >
        <div>{{ tipsText }}</div>
        <i class="iconfont icon-erjiyoujiantou" />
      </div>
    </div>
  </div>
</template>

<script>
import { isNotLogin, whetherToLogIn } from "@/libs/tools";
import { mapGetters } from "vuex";
import { toLawyerHome, turnToLawyerAuthResultPage } from "@/libs/turnPages";

export default {
  name: "MineLawyerInfo",
  methods: {
    jump() {
      whetherToLogIn(() => {});

      if (!isNotLogin()) turnToLawyerAuthResultPage();
    },
    /** 点击头像 */
    handleClickHeader() {
      whetherToLogIn(() => {});

      if (!isNotLogin()) toLawyerHome();
    },
  },
  computed: {
    ...mapGetters({
      userInfo: "user/getUserInfo",
      certStatus: "user/getCertStatus",
    }),
    isLogin(){
      return !isNotLogin();
    },
    /** 提示文案 */
    tipsText() {
      if (isNotLogin()) return "点击登录";

      switch (this.certStatus) {
      case 1:
        return "律师认证审核中，点击查看进度";
      default:
        return "未认证，去认证律师身份";
      }
    },
    /** 头像 */
    header() {
      return (
        this.userInfo.imgUrl || require("@/pages/mine/img/Frame1321314870.png")
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.info {
  display: flex;
  align-items: center;

  &__image {
    flex-shrink: 0;
    display: block;
    width: 52px;
    height: 52px;
    border-radius: 50%;
  }

  &__title {
    font-size: 18px;
    font-weight: bold;
    color: #333333;
  }

  &__cert {
    margin-top: 6px;
    display: block;
    width: 74px;
    height: 18px;
  }

  &__tips {
    margin-top: 6px;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 400;
    color: #3887f5;

    .iconfont {
      font-size: 12px;
    }
  }
}
</style>
