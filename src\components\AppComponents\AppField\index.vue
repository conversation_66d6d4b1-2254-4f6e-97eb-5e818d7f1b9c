<template>
  <div
    :class="[textAlign]"
    class="field flex flex-align-center"
    @click="$emit('click')"
  >
    <div>
      <slot>
        <span
          :class="[lineClampStyle]"
          :style="[
            {
              color: value ? color : placeholderColor,
            },
            spaceStyle
          ]"
        >{{ value || placeholder }}</span>
      </slot>
    </div>
    <slot name="icon">
      <i
        v-if="arrow"
        class="iconfont icon-erjiyoujiantou text-cccccc"
      />
    </slot>
  </div>
</template>

<script>
import {pxToRpx} from "@/libs/tools";

export default {
  name: "AppField",
  props: {
    value: {
      type: String,
      default: "",
    },
    /** 是否显示箭头 */
    arrow: {
      type: Boolean,
      default: false,
    },
    /** 对齐方式 */
    align: {
      type: String,
      validator: (value) => {
        return ["left", "right"].includes(value);
      },
      default: "right",
    },
    /** 无值的文案 */
    placeholder: {
      type: String,
      default: "请输入",
    },
    /** 有值的颜色 */
    color: {
      type: String,
      default: "#333333",
    },
    /** 无值的颜色 */
    placeholderColor: {
      type: String,
      default: "#cccccc",
    },
    /** 超过行数省略 */
    lineClamp: {
      type: [Number, String],
      default: 0,
    },
    /* 文字到箭头的间距*/
    space: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    /** 对齐方式 */
    textAlign() {
      switch (this.align) {
      case "left":
        return "flex-space-between";
      case "right":
        return "flex-space-end";
      default:
        return "flex-space-end";
      }
    },
    /** 超过行数省略 */
    lineClampStyle() {
      return this.lineClamp ? `text-ellipsis-${this.lineClamp}` : "";
    },
    spaceStyle() {
      return {
        paddingRight: pxToRpx(this.space),
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.field {
  font-size: 14px;
  font-weight: 400;
  color: #999999;

  &--primary {
    color: #3887f5;
  }
}
</style>
