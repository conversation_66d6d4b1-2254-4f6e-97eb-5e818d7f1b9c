/**
 * 律师主页控制导航栏显示隐藏
 */
export default {
  namespaced: true,
  state: {
    /** 案件总数
     * 由于这里要控制渲染，如果为0的话组件不会渲染 也不会请求，会一直为0
     * 所以默认值为1
     */
    caseCount: 1,
    /**
     * 评价总数
     * 同上
     */
    evaluateCount: 1,
    /* 文章总数*/
    articleCount: 1,
    /* 电话咨询弹窗配置*/
    phoneConsultationConfig: {
      lawyerInfo: {},
      serverInfo: {}
    },
    /** 电话咨询弹窗回调 */
    phoneConsultationCallback: [],
    /** 保存获取title回调 */
    getTitleCallback: null,
    /** 图文、电话服务信息 */
    phoneAndGraphicServiceInfo: {},
  },
  mutations: {
    /** 设置案件总数 */
    SET_CASE_COUNT(state, count) {
      state.caseCount = count;
      state.getTitleCallback?.();
    },
    /** 设置评价总数 */
    SET_EVALUATE_COUNT(state, count) {
      state.evaluateCount = count;
      state.getTitleCallback?.();
    },
    /** 设置文章总数 */
    SET_ARTICLE_COUNT(state, count) {
      state.articleCount = count;
      state.getTitleCallback?.();
    },
    /**
     * 触发 getTitleCallback
     * 重新获取title
     */
    CALL_TITLE_CALLBACK(state){
      state.getTitleCallback?.();
    },
    /** 初始化state中的数据 */
    INIT_STATE(state) {
      state.caseCount = 1;
      state.evaluateCount = 1;
      state.articleCount = 1;
    },
    /** 设置电话咨询弹窗配置 */
    SET_PHONE_CONSULTATION_CONFIG(state, config) {
      state.phoneConsultationConfig = config;
    },
    /** 重置电话咨询弹窗配置 */
    RESET_PHONE_CONSULTATION_CONFIG(state) {
      state.phoneConsultationConfig = {
        lawyerInfo: {},
        serverInfo: {}
      };
    },
    /** 添加电话咨询弹窗回调 */
    ADD_PHONE_CONSULTATION_CALLBACK(state, cb) {
      state.phoneConsultationCallback.push(cb);
    },
    /** 移除电话咨询弹窗回调 */
    REMOVE_PHONE_CONSULTATION_CALLBACK(state, cb) {
      const index = state.phoneConsultationCallback.indexOf(cb);

      if (index > -1) {
        state.phoneConsultationCallback.splice(index, 1);
      }
    },
    /**
     * 设置获取title回调
     * 因为很多数据是异步获取的，所以需要在获取到数据后再设置title
     * 如果在滚动的时候就设置title，会导致title在滚动时选中项会跳动
     */
    SET_GET_TITLE_CALLBACK(state, cb) {
      state.getTitleCallback = cb;
    },
    /** 设置图文、电话服务信息 */
    SET_PHONE_AND_GRAPHIC_SERVICE_INFO(state, info) {
      state.phoneAndGraphicServiceInfo = info;
    },
  },
  getters: {
    /** 是否有案件 */
    hasCase(state) {
      return state.caseCount > 0;
    },
    /** 是否有评价 */
    hasEvaluate(state) {
      return state.evaluateCount > 0;
    },
    /** 是否有文章 */
    hasArticle(state) {
      return state.articleCount > 0;
    },
    /** 电话咨询弹窗 */
    getPhoneConsultation(state) {
      return state.phoneConsultationConfig;
    },
  },
  actions: {
    /** 开启电话咨询配置 */
    openPhoneConsultationConfig({ commit, state }, config) {
      // 触发回调
      state.phoneConsultationCallback.forEach(cb => {
        cb();
      });

      commit("SET_PHONE_CONSULTATION_CONFIG", {
        ...config
      });
    },
  }
};
