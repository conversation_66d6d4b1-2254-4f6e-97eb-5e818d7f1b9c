import { requestCommon } from "@/libs/axios";

/** 查询该律师不同案源等级的剩余线索次数 */
export const lawyerCluePackRemainClueNum = (data) =>
  requestCommon.post("/paralegal/lawyerCluePack/remainClueNum", data);

/** 查询该律师历史的购买记录明细V2 */
export const lawyerCluePackBuyHistoryByClueType = (data) =>
  requestCommon.post("/paralegal/lawyerCluePack/buy/historyByClueType", data);

/** 获取消耗单个案源商品所抢的案源信息 */
export const lawyerCaseSourceGoodsServerPage = (data) =>
  requestCommon.post(
    "/paralegal/lawyerCaseSource/lawyerCaseSourceGoodsServerPage",
    data
  );

/** 律师点击结束完成服务（我的案源结束跟进，im聊天中点击完成服务案源） */
export const caseSourceServerV2CompleteCaseServer = (caseSourceServerV2Id) =>
  requestCommon.post(
    `/paralegal/caseSourceServerV2/completeCaseServer/${caseSourceServerV2Id}`
  );
