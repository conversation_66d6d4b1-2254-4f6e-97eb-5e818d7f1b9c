<template>
  <div class="flex items-center justify-between">
    <div
      class="w-[171px] h-[90px] bg-[linear-gradient(_151deg,_#FFFFFF_0%,_#F4F9FF_100%)] rounded-[8px] box-border p-[12px] position-relative"
      @click="toAnswerSquare"
    >
      <div class="font-bold text-[16px] text-[#333333]">
        法律问答
      </div>
      <div class="text-[12px] text-[#666666] mt-[4px] text-ellipsis">
        {{ data.todayQaMessageNewestInfo }}
      </div>
      <div class="text-[11px] text-[#666666] mt-[10px] flex items-center">
        #今日<span class="text-[#3887F5]">{{ data.todayAnswerLawyerCount }}</span>位律师参与解答
      </div>
      <img
        alt=""
        class="block w-[27px] h-[27px] absolute bottom-[12px] right-[12px]"
        src="@/pages/index/imgs/Frame1321315903.png"
      >
    </div>
    <div
      class="w-[171px] h-[90px] bg-[linear-gradient(_147deg,_#FFFFFF_0%,_#FFF9F6_100%)] rounded-[8px] box-border p-[12px] position-relative"
      @click="toOrderSuccessful"
    >
      <div class="flex items-center">
        <div class="font-bold text-[16px] text-[#333333]">
          成单案例
        </div>
        <img
          alt=""
          class="block w-[24px] h-[27px] absolute bottom-[12px] right-[12px]"
          src="@/pages/index/imgs/Frame1634.png"
        >
      </div>
      <div class="text-[12px] text-[#666666] mt-[4px] text-ellipsis">
        {{ latestGoodNews.title }}
      </div>
      <div class="text-[11px] text-[#666666] mt-[10px] flex items-center">
        #今日<span class="text-[#F6513B]">{{ latestGoodNews.getRedPacketLawyerNum }}</span>位律师已瓜分红包
      </div>
      <img
        alt=""
        class="block w-[24px] h-[27px] absolute bottom-[12px] right-[12px]"
        src="@/pages/index/imgs/4uzotc.png"
      >
    </div>
  </div>
</template>

<script>

import { getLatestGoodNews } from "@/api";
import { toAnswerSquare, toOrderSuccessful } from "@/libs/turnPages";

export default {
  name: "ServiceButton",
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data(){
    return {
      latestGoodNews: {}
    };
  },
  mounted() {
    this.getLatestGoodNews();
  },
  methods: {
    toAnswerSquare,
    toOrderSuccessful(){
      toOrderSuccessful();
    },
    getLatestGoodNews(){
      getLatestGoodNews().then(res => {
        this.latestGoodNews = res.data;
      });
    }
  }
};
</script>
