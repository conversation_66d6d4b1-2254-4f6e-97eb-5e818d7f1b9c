<template>
  <div class="case-source-notice">
    <u-notice-bar
      bgColor="#EBF3FE"
      color="#3887F5"
      direction="column"
      mode="closable"
      :icon="' '"
      disableTouch
      :text="['该用户为案源发起用户，您回复后可正常沟通']"
      @close="$emit('close')"
    />
  </div>
</template>

<script>
import UNoticeBar from "@/uview-ui/components/u-notice-bar/u-notice-bar.vue";

export default {
  name: "CaseSourceNotifications",
  components: { UNoticeBar }
};
</script>

<style scoped lang="scss">

.case-source-notice{
  box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.12);
}
</style>
