<template>
  <div class="flex items-center justify-between">
    <div
      class="px-[12px] py-[16px] w-[171px] h-[74px] box-border position-relative"
      @click="toLawyerHome"
    >
      <img
        alt=""
        class="background-image"
        src="@/pages/mine/img/<EMAIL>"
      >
      <div class="flex items-center">
        <div class="font-bold text-[14px] text-[#333333]">
          个人网站
        </div>
        <div
          class="ml-[6px] flex items-center justify-center w-[52px] h-[16px] bg-[linear-gradient(_90deg,_#FE6128_0%,_#FF8213_100%)] rounded-tl-[8px] rounded-br-[8px] rounded-tr-[8px] rounded-bl-[1px] border-[1px] border-solid border-[#FFFFFF]"
        >
          <div class="text-[10px] text-[#FFFFFF]">
            获得曝光
          </div>
        </div>
      </div>
      <div class="text-[11px] text-[#666666] flex items-center mt-[6px]">
        <div class="shrink-0">
          分享下单
        </div>
        <div
          class="mx-[4px] shrink-0"
        >
          服务定价
        </div>
        <div class="shrink-0">
          名片打造
        </div>
      </div>
    </div>
    <div
      class="px-[12px] py-[16px] w-[171px] h-[74px] box-border position-relative"
      @click="toMyCollaborationList"
    >
      <img
        alt=""
        class="background-image"
        src="@/pages/mine/img/Frame8710.png"
      >
      <div class="font-bold text-[14px] text-[#333333]">
        我发布的协作
      </div>
      <div class="text-[11px] text-[#666666] flex items-center mt-[6px]">
        <div class="shrink-0">
          协作服务设置
        </div>
        <div class="ml-[4px] shrink-0">
          发布需求及接单
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { toLawyerHome, toMyCollaborationList, turnToLawyerAuthResultPageToLogin } from "@/libs/turnPages";

export default {
  name: "MineButton",
  methods: {
    toLawyerHome(){
      turnToLawyerAuthResultPageToLogin(() => {
        toLawyerHome();
      });
    },
    toMyCollaborationList() {
      turnToLawyerAuthResultPageToLogin(() => {
        toMyCollaborationList();
      });
    },
  },
};
</script>
