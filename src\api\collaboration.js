import { requestCommon } from "@/libs/axios";

/* 发布异地查档/案件协作*/
export const lawyerCollaborationPublish = (params) => requestCommon.post("/paralegal/lawyerCollaboration/publish", params); //

/** 
 * 协作协作广场分页
 * https://showdoc.imlaw.cn/web/#/5/3034
 */
export const lawyerCollaborationCooperativeSquarePage = (params) => requestCommon.post("/paralegal/lawyerCollaboration/cooperativeSquarePage", params); //

/* 协作详情*/
export const lawyerCollaborationDetailById = (params) => requestCommon.post("/paralegal/lawyerCollaboration/detailById", params); //

/** 
 * 根据接单记录id查询律师手机号
 * https://showdoc.imlaw.cn/web/#/5/3159
 */
export const lawyerCollaborationGetPhoneByJdRecordId = (params) => requestCommon.post("/paralegal/lawyerCollaboration/getPhoneByJdRecordId", params); //

/* 协作立即抢单*/
export const lawyerCollaborationGrabOrder = (params) => requestCommon.post("/paralegal/lawyerCollaboration/grabOrder", params); //

/* 关闭自己发布的协作内容*/
export const lawyerCollaborationClose = (params) => requestCommon.post("/paralegal/lawyerCollaboration/close", params); //

/* 我发布的协作列表*/
export const lawyerCollaborationMyPublishPage = (params) => requestCommon.post("/paralegal/lawyerCollaboration/myPublishPage", params); //

/* 我的接单协作列表*/
export const lawyerCollaborationMyAcceptOrder = (params) => requestCommon.post("/paralegal/lawyerCollaboration/myAcceptOrder", params); //

/* 协作广场轮播*/
export const lawyerCollaborationMoment = (params) => requestCommon.post("/paralegal/lawyerCollaboration/moment", params); //

/* 我的协作-协作服务设置-修改 */
export const lawyerCollaborationConfigUpdate = (params) => requestCommon.post("/paralegal/lawyerCollaboration/config/update", params); //

/* 我的协作-协作服务设置-查询 */
export const lawyerCollaborationConfigQuery = (params) => requestCommon.post("/paralegal/lawyerCollaboration/config/query", params); //
