<template>
  <app-popup
    :safeAreaInsetBottom="false"
    :show="show"
    mode="center"
  >
    <div class="w-[311px] bg-[#FFFFFF] rounded-[16px]">
      <p class="font-bold w-[263px] mx-auto text-[16px] text-[#333333] pt-[24px] pb-[20px] text-align-center">
        {{ data.realName }}律师可提供以下协作服务
      </p>
      <div class="px-[24px] pb-[24px]">
        <div class="bg-[#FFFFFF] rounded-[8px] border-[1px] border-solid border-[#E8EBFA] overflow-hidden">
          <p class="text-[14px] px-[10px] py-[8px] text-[#333333] ">
            案件协作：
          </p>
          <p class="text-[14px] p-[10px] text-[#3887F5] bg-[#F9FAFF]">
            {{ (data.xzTypeDesc||[]).join('、') }}
          </p>
          <p class="text-[14px] px-[10px] py-[8px] text-[#333333] ">
            异地查档：
          </p>
          <p class="text-[14px] p-[10px] text-[#3887F5] bg-[#F9FAFF]">
            {{ (data.cdTypeDesc||[]).join('、') }}
          </p>
        </div>
      </div>
      <div class="px-[24px] pb-[16px]">
        <div
          class="h-[36px] bg-[#3887F5] rounded-[68px] font-bold text-[14px] text-[#FFFFFF] flex items-center justify-center"
          @click="show=false"
        >
          知道了
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";

export default {
  name: "LawyerTheTypeOfCollaboration",
  components: { AppPopup },
  props: {
    data: {
      type: Object,
      default: () => ({}),
      required: true
    },
    value: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  computed: {
    show: {
      get(){
        return this.value;
      },
      set(val){
        this.$emit("input", val);
      }
    }
  },
};
</script>

<style scoped lang="scss">

</style>