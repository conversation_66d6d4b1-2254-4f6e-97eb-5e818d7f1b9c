<template>
  <div class="user-reviews">
    <p class="title text-center">
      用户评价了您的服务
    </p>
    <div class="score flex flex-align-center flex-space-center">
      <p class="text">
        总体
      </p>
      <img
        v-for="i in 5"
        :key="i"
        class="score-img"
        :src="i<=customExtsinExt.totalScore?fraction:unFraction"
        alt=""
      >
    </div>
    <div class="tags flex flex-space-center flex-align-center flex-wrap">
      <div
        v-for="(i,index) in (customExtsinExt.labels||[])"
        :key="index"
        class="tag"
      >
        {{ i }}
      </div>
    </div>
    <div
      v-if="customExts.content"
      class="desc text-center"
    >
      “{{ customExts.content }}”
    </div>
    <div
      v-if="customExtsinExt.totalScore>=3"
      class="tip"
    >
      您在本次付费咨询服务中获得了当事人的高度好评，保持优质的律师服务将获得更多的付费咨询公共派单及平台曝光！
    </div>
  </div>
</template>

<script>
import cardProps, { customComputed } from "@/pages/sub/im/mixins/card-props.js";

export default {
  name: "UserReviews",
  mixins: [cardProps, customComputed],
  data() {
    return {
      fraction: require("@/pages/sub/im/imgs/fraction.png"),
      unFraction: require("@/pages/sub/im/imgs/unfraction.png")
    };
  },
};
</script>

<style scoped lang="scss">
  .user-reviews{
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    opacity: 1;
    border: 1px solid #EEEEEE;
    padding:16px 16px 0;
    overflow: hidden;
    .title{
      font-size: 16px;
      font-weight: 500;
      color: #222222;
      padding-bottom: 16px;
    }
    .score{
      padding-bottom: 20px;
      .text{
        font-size: 15px;
        font-weight: 500;
        color: #222222;
        padding-right: 8px;
      }
      .score-img{
        padding-left: 8px;
        width: 30px;
        height: 30px;
      }
    }
    .tags{
      padding-bottom: 16px;
      overflow: hidden;
      .tag{
        padding: 6px 12px;
        font-size: 12px;
        font-weight: 400;
        color: #F2AF30;
        border: 1px solid #F2AF30;
        border-radius: 17px 17px 17px 17px;
        overflow: hidden;
        margin-left: 12px;
        margin-top: 4px;
      }
    }
    .desc{
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      padding-bottom: 16px;
    }
    .tip{
      background: #F4F8FE;
      border-radius: 8px 8px 8px 8px;
      padding: 12px;
      font-size: 12px;
      font-weight: 400;
      color: #666666;
      margin-bottom: 16px;
    }
  }
</style>
