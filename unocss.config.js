import presetWeapp from "unocss-preset-weapp";
import {
  extractorAttributify,
  transformerClass,
} from "unocss-preset-weapp/transformer";
import { defineConfig } from "unocss";

const { transformerAttributify, presetWeappAttributify } =
  extractorAttributify();

// 自定义转换器，将复杂选择器转换为小程序兼容类名
const complexSelectorsTransformer = () => {
  return {
    name: "complex-selectors-transformer",
    enforce: "pre",
    transform(code) {
      // 处理 [&:not(:last-child)]:mb-[数字px] 模式
      code.replace(
        /\[&:not\(:last-child\)\]:mb-\[(\d+(?:\.\d+)?)px\]/g,
        (_, size) => {
          return `not-last-child-mb-${size}`;
        }
      );

      // 处理 [&:not(:last-child)]:border-r-[数字px] 模式
      code.replace(
        /\[&:not\(:last-child\)\]:border-r-\[(\d+(?:\.\d+)?)px\]/g,
        (_, size) => {
          return `not-last-child-border-r-${size}`;
        }
      );

      // 处理 [&:not(:first-child)]:mt-[数字px] 模式
      code.replace(
        /\[&:not\(:first-child\)\]:mt-\[(\d+(?:\.\d+)?)px\]/g,
        (_, size) => {
          return `not-first-child-mt-${size}`;
        }
      );

      // 处理 [&:not(:last-child)]:border-b-[数字px] 模式
      code.replace(
        /\[&:not\(:last-child\)\]:border-b-\[(\d+(?:\.\d+)?)px\]/g,
        (_, size) => {
          return `not-last-child-border-b-${size}`;
        }
      );

      // 处理 [&:not(:last-child)]:mr-[数字px] 模式
      code.replace(
        /\[&:not\(:last-child\)\]:mr-\[(\d+(?:\.\d+)?)px\]/g,
        (_, size) => {
          return `not-last-child-mr-${size}`;
        }
      );

      return code;
    },
  };
};

// 合并变换器，集成了颜色处理和px转rpx的功能
function combinedTransformer() {
  return {
    name: "combined-transformer",
    enforce: "pre",
    transform(code) {
      code = code.replace(
        /([a-zA-Z][a-zA-Z0-9]*(?:-[a-zA-Z][a-zA-Z0-9]*)*)-([0-9A-Fa-f]{6})\b/g,
        (match, prop, color) => {
          return `${prop}-#${color}`;
        }
      );

      // 转换直接px格式的尺寸类，如 w-16px 转为 w-32rpx
      code = code.replace(
        /(-?[a-zA-Z][a-zA-Z0-9]*(?:-[a-zA-Z][a-zA-Z0-9]*)*)-(\d+(?:\.\d+)?)px\b/g,
        (match, prop, size) => {
          const rpxSize = parseFloat(size) * 2;
          return `${prop}-${rpxSize}rpx`;
        }
      );

      // 转换多值px格式 p-[12px_16px_8px_16px] 为 p-[24rpx_32rpx_16rpx_32rpx]
      code = code.replace(
        /(-?[a-zA-Z][a-zA-Z0-9]*(?:-[a-zA-Z][a-zA-Z0-9]*)*)-\[([^[\]]*_[^[\]]*)\]/g,
        (match, prop, values) => {
          // 只处理包含px的多值情况
          if (values.includes("px")) {
            const convertedValues = values.replace(/(\d+(?:\.\d+)?)px/g, (_, size) => {
              const rpxSize = parseFloat(size) * 2;
              return `${rpxSize}rpx`;
            });
            return `${prop}-[${convertedValues}]`;
          }
          
          // 如果不包含px，保持原样
          return match;
        }
      );

      // 转换单值px格式 w-[16px] 为 w-32rpx
      code = code.replace(
        /(-?[a-zA-Z][a-zA-Z0-9]*(?:-[a-zA-Z][a-zA-Z0-9]*)*)-\[(\d+(?:\.\d+)?)px\]/g,
        (match, prop, size) => {
          const rpxSize = parseFloat(size) * 2;
          return `${prop}-${rpxSize}rpx`;
        }
      );

      return code;
    },
  };
}

export default defineConfig({
  presets: [
    // https://github.com/MellowCo/unocss-preset-weapp
    presetWeapp({
      // h5兼容
      platform: "uniapp",
      isH5: process.env.UNI_PLATFORM === "h5",
      // 关闭注释
      transformCSS: {
        // 不生成颜色注释
        commentColors: false,
      },
    }),
    presetWeappAttributify(),
  ],
  shortcuts: [
    {
      "border-base": "border border-gray-500_10",
      center: "flex justify-center items-center",
    },
  ],
  // 如果 enforce 相同，则从上到下执行
  transformers: [
    complexSelectorsTransformer(),
    combinedTransformer(),
    transformerAttributify(),
    transformerClass(),
  ],
  rules: [
    // 解决 在unocss中 font-[600] 会被编译成为font-family: 600
    [/^font-\[(\d+)\]$/, ([, d]) => ({ "font-weight": d })],
    // 解决 在unocss中 font14 会被编译成为font-family: 14
    [/^font(\d+(?:\.\d+)?)$/, ([, d]) => ({ "font-size": `${parseFloat(d) * 2}rpx` })],
    // 为转换后的类名定义样式
    [
      /^not-last-child-mb-(\d+(?:\.\d+)?)$/,
      ([, size]) => {
        return {
          "view + view": {
            "margin-top": `${size}rpx`,
          },
        };
      },
    ],

    [
      /^not-last-child-border-r-(\d+(?:\.\d+)?)$/,
      ([, size]) => {
        return {
          "view:not(:last-child)": {
            "border-right-width": `${size}rpx`,
            "border-right-style": "solid",
            "border-right-color": "currentColor",
          },
        };
      },
    ],

    [
      /^not-first-child-mt-(\d+(?:\.\d+)?)$/,
      ([, size]) => {
        return {
          "view + view": {
            "margin-top": `${size}rpx`,
          },
        };
      },
    ],

    [
      /^not-last-child-border-b-(\d+(?:\.\d+)?)$/,
      ([, size]) => {
        return {
          "view:not(:last-child)": {
            "border-bottom-width": `${size}rpx`,
            "border-bottom-style": "solid",
            "border-bottom-color": "currentColor",
          },
        };
      },
    ],

    [
      /^not-last-child-mr-(\d+(?:\.\d+)?)$/,
      ([, size]) => {
        return {
          "view:not(:last-child)": {
            "margin-right": `${size}rpx`,
          },
        };
      },
    ],
  ],
});