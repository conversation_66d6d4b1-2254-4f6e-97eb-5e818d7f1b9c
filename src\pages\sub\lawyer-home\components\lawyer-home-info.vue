<template>
  <app-popup
    :round="16"
    :show="value"
    :zIndex="9998"
    position="bottom"
  >
    <div>
      <div
        class="px-[16px] py-[16px] flex justify-between items-center text-[18px] font-bold text-[#333333]"
      >
        <div>律师档案</div>
        <img
          alt=""
          class="w-[24px] h-[24px] block"
          src="../img/close.png"
          @click="$emit('input', false)"
        >
      </div>
      <div
        class="lawyer-info px-[16px] py-[10px] h-[390px] overflow-y-auto box-border"
      >
        <div
          v-for="(item, index) in infoArr"
          :key="index + 'infoArr'"
          class="lawyer-info-content flex mb-[20px] last:mb-0"
        >
          <img
            :src="item.icon"
            alt=""
            class="icon"
            srcset=""
          >
          <div class="text-box">
            <p class="lawyer-sub-title">
              {{ item.name }}
            </p>
            <p
              v-show="item.single"
              class="desc"
            >
              {{ lawyerInfo[item.key] || "-" }}
            </p>
            <p
              v-show="item.key === 'certStatus'"
              class="desc"
            >
              {{ certStatus }}
            </p>
            <div v-show="item.key === 'lawyerEdus'">
              <div
                v-for="(item, index) in lawyerInfo.lawyerEdus"
                v-show="lawyerInfo.lawyerEdus"
                :key="index"
                class="desc"
              >
                <p>
                  {{ item.school + (item.degree ? "-" + item.degree : "") }}
                </p>
              </div>
              <p
                v-show="!lawyerInfo.lawyerEdus"
                class="desc font12"
              >
                -
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";

export default {
  name: "LawyerHomeInfo",
  components: { AppPopup },
  props: {
    lawyerInfo: {
      type: Object,
      default: () => ({}),
    },
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      infoArr: [
        {
          name: "执照认证",
          key: "certStatus",
          icon: require("@/pages/sub/lawyer-home/img/zzrz-icon.png"),
        },
        {
          name: "律所认证",
          key: "lawyerOffice",
          icon: require("@/pages/sub/lawyer-home/img/lsrz-icon.png"),
          single: true,
        },
        {
          name: "教育背景",
          key: "lawyerEdus",
          icon: require("@/pages/sub/lawyer-home/img/jybj-icon.png"),
        },
        {
          name: "荣誉奖项",
          key: "honor",
          icon: require("@/pages/sub/lawyer-home/img/ryjx-icon.png"),
          single: true,
        },
        {
          name: "简介",
          key: "lawyerProfile",
          icon: require("@/pages/sub/lawyer-home/img/jj-icon.png"),
          single: true,
        },
      ],
    };
  },
  computed: {
    /**
     * 认证状态
     * [1:待认证,2.已认证,3.未认证,4.认证失败] 不等于2都是未认证
     */
    certStatus() {
      return (
        this.lawyerInfo.certificateId ||
        (Number(this.lawyerInfo.certStatus) !== 2 ? "未认证" : "已认证")
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.lawyer-file-title {
  height: 46px;
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  line-height: 46px;
  padding-left: 16px;
}

.lawyer-info {
  .lawyer-info-content {
    overflow: hidden;

    .icon {
      flex-shrink: 0;
      width: 32px;
      height: 32px;
      margin-right: 12px;
    }

    .text-box {
      .lawyer-sub-title {
        color: #222;
        font-size: 14px;
      }

      .desc {
        font-size: 12px;
        color: #666;
        margin-top: 6px;
        word-wrap: break-word;
        word-break: break-all;

        &-identity {
          height: 18px;
          background: #edf5ff;
          border-radius: 11px 11px 11px 11px;
          font-size: 12px;
          transform: scale(0.8);
          transform-origin: left center;
          color: #177eff;
          padding: 3px 6px;

          img {
            width: 9px;
            height: 10px;
            margin-right: 1px;
          }
        }
      }
    }
  }
}
</style>
