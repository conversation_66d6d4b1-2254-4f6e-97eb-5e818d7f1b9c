<template>
  <div class="tips">
    <div class="flex flex-align-center">
      <i class="iconfont icon-xianxingtubiao-2" />
      <div class="mg-l-8">
        {{ tips }}
      </div>
    </div>
    <i class="iconfont icon-er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" />
  </div>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: "MineLawyerTips",
  computed: {
    ...mapGetters({
      userInfo: "user/getUserInfo",
    }),
    /** 提示文案 */
    tips() {
      // 个人资料完成状态[1.暂未上传,2.机审未通过,3.机审通过待人审,4.人审通过，5.人审未通过]
      let number = Number(this.userInfo.profileStatus);
      if (number === 1) {
        return "个人网站评分60分，完善资料获得更多曝光";
      } else if (number === 2 || number === 5) {
        return "个人资料审核未通过，请重新提交";
      } else if (number === 4) {
        return "";
      } else {
        return "个人资料审核中";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.tips {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #FFF7EB;
  padding: 10px 16px;
  font-size: 13px;
  font-weight: 400;
  color: #FA700D;
  border-radius: 8px;
}
</style>
