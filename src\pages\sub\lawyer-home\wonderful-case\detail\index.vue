<template>
  <login-layout>
    <div>
      <img
        alt=""
        class="top-img"
        src="../../img/<EMAIL>"
      >
      <document-detail-of-me-info :lawCaseRes="lawCaseRes" />
      <app-placeholder
        :height="72"
        :showSafe="false"
      />
      <!-- 底部按钮 -->
      <div class="footer">
        <div class="footer__btn flex flex-align-center flex-space-between">
          <!-- 左边的按钮 -->
          <div
            class="footer__btn__left"
            @click="clickLeftBtn"
          >
            免费问律师
          </div>
          <!-- 右边的按钮 -->
          <div
            class="footer__btn__right flex flex-align-center"
            @click="clickRightBtn"
          >
            <img
              :src="lawyerInfo.imgUrl"
              mode="aspectFill"
              alt=""
              class="footer__btn__right__img"
            >
            <div class="mg-l-8">
              <p class="footer__btn__right__name">
                咨询 {{ lawyerInfo.realName || "" }}律师
              </p>
              <p class="footer__btn__right__price">
                ¥{{ minPrice | amountFilterOne }}元起
              </p>
            </div>
          </div>
        </div>
        <u-safe-bottom />
      </div>
    </div>
  </login-layout>
</template>

<script>
import { lawyerLawCaseDetail, oneLawyer } from "@/api/lawyer.js";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import AppPlaceholder from "@/components/AppComponents/AppPlaceholder/index.vue";
import { toPayLawyerGuide } from "@/libs/turnPages.js";
import DocumentDetailOfMeInfo from "@/pages/sub/lawyer-home/document-detail-of-me/DocumentDetailOfMeInfo.vue";
import LoginLayout from "@/components/login/login-layout.vue";

export default {
  name: "LawyerHomeWonderfulCaseDetail",
  components: {
    LoginLayout,
    DocumentDetailOfMeInfo,
    AppPlaceholder,
    USafeBottom,
  },
  data() {
    return {
      /** 案件详情 */
      lawCaseRes: {},
      /** 律师信息 */
      lawyerInfo: {},
      /** 律师的完整信息 */
      lawyerInfoRes: {},
      /** 查询条件 */
      query: {},
    };
  },
  computed: {
    /** 律师服务的最低价格 */
    minPrice() {
      // 返回最低价格
      return (
        this.lawyerInfoRes.serviceManageV2VoList?.reduce((prev, cur) => {
          return prev.servicePrice < cur.servicePrice ? prev : cur;
        })?.servicePrice || 0
      );
    },
  },
  onLoad(query) {
    this.query = query || {};
    this.getCaseDetail();
  },
  methods: {
    /** 获取案件详情 */
    getCaseDetail() {
      lawyerLawCaseDetail(this.query).then((res) => {
        this.lawCaseRes = res.data.lawCaseRes || {};
        this.lawyerInfo = res.data.lawyerInfo || {};

        // 查询律师的完整信息
        oneLawyer({ id: this.lawyerInfo.id }).then((res) => {
          this.lawyerInfoRes = res.data || {};
        });
      });
    },
    /** 点击左边的按钮，跳转到问律师 */
    clickLeftBtn() {
      uni.navigateTo({
        url: "/pages/submit-question/index",
      });
    },
    /** 点击右边的按钮 */
    clickRightBtn() {
      toPayLawyerGuide({
        lawyerId: this.lawyerInfo.id,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.top-img {
  width: 100%;
  height: 84px;
  margin-bottom: -10px;
}
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  box-shadow: 0px -3px 10px 0px rgba(0, 0, 0, 0.08);
  border-radius: 16px 16px 0 0;
  opacity: 1;

  &__btn {
    padding: 12px 16px;

    &__left {
      width: 120px;
      height: 46px;
      border-radius: 49px;
      opacity: 1;
      border: 1px solid #eeeeee;
      font-size: 14px;
      font-weight: bold;
      color: #333333;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &__right {
      width: 207px;
      height: 46px;
      box-sizing: border-box;
      background: #3887f5;
      border-radius: 49px;
      opacity: 1;

      &__img {
        display: block;
        margin-left: 10px;
        width: 32px;
        height: 32px;
        border-radius: 16px;
        opacity: 1;
        flex-shrink: 0;
      }

      &__name {
        font-size: 14px;
        font-weight: bold;
        color: #ffffff;
      }

      &__price {
        font-size: 12px;
        font-weight: 400;
        color: #ffffff;
      }
    }
  }
}
</style>
