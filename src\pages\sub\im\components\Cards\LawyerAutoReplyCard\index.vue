<template>
  <theme-layout-card :theme="theme">
    <u-parse
      :content="customExts.title.replace('#FFFFFF','#3887F5')"
    />
  </theme-layout-card>
</template>

<script>
/* 自动回复卡片*/
import ThemeLayoutCard from "@/pages/sub/im/components/Cards/ThemeLayoutCard/index.vue";
import cardProps, { customComputed } from "@/pages/sub/im/mixins/card-props.js";
import uParse from "@/uview-ui/components/u-parse/u-parse.vue";

export default {
  name: "LawyerAutoReplyCard",
  components: { ThemeLayoutCard, uParse },
  mixins: [cardProps, customComputed]
};
</script>

<style scoped lang="scss">

</style>
