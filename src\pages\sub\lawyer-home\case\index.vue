<template>
  <div>
    <div class="tabs">
      <lawyer-home-tab @change="changeTabOne" />
    </div>
    <div class="case">
      <div class="content">
        <!-- 案件标的 -->
        <subject-case
          v-if="current === INDICATOR_NAME.caseAmt"
          :data="all[INDICATOR_NAME.caseAmt]"
        />
        <!-- 近期案件数 -->
        <number-recent-cases
          v-if="current === INDICATOR_NAME.recentCase"
          :data="all[INDICATOR_NAME.recentCase]"
        />
        <!-- 案由分布 -->
        <case-action
          v-if="current === INDICATOR_NAME.caseReason"
          :data="all[INDICATOR_NAME.caseReason]"
        />
        <!-- 常去法院 -->
        <frequent-court
          v-if="current === INDICATOR_NAME.courtName"
          :data="all[INDICATOR_NAME.courtName]"
        />
      </div>
    </div>
  </div>
</template>

<script>
import LawyerHomeTab from "@/pages/sub/lawyer-home/components/lawyer-home-case-statistics/lawyer-home-tab.vue";
import SubjectCase from "@/pages/sub/lawyer-home/components/lawyer-home-case-statistics/subject-case.vue";
import NumberRecentCases from "@/pages/sub/lawyer-home/components/lawyer-home-case-statistics/number-recent-cases.vue";
import getLawyerInfo from "@/pages/sub/lawyer-home/mixins/getLawyerInfo.js";
import CaseAction from "@/pages/sub/lawyer-home/components/lawyer-home-case-statistics/case-action.vue";
import caseStatistics from "@/pages/sub/lawyer-home/mixins/caseStatistics.js";
import FrequentCourt from "@/pages/sub/lawyer-home/components/lawyer-home-case-statistics/frequent-court.vue";
import { isObjNull } from "@/libs/basics-tools";

export default {
  name: "LawyerHomeCase",
  components: {
    FrequentCourt,
    CaseAction,
    NumberRecentCases,
    SubjectCase,
    LawyerHomeTab,
  },
  mixins: [getLawyerInfo, caseStatistics],
  watch: {
    lawyerInfo: {
      handler(value) {
        if (isObjNull(value)) return;
        this.getCaseStatisticsAll(this.tabList).then((datas) => {
          if (!isObjNull(datas)) {
            this.all = datas;
          }
        });
      },
      deep: true,
      immediate: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.tabs {
  background-color: #fff;
  padding: 8px 12px;
}

.case {
  padding: 12px 16px;
}

.content {
  background-color: #fff;
  padding: 12px 16px;
  border-radius: 8px;
}
</style>
