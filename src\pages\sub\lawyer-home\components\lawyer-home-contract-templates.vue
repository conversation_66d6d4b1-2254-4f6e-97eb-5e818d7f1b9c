<template>
  <div
    v-if="!isArrNull(contractList)"
    class="w-[343px] rounded-[8px] mx-auto bg-[#FFFFFF] my-[12px]"
  >
    <div class="px-[16px] py-[12px] flex items-center justify-between">
      <div class="text-[16px] font-bold text-[#333333]">
        合同模板
      </div>
      <div
        class="flex items-center"
        @click="clickLawyerCard"
      >
        <div class="text-[13px] text-[#666666]">
          全部
        </div>
        <img
          alt=""
          class="w-[16px] h-[16px] block"
          src="../img/arrow.png"
        >
      </div>
    </div>
    <div>
      <div class="px-[16px]">
        <div
          v-for="item in contractList"
          :key="item.id"
          class="border-0 [&:not(:last-child)]:border-b-[0.5px] border-solid border-[#EEEEEE] py-[16px]"
        >
          <contract-lawyer-item :data="item" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import ContractLawyerItem from "@/components/ContractLawyerItem/ContractLawyerItem.vue";
import { toContractTemplatesDetailLawyerPage } from "@/libs/turnPages";
import { isArrNull } from "@/libs/basics-tools";

export default {
  name: "LawyerHomeContractTemplates",
  components: { ContractLawyerItem },
  props: {
    lawyerInfo: {
      type: Object,
      default: () => ({}),
    },
    contractList: {
      type: Array,
      default: () => [],
      required: true,
    },
  },
  methods: {
    isArrNull,
    clickLawyerCard() {
      toContractTemplatesDetailLawyerPage({
        id: this.lawyerInfo.id,
      });
    },
  },
};
</script>
