<template>
  <!--下面有3种类型的消息 但是样式不一样 所以要分开写样式 但是逻辑都是一样的  -->
  <div>
    <!--  互助消息    -->
    <mutual-aid-messages
      v-if="type===SYSTEM_MSG_TYPE.INTERACTION"
      :currentEnum="getCurrentEnum"
      :data="data"
      @jump="jump"
    />
    <server-messages
      v-else-if="type===SYSTEM_MSG_TYPE.SERVER"
      :currentEnum="getCurrentEnum"
      :data="data"
      @jump="jump"
    />
    <!-- 服务消息和系统消息   -->
    <system-messages
      v-else
      :currentEnum="getCurrentEnum"
      :data="data"
      @jump="jump"
    />
  </div>
</template>

<script>

import { CHAT_MESSAGES_ENUM, SERVICE_MESSAGES_ENUM, SYSTEM_MESSAGES_ENUM } from "@/pages/sub/im/enum/systemMessages.js";
import { SYSTEM_MSG_TYPE } from "@/enum/imCardEnum.js";
import { parseTimeDiffToday } from "@/libs/tools.js";
import SystemMessages from "@/pages/sub/im/components/NoticeCard/systemMessages.vue";
import MutualAidMessages from "@/pages/sub/im/components/NoticeCard/mutualAidMessages.vue";
import ServerMessages from "@/pages/sub/im/components/NoticeCard/ServerMessages.vue";

const sysEnum = {
  [SYSTEM_MSG_TYPE.SERVER]: SERVICE_MESSAGES_ENUM,
  [SYSTEM_MSG_TYPE.SYSTEM]: SYSTEM_MESSAGES_ENUM,
  [SYSTEM_MSG_TYPE.INTERACTION]: CHAT_MESSAGES_ENUM
};
export default {
  name: "NoticeCard",
  components: { ServerMessages, MutualAidMessages, SystemMessages },
  props: {
    showAvatar: {
      type: Boolean,
      default: true
    },
    data: {
      type: Object,
      default: () => {
        return {};
      }
    },
    type: {
      type: String,
      default: ""
    }
  },
  computed: {
    SYSTEM_MSG_TYPE() {
      return SYSTEM_MSG_TYPE;
    },
    /* 获取当前类型得枚举*/
    getEnumerate() {
      return sysEnum[this.type] || {};
    },
    getMessageType(){
      if(this.data && this.data.messagePageType){
        return this.data.messagePageType;
      }

      return  "";
    },
    /* 获取当前枚举*/
    getCurrentEnum() {
      return this.getEnumerate[this.getMessageType];
    }
  },
  methods: {
    parseTimeDiffToday,
    jump(){
      const currentEnum = this.getCurrentEnum;

      if(currentEnum.clickHook){
        currentEnum.clickHook(this.data);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
</style>
