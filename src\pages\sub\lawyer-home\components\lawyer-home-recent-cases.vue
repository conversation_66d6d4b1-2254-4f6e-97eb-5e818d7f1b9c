<template>
  <div
    v-if="caseTotal"
    class="case-container"
  >
    <div class="header flex flex-space-between flex-align-center">
      <p class="header-title">
        近期案例
      </p>
      <div
        class="header-right flex flex-align-center"
        @click="toRecentCases"
      >
        全部
        <img
          alt=""
          class="header-icon"
          src="../img/arrow.png"
        >
      </div>
    </div>
    <div class="content">
      <div
        v-for="item in caseList"
        :key="item.id"
        class="content-item flex"
        @click="goDetail(item.id)"
      >
        <p class="content-item-icon" />
        <lawyer-ruling-document
          :data="item"
          class="flex-1"
        />
      </div>
    </div>
  </div>
</template>

<script>
import LawyerRulingDocument from "@/pages/sub/lawyer-home/components/lawyer-ruling-document.vue";
import { bigdataLawyerCaseList, lawyerLawCase } from "@/api/lawyer.js";
import {
  toLawyerHomeJudgment,
  toLawyerHomeJuDocumentDetail,
} from "@/libs/turnPages.js";
import { isObjNull } from "@/libs/basics-tools.js";

export default {
  name: "LawyerHomeRecentCases",
  components: { LawyerRulingDocument },
  props: {
    lawyerInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      /** 案件列表 */
      caseList: [],
      /** 案件总数 */
      caseTotal: 0,
      /* 是不是律师手动上传案例件*/
      isOfMeState: false,
    };
  },
  watch: {
    lawyerInfo: {
      handler(value) {
        if (isObjNull(value)) return;
        this.getCaseList();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    /** 获取案件列表 */
    getCaseList() {
      if (!this.lawyerInfo.realName || !this.lawyerInfo.lawyerOffice)
        return false;
      this.isOfMeState = false;

      if (!this.lawyerInfo.realName || !this.lawyerInfo.lawyerOffice) {
        this.getCaseListOfMe();
        return false;
      }

      const params = {
        lawyerName: this.lawyerInfo.realName,
        lawfirmName: this.lawyerInfo.lawyerOffice,
        currentPage: 1,
        pageSize: 3,
      };

      bigdataLawyerCaseList(params).then((res) => {
        this.caseList = this.caseList.concat(res.data.caseList || []);
        this.caseTotal = res.data.total || 0;
        if (this.caseTotal < 1) {
          this.getCaseListOfMe();
        } else {
          this.$store.commit("lawyerHome/CALL_TITLE_CALLBACK");
        }
      });
    },
    /* 获取律师手动上传案例件*/
    getCaseListOfMe() {
      this.isOfMeState = true;
      if (!this.lawyerInfo.id) return;
      /* lawyerId	是	string	律师ID
       currentPage	否	Integer	当前页码
       pageSize	否	Integer	每页大小*/
      lawyerLawCase({
        lawyerId: this.lawyerInfo.id,
        currentPage: 1,
        pageSize: 3,
      }).then(({ data }) => {
        this.caseTotal = data.total || 0;
        this.caseList = this.caseList.concat(data.records || []);
        this.$store.commit("lawyerHome/CALL_TITLE_CALLBACK");
      });
    },
    /** 跳转到近期案例 */
    toRecentCases() {
      toLawyerHomeJudgment({
        id: this.lawyerInfo.id,
      });
    },
    goDetail(id) {
      toLawyerHomeJuDocumentDetail({
        id,
        lawyerId: this.lawyerInfo.id,
        state: this.isOfMeState,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.case-container {
  width: 343px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 8px;
  opacity: 1;
  padding: 12px 16px 8px 16px;
  box-sizing: border-box;
}

.header {
  &-title {
    font-size: 16px;
    font-weight: bold;
    color: #333333;
  }

  &-right {
    font-size: 13px;
    font-weight: 400;
    color: #666666;
  }

  &-icon {
    width: 16px;
    height: 16px;
  }
}

.content {
  margin-top: 21px;
  position: relative;
  z-index: 10;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    display: block;
    clear: both;
    background-color: #eeeeee;
    width: 1px;
    opacity: 1;
    left: 1.9px;
    z-index: -1;
  }

  &-item {
    width: 299px;
    padding-bottom: 20px;

    &-icon {
      flex-shrink: 0;
      margin-right: 8px;
      margin-top: 7px;
      width: 4px;
      height: 4px;
      background: #3887f5;
      opacity: 1;
      border-radius: 50%;
    }
  }
}
</style>
