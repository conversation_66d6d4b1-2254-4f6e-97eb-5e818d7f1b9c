<template>
  <div class="phrases">
    <u--textarea
      v-model="msg"
      :showConfirmBar="false"
      maxlength="120"
      height="450rpx"
      placeholder="输入您的常用回复，请不要填写微信，电话等联系方式或广告信息，否则系统将封禁您的账号"
      count
    />
    <div
      class="btn"
      :class="{active:!disable}"
      @click="handleSubmit"
    >
      完成
    </div>
  </div>
</template>

<script>
import UTextarea from "@/uview-ui/components/u-textarea/u-textarea.vue";
import { isNull } from "@/libs/basics-tools.js";
import { quickReplyGet, quickReplyInsert, quickReplyUpdate } from "@/api/im.js";
export default {
  name: "Index",
  components: {
    "u--textarea": UTextarea,
  },
  data() {
    return {
      msg: "",
      quickId: ""
    };
  },
  onLoad({ id }) {
    /* 查看是否有id 有的话 就是修改*/
    if(isNull(id)) return;
    this.quickId = id;
    quickReplyGet().then(({ data = [] }) => {
      const findData = data.find(item => String(item.id) === String(id));
      this.msg = findData ? findData.text : "";
    });
  },
  computed: {
    disable() {
      return isNull(this.msg.trim());
    }
  },
  methods: {
    isNull,
    handleSubmit() {
      if(isNull(this.quickId)){
        quickReplyInsert({ text: this.msg }).then(() => {
          uni.showToast({
            title: "添加成功",
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1000);
        });
        return;
      }
      quickReplyUpdate({ id: this.quickId, text: this.msg }).then(() => {
        uni.showToast({
          title: "修改成功",
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1000);
      });
    }
  },
};
</script>

<style scoped lang="scss">
.phrases{
  padding: 12px 16px 0;
  min-height: 100vh;
  background: #FFFFFF;
  ::v-deep .u-textarea{
    border-radius: 4px 4px 4px 4px;
    padding: 8px;
    background: #F5F5F7;
    border: none;
    .u-textarea__field{
      color: #333333;
    }
  }
  ::v-deep .u-textarea__count{
    background: #F5F5F7 !important;
    bottom: 8px;
  }
  .btn{
    position: absolute;
    bottom: 46px;
    left: 50%;
    width: 343px;
    transform: translateX(-50%);
    font-size: 16px;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 44px;
    background: #A0CFFB;
    text-align: center;
    border-radius: 68px 68px 68px 68px;
    &.active{
      background: #3887F5;
    }
  }
}
</style>
