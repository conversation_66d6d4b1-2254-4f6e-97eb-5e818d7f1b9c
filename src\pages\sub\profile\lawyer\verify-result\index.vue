<template>
  <div>
    <div class="header-tips">
      <i class="iconfont icon-zhengque" />
      <div class="mg-l-8">
        当前执业认证已通过
      </div>
    </div>
    <div class="content">
      <div class="content-tip-title">
        执业信息
      </div>
      <div class="app-form">
        <u-form
          labelPosition="left"
          labelWidth="auto"
        >
          <div class="form-item">
            <u-form-item
              borderBottom
              label="认证身份"
            >
              <app-field
                v-model="ROLE_TYPE[certInfo.role]"
                border="none"
                placeholder=" "
              />
            </u-form-item>
            <u-form-item
              borderBottom
              label="姓名"
            >
              <app-field
                v-model="userInfo.realName"
                border="none"
                placeholder=" "
              />
            </u-form-item>
            <u-form-item
              borderBottom
              label="所在地区"
            >
              <app-field
                :value="certInfo.provinceName + certInfo.cityName"
                border="none"
                placeholder=" "
              />
              <!--              <app-area-->
              <!--                :defaultValue="lawyerCity"-->
              <!--                hasDefault-->
              <!--                @confirm="areaChange"-->
              <!--              >-->
              <!--                <app-field-->
              <!--                  :value="certInfo.provinceName + certInfo.cityName"-->
              <!--                  arrow-->
              <!--                  border="none"-->
              <!--                  placeholder=" "-->
              <!--                />-->
              <!--              </app-area>-->
            </u-form-item>
            <u-form-item
              borderBottom
              label="就职单位"
            >
              <app-field
                v-model="certInfo.lawyerOffice"
                border="none"
                placeholder=" "
              />
            </u-form-item>
            <u-form-item label="擅长领域">
              <app-field
                v-model="workFields"
                placeholder=" "
              />
              <!--              <app-areas-->
              <!--                :value="certInfo.workFields"-->
              <!--                @input="handleUpdateWorkFields"-->
              <!--              >-->
              <!--                <app-field-->
              <!--                  v-model="workFields"-->
              <!--                  arrow-->
              <!--                  placeholder=" "-->
              <!--                />-->
              <!--              </app-areas>-->
            </u-form-item>
          </div>
        </u-form>
      </div>
    </div>
    <div
      v-if="certificatePic.length > 0"
      class="content"
    >
      <div class="content-title">
        执业证照片
      </div>
      <div class="content-box">
        <img
          v-for="(item, index) in certificatePic"
          :key="index"
          :src="item"
          alt=""
          class="content-box__item"
          @click="handlePreviewImage(item)"
        >
      </div>
    </div>
    <div
      v-if="certInfo.licensePic"
      class="content"
    >
      <div class="content-title">
        律师事务所执业许可证照片
      </div>
      <div class="content-box">
        <img
          :src="certInfo.licensePic"
          alt=""
          class="content-box__item"
        >
      </div>
    </div>
    <verify-result-customer />
  </div>
</template>

<script>
import VerifyResultCustomer from "@/pages/sub/profile/lawyer/verify-result/components/VerifyResultCustomer.vue";
import UForm from "@/uview-ui/components/u-form/u-form.vue";
import AppField from "@/components/AppComponents/AppField/index.vue";
import UFormItem from "@/uview-ui/components/u-form-item/u-form-item.vue";
import { certInfo } from "@/api";
import { mapGetters } from "vuex";
import { ROLE_TYPE } from "@/enum";
import { handlePreviewImage, shareAppMessage } from "@/libs/tools";
import { updateWorkCity, updateWorkFields } from "@/api/lawyerProfile";
import { LAWYER_AUTH_PAGE_PATH } from "@/libs/turnPages";

export default {
  name: "ProfileLawyerVerifyResult",
  components: {
    UFormItem,
    AppField,
    UForm,
    VerifyResultCustomer,
  },
  data() {
    return {
      /** 认证信息 */
      certInfo: {},
    };
  },
  created() {
    this.getInfo();
  },
  onShareAppMessage() {
    return shareAppMessage({
      title:
          "邀请您入驻法临律师端，认证通过可得35法临币免费抢案源，点击完成认证",
      imageUrl: require("@/img/share/<EMAIL>"),
      path: LAWYER_AUTH_PAGE_PATH
    });
  },
  computed: {
    ROLE_TYPE() {
      return ROLE_TYPE;
    },
    workFields() {
      return (
        this.certInfo.workFields
          ?.map((item) => item.workFieldName)
          .join("、") || ""
      );
    },
    /** 律师执业证照片（多个英文逗号隔开） */
    certificatePic() {
      if (!this.certInfo.certificatePic) {
        return [];
      }

      if (this.certInfo.certificatePic.includes(",")) {
        return this.certInfo.certificatePic.split(",");
      }

      return [this.certInfo.certificatePic];
    },
    /** 城市 */
    lawyerCity() {
      return {
        cityName: this.certInfo?.cityName,
        cityCode: this.certInfo?.workCity,
        provinceCode: this.certInfo?.provinceCode,
        provinceName: this.certInfo?.provinceName,
      };
    },
    ...mapGetters({
      userInfo: "user/getUserInfo",
    }),
  },
  methods: {
    handlePreviewImage,
    /** 获取认证信息 */
    getInfo() {
      certInfo().then((res) => {
        this.certInfo = res.data;
      });
    },
    /** 修改擅长领域 */
    handleUpdateWorkFields(workFields) {
      updateWorkFields({
        workFields,
      }).then(() => {
        uni.showToast({
          title: "修改成功",
          icon: "none",
        });
        this.getInfo();
      });
    },
    /** 地区选择 */
    areaChange({ workCity, cityName }) {
      updateWorkCity({
        workCity,
        cityName,
      }).then(() => {
        uni.showToast({
          title: "修改成功",
          icon: "none",
        });
        this.getInfo();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/formStyles";

.header-tips {
  width: 343px;
  height: 40px;
  background: #ffffff;
  border-radius: 8px;
  opacity: 1;
  box-sizing: border-box;
  padding: 12px 16px;
  display: flex;
  align-items: center;

  font-size: 14px;
  font-weight: 400;
  color: #22bf7e;
  margin: 12px auto 0 auto;
}

.content {
  margin: 12px auto 0 auto;
  width: 343px;
  border-radius: 8px;
  opacity: 1;
  background-color: #fff;

  &-tip-title {
    font-size: 16px;
    font-weight: bold;
    color: #111111;
    padding: 12px 0;
    margin: 0 16px;
    border-bottom: 0.5px solid #eeeeee;
  }

  &-title {
    font-size: 16px;
    font-weight: bold;
    color: #111111;
    padding: 12px 16px;
  }

  &-box {
    display: flex;
    padding: 0 14px 12px 14px;

    &__item {
      display: block;
      width: 80px;
      height: 80px;
      border-radius: 8px;
      opacity: 1;
    }
  }
}

.form-item {
  padding: 0 16px;
}
</style>
