/**
 * 上传小程序 统一脚本入口
 * @description: 小程序上传
 */
const readline = require("readline");
const spawn = require("child_process").spawn;
const packageJson = require("./package.json");

const packageScripts = packageJson.scripts;

// 获取控制台输入
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

function question(query) {
  return new Promise((resolve) => {
    rl.question(query, (answer) => {
      resolve(answer);
    });
  });
}

function initNpmRun({ ENV, UNI_PLATFORM, FILE_NAME = UNI_PLATFORM, WARP_BAG }){
  const upload = `node miniProgramsUpload UNI_PLATFORM=${UNI_PLATFORM} FILE_NAME=${FILE_NAME}${ENV === "dev" ? "-test" : ""} UPLOAD_ENV=${ENV}`;

  if (WARP_BAG) {
    // 测试环境
    if (ENV === "dev") return `cross-env NODE_ENV=production UNI_OUTPUT_DIR=dist/dev/${FILE_NAME}-test uniapp-cli custom ${FILE_NAME} --mode test --minimize && ${upload}`;

    // 生产环境
    return `cross-env NODE_ENV=production UNI_OUTPUT_DIR=dist/build/${FILE_NAME} uniapp-cli custom ${FILE_NAME} && ${upload}`;
  }

  // 测试环境
  if (ENV === "dev") return `cross-env NODE_ENV=production UNI_PLATFORM=${UNI_PLATFORM} UNI_OUTPUT_DIR=dist/dev/${FILE_NAME}-test vue-cli-service uni-build --mode test --minimize && ${upload}`;

  // 生产环境
  return `cross-env NODE_ENV=production UNI_PLATFORM=${UNI_PLATFORM} vue-cli-service uni-build && ${upload}`;
}

function cmdInit({ ENV, UNI_PLATFORM, FILE_NAME, WARP_BAG }) {
  let name;

  // 是否是马甲包（自定义包）
  if (WARP_BAG) {
    // 测试环境
    if (ENV === "dev") {
      name = `upload:custom-${FILE_NAME}-test`;
    } else {
      // 生产环境
      name = `upload:custom-${FILE_NAME}`;
    }
  } else {
    // 测试环境
    if (ENV === "dev") {
      name = `upload:${FILE_NAME || UNI_PLATFORM}-test`;
    } else {
      // 生产环境
      name = `upload:${FILE_NAME || UNI_PLATFORM}`;
    }
  }

  // 如果没有这个命令，则添加
  if (!packageScripts[name]) {
    packageScripts[name] = initNpmRun({ ENV, UNI_PLATFORM, FILE_NAME, WARP_BAG });

    // 写入package.json
    require("fs").writeFileSync("./package.json", JSON.stringify(packageJson, null, 2));
  }

  return name;
}


(async () => {

  const cmdConfig = {
    /** 微信上传 */
    "1": {
      "1": cmdInit({ ENV: "dev", UNI_PLATFORM: "mp-weixin" }),
      "2": cmdInit({ ENV: "build", UNI_PLATFORM: "mp-weixin" }),
    }
  };

  const env = await question(`请输入数字选择登陆方式：
1. 测试环境
2. 生产环境
`);


  let cmd;
  // 如果是头条登陆
  if (env === "3") {
    cmd = "node miniProgramsUpload/toutiao/login.js";
  } else {
    const type = await question(`请输入数字选择登陆方式：
1. 微信上传
`);

    cmd = `npm run ${cmdConfig[type][env]}`;
  }

  const result = spawn("cmd.exe", ["/s", "/c", cmd]);

  result.stdout.on("data", (data) => {
    console.log("stdout: " + data);
  });

  result.stderr.on("data", (data) => {
    console.log("stderr: " + data);
  });

  result.on("close", (code) => {
    console.log("脚本执行完毕，退出码为：" + code);
  });

  // 监听手动终止信号
  process.on("SIGINT", function () {
  });

})();
