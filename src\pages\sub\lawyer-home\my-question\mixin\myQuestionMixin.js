import { toLawyerHome, toPayLawyerGuide } from "@/libs/turnPages.js";

export default {
  computed: {
    getTime() {
      return this.details.createTime?.split(" ")[0];
    },
    /** 回复内容 */
    answerContent() {
      const replies = JSON.parse(JSON.stringify(this.details.replies || []));

      // * 如果有被采纳的回复则将其放在第一位
      const adoptIndex = replies.findIndex((item) => item.hasAccepted === 1);

      if (adoptIndex !== -1) {
        const adoptItem = replies.splice(adoptIndex, 1);
        replies.unshift(adoptItem[0]);
      }

      return replies;
    },
    /** 是否有采纳 */
    hasAdopt() {
      return this.details.replies?.some((item) => item.hasAccepted === 1);
    },
    /** 该问答是否付过费 */
    qaMessagePay() {
      return this.details.qaMessagePayState === 1;
    },
    /**
     * 是否显示采纳按钮
     * 没有采纳 && 付过费
     * @return {boolean}
     */
    showAdoptBtn() {
      return !this.hasAdopt && this.qaMessagePay;
    },
  },
  methods: {
    toAsk(id) {
      toPayLawyerGuide({ lawyerId: id });
    },
    /** 跳转到律师主页 */
    toLawyerHome(id) {
      toLawyerHome({
        id,
      });
    },
  },
};
