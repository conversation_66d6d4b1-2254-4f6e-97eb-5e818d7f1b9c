import { isNull } from "@/libs/basics-tools.js";
import { CaseSourceStatus } from "@/enum";

const  caseInfoComputed = {
  computed: {
    /* 案源id*/
    caseSourceServerV2Id() {
      return this.caseInfo.id;
    },
    /* 获取案源信息V2*/
    caseSourceV2Info() {
      return (this.caseInfo && this.caseInfo.caseSourceV2) || {};
    },
    /* 案源 服务信息*/
    caseSourceServiceInfo() {
      return this.caseInfo.serviceInfo || {};
    },
    /* 案源服务状态*/
    /* 状态（0待锁定，1服务中，2待完成，3已完成，9取消锁定）*/
    serverStatus() {
      return this.caseInfo.status;
    },
    /** 是否是案源 */
    isCaseInfoItTheSourceOfTheCase() {
      return this.caseSourceV2Info.type === 0;
    },
    /* 是否支付*/
    isCaseInfoOrderPay() {
      return this.caseSourceV2Info.payState === 1;
    },
    /* 是不是问答 6 新版用户追问,5留言问答的追问 3：用户问答创建追问 */
    isCaseInfoQuestionClosely() {
      return [3, 5, 6].includes(this.caseInfo.createFrom);
    },
    /* 公共派单*/
    isCaseInfoPublicDispatch() {
      return this.caseInfo.createFrom === 2;
    },
    /* 公共派单的付费*/
    isCaseInfoPayPublicDispatch() {
      return this.isCaseInfoOrderPay && this.isCaseInfoPublicDispatch;
    },
    /* 是不是一对一咨询*/
    isCaseInfoLawyerConsulOneToOne() {
      return this.caseSourceV2Info.type === 1;
    },
    /** 已付费，并且是一对一咨询 */
    isCaseInfoPayOneToOne() {
      return (this.isCaseInfoOrderPay && this.isCaseInfoLawyerConsulOneToOne);
    },
    /** 未付费，并且是一对一咨询 */
    isCaseInfoNotPayOneToOne() {
      return (!this.isCaseInfoOrderPay && this.isCaseInfoLawyerConsulOneToOne);
    },
    /* 服务评价状态（1已评价，0未评价）*/
    getEvaluateStatus() {
      return this.caseInfo.evaluateStatus;
    },
    /** 是否是评价状态 */
    isCommentState() {
      return (this.getEvaluateStatus === 1 || this.serverStatus === CaseSourceStatus.CASE_SOURCE_STATUS_FINISHED || this.serverStatus === CaseSourceStatus.CASE_SOURCE_STATUS_CANCEL_LOCK);
    },
    /* 待评价*/
    isCaseInfoWaitEvaluate() {
      return this.serverStatus === CaseSourceStatus.CASE_SOURCE_STATUS_FINISHED && this.getEvaluateStatus === 0;
    },
    /* 已评价*/
    isCaseInfoEvaluate() {
      return this.serverStatus === CaseSourceStatus.CASE_SOURCE_STATUS_FINISHED && this.getEvaluateStatus === 1;
    },
    /* 用户换律师审核状态[0.待审核,1.审核通过,2.审核不通过,9.用户撤销申请]*/
    isCaseInfoUserChangeLawyer() {
      return isNull(this.caseInfo.userChangeLawyerCheckStatus) ? "" : this.caseInfo.userChangeLawyerCheckStatus;
    }
  }
};

/* im当前页面没有inject 单独写*/
export const imCaseInfoStateProps = {
  mixins: [caseInfoComputed]
};



/* 安源信息*/
export const caseInfoSymbol = "_caseInfo";
/* 更新案源*/
export const updateCaseInfoSymbol = "_updateCaseInfo";

/* 会话id*/
export const conversationIdSymbol = "_conversationId";
/* 会话信息 */
export const conversationInfoSymbol = "_conversationInfo";


/* 整个会话im的this */
export const sessionIMThisSymbol = "_sessionIMThisSymbol";

/* im 子组件inject通过获取案源信息*/
export const caseInfoStateProps = {
  inject: [caseInfoSymbol],
  mixins: [caseInfoComputed],
  computed: {
    caseInfo(){
      return this[caseInfoSymbol]();
    }
  }
};

export const conversationIdSymbolProps = {
  inject: [conversationIdSymbol],
  computed: {
    conversationId(){
      return this[conversationIdSymbol]();
    }
  }
};

// 整个会话im的this
export const sessionIMThisSymbolProps = {
  inject: [sessionIMThisSymbol],
  methods: {
    // 这里使用的方法 没有使用computed是为了防止数据太多
    getSessionIMThis(){
      return this[sessionIMThisSymbol]();
    }
  }
};

/* 会话信息 */
export const conversationInfoStateProps = {
  inject: [conversationInfoSymbol],
  computed: {
    getConversationInfo(){
      return this[conversationInfoSymbol]();
    }
  },
  watch: {
    /* 因为会话信息是异步请求的 这里加一个监听 */
    getConversationInfo: {
      deep: true,
      immediate: true,
      handler(val){
        // 这里判断 会话信息有没有获取到
        if(val.getOtherToken){
          this.conversationInfoInit && this.conversationInfoInit(val);
        }
      }
    }
  }
};
