import { requestCommon, requestCore } from "@/libs/axios";

/* 数据字典*/
export const dataDictionary = (data) =>
  requestCore.post("/dataDetail/list", data);

/** 获取系统参数 */
export const getCommonConfigKey = (data) =>
  requestCore.post("/common/config/key", data);

export const getArea = (data) => requestCore.post("/area/getArea", data); // 省市区县列表查询接口

export const caseSourceV2Clues = (data) =>
  requestCommon.post("/paralegal/orderCenter/caseSourceV2Clues", data);
export const noCheckCaseSourceV2Clues = (data) =>
  requestCommon.post("/paralegal/orderCenter/noCheck/caseSourceV2Clues", data);
export const newRecommendPage = (data) =>
  requestCommon.post("/paralegal/orderCenter/newRecommendPage", data);
export const noCheckNewRecommendPage = (data) =>
  requestCommon.post("/paralegal/orderCenter/noCheck/newRecommendPage", data);
export const orderCenterSourceDetail = (data) =>
  requestCommon.post(`/paralegal/orderCenter/orderCenterSourceDetail/${data}`);
export const lawyerCaseSourceDetail = (data) =>
  requestCommon.post(`/paralegal/orderCenter/lawyerCaseSourceDetail/${data}`);
export const completeCaseServer = (data) =>
  requestCommon.post(
    `/paralegal/caseSourceServerV2/completeCaseServer/${data}`
  );
export const remainClueNum = (data) =>
  requestCommon.post("/paralegal/lawyerCluePack/remainClueNum", data);
export const getGoodsTypeGrabScope = () =>
  requestCommon.post("/paralegal/orderCenter/getGoodsTypeGrabScope");
/** 返回咨询助手的相关信息 */
export const consultingAssistant = () =>
  requestCommon.post("/paralegal/lawyerCaseSource/consultingAssistant");
/** 获取律师执业认证信息 */
export const certInfo = () => requestCommon.post("/paralegal/cert/info");
/** 合同上传 */
export const contactUpload = (data) =>
  requestCommon.post("/paralegal/lawyerContract/insert", data);

/** 数据字典明细查询 */
export const dataDetailList = (data) =>
  requestCore.post("/dataDetail/list", data);

/** 根据位置获取广告(C端广告) */
export const advertListPosition = (data) =>
  requestCore.post("/advert/list/position", data);

/**
 * 根据城市名称，模糊 查询省份code和城市code
 */
export const getAreaLikeCityName = (params) =>
  requestCommon.post("/core/area/getAreaLikeCityName", params);
export const listAndBalance = (params) =>
  requestCommon.post(
    "/paralegal/v2/lawyerRechargeService/listAndBalance",
    params
  );
export const getLocalStatus = (params) =>
  requestCommon.post(
    "/paralegal/privilege/get/CASE_SOURCE_V2_LOCAL_STATUS",
    params
  );
export const updateNewPrivilegeValue = (params) =>
  requestCommon.post(
    "/paralegal/lawyerPrivilege/updateNewPrivilegeValue",
    params
  );

/** 分享数据字典 */
export const shareDataDictionary = () => {
  const groupCode = "B_MINI_SHARE";

  return requestCore
    .post("/dataDetail/list", { groupCode: groupCode })
    .then((res) => {
      return res.data.map((item) => {
        try {
          const remark = JSON.parse(item.remark);
          return {
            ...item,
            remark,
          };
        } catch (e) {
          console.log(e);
          return item;
        }
      });
    });
};

/** 获取微信urlScheme */
export const urlSchemeGenerate = (data) =>
  requestCommon.post("/core/user/wechat/urlScheme/generate", data, {
    failToast: false,
  });

/** 获取微信UrlLink */
export const generateUrlLink = (data) =>
  requestCommon.post("/core/user/wechat/generateUrlLink", data, {
    failToast: false,
  });

/** 生成跳转到小程序的链接 */
export const generateToWeChat = (data, options = {}) => {
  const { header = {}, type } = options;

  if (type === "scheme") {
    return urlSchemeGenerate(data, header);
  }

  return generateUrlLink(data, header);
};

/** 律师上传成单喜报详情 */
export const goodNewsLawyerUploadDetail = (data) =>
  requestCommon.post("/paralegal/goodNewsLawyerUpload/detail", data);

/** 律师首页成单喜报轮播列表 */
export const rollingInfo = () =>
  requestCommon.post("/paralegal/goodNewsLawyerUpload/rollingInfo");

/** 成案喜报分页查询 */
export const goodNewsPage = (data) =>
  requestCommon.post("/paralegal/goodNews/page", data);

/** 修改成单喜报浏览量 */
export const updateGoodNewsDetailPv = (data) =>
  requestCommon.post(
    "/paralegal/goodNewsLawyerUpload/updateGoodNewsDetailPv",
    data
  );

/** 沾沾喜气获取红包 */
export const getRedPacket = (data) =>
  requestCommon.post("/paralegal/goodNewsLawyerUpload/getRedPacket", data);

/** 成单喜报红包领取滚动 */
export const redPacketMoment = () =>
  requestCommon.post("/paralegal/goodNewsLawyerUpload/redPacketMoment");

/** 律师最新成单喜报和今日瓜分沾沾喜气红包律师数 */
export const getLatestGoodNews = () =>
  requestCommon.post("/paralegal/goodNewsLawyerUpload/getLatestGoodNews");

/** 律师工具小程序-今日问答数据 */
export const todayQaMessageInfo = () =>
  requestCommon.post("/info/qaMessage/lawyerTools/todayQaMessageInfo");

/** 问答列表 */
export const qaMessagePageList = (data) =>
  requestCommon.post("/info/qaMessage/pageList", data); /** 问答列表 */

/** 查询律师手机号 */
export const lawyerPhone = (data) =>
  requestCommon.post("/lawyer/lawyer/lawyerPhone", data);

export const lawyerSpeciality = () => {
  return dataDictionary({ groupCode: "LAWYER_SPECIALITY" }).then(({ data }) => {
    return data?.map((item) => {
      try {
        return {
          ...item,
          ext: JSON.parse(item.supplementDesc) || {},
        };
      } catch (e) {
        return {
          ...item,
          ext: {},
        };
      }
    });
  });
};

/** 前端轮训修改最后在线时间 */
export const lastOnlineTime = (data) =>
  requestCommon.post("/paralegal/user/online/lastOnlineTime", data);

/**
 * 我的协作发布次数
 * https://showdoc.imlaw.cn/web/#/5/3883
 */
export const lawyerCollaborationQueryPubCount = () =>
  requestCommon.post("/paralegal/lawyerCollaboration/queryPubCount");
