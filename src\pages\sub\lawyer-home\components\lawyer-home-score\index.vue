<template>
  <div class="law-score">
    <div class="score-header px-[16px] pt-[16px] flex flex-align-center">
      <div class="flex flex-align-center">
        <p class="score-header-num">
          {{ average }}
        </p>
        <p class="score-header-unit">
          分
        </p>
        <p class="score-header-line" />
      </div>
      <u-rate
        :value="average"
        activeColor="#F2AF30"
        allowHalf
        inactiveColor="#ffe6be"
        inactiveIcon="star-fill"
        readonly
        size="48rpx"
      />
    </div>
    <div
      class="flex flex-space-between px-[16px] py-[12px] border-0 border-solid border-b-[0.5px] border-[#EEEEEE]"
    >
      <div class="score-title">
        用户评价 <span class="score-title-num">({{ total }}条)</span>
      </div>
      <div
        class="show-all flex flex-align-center flex-space-center"
        @click="toAllEvaluate"
      >
        全部
        <img
          alt=""
          class="show-all-icon"
          src="../../img/arrow.png"
        >
      </div>
    </div>
    <div class="score-container">
      <div>
        <div
          v-for="item in evaluateList"
          :key="item.id"
          class="score-item"
        >
          <lawyer-home-score-item :data="item" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getEvaluatePage, scoreCountLawyers } from "@/api/lawyer.js";
import LawyerHomeScoreItem from "@/pages/sub/lawyer-home/components/lawyer-home-score/lawyer-home-score-item.vue";
import URate from "@/uview-ui/components/u-rate/u-rate.vue";
import { toLawyerHomeComment } from "@/libs/turnPages.js";
import { isObjNull } from "@/libs/basics-tools.js";
import store from "@/store";

export default {
  name: "LawyerHomeScore",
  components: { URate, LawyerHomeScoreItem },
  props: {
    lawyerInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      /** 平均分 */
      average: 0,
      /** 服务条数 */
      total: 0,
      /** 服务评价列表 */
      evaluateList: [],
    };
  },
  watch: {
    lawyerInfo: {
      handler(val) {
        if (isObjNull(val)) return;

        this.getScore();
        this.getEvaluatePage();
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    /** 获取得分 */
    getScore() {
      if (!this.lawyerInfo.id) return;

      scoreCountLawyers({ lawyerId: this.lawyerInfo.id }).then(({ data }) => {
        this.average = data || 0;
      });
    },
    /** 获取律师的评价列表 */
    getEvaluatePage() {
      if (!this.lawyerInfo.id) return;

      getEvaluatePage({
        lawyerId: this.lawyerInfo.id,
        pageSize: 5,
      }).then(({ data }) => {
        this.total = data.total;
        store.commit("lawyerHome/SET_EVALUATE_COUNT", this.total);
        this.evaluateList = data.records || [];
      });
    },
    /** 查看全部 */
    toAllEvaluate() {
      toLawyerHomeComment({
        id: this.lawyerInfo.id,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.law-score {
  width: 343px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 8px;
}

.score-header {
  opacity: 1;
  box-sizing: border-box;
  border-radius: 8px 8px 0 0;

  &-num {
    font-size: 32px;
    font-weight: bold;
    color: #333333;
  }

  &-unit {
    margin-left: 8px;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    margin-top: 20px;
  }

  &-line {
    margin: 0 12px;
    height: 18px;
    opacity: 1;
    border: 1px solid;
    border-image: radial-gradient(
        circle,
        rgba(255, 233, 198, 1),
        rgba(255, 226, 199, 1)
      )
      1 1;
  }
}

.score-title {
  font-size: 16px;
  font-weight: bold;

  &-num {
    margin-left: 6px;
    font-size: 14px;
    font-weight: 400;
    color: #999999;
  }
}

.score-container {
  padding: 0 16px;
}

.score-item {
  margin-top: 12px;
  padding-bottom: 12px;

  &:not(:last-child) {
    border-bottom: 1px solid #f5f5f5;
  }
}

.show-all {
  font-size: 14px;
  font-weight: 400;
  color: #666666;

  &-icon {
    width: 16px;
    height: 16px;
  }
}
</style>
