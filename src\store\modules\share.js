const state = {
  // 是否正在分享
  isSharing: false,
  // 分享完成后的回调函数
  shareCallback: null
};

const mutations = {
  // 设置分享状态
  SET_SHARING_STATUS(state, status) {
    console.log("设置分享状态", status);
    state.isSharing = status;
  },
  // 设置分享回调
  SET_SHARE_CALLBACK(state, callback) {
    state.shareCallback = callback;
  },
  // 清除分享回调
  CLEAR_SHARE_CALLBACK(state) {
    state.shareCallback = null;
  }
};

const actions = {
  // 分享完成处理
  handleShareComplete({ commit, state }) {
    if (state.isSharing) {
      // 执行回调
      if (state.shareCallback && typeof state.shareCallback === "function") {
        try {
          state.shareCallback();
        } catch (error) {
          console.error("分享回调执行错误:", error);
        }
      }
      // 清除回调和状态
      commit("CLEAR_SHARE_CALLBACK");
      commit("SET_SHARING_STATUS", false);
    }
  },
};

const getters = {
  isSharing: state => state.isSharing,
  shareCallback: state => state.shareCallback
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}; 