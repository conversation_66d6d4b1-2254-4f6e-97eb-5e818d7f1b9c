<template>
  <div>
    <app-popup
      :round="10"
      :safeAreaInsetBottom="false"
      :show="show"
      borderRadius="14"
      mode="center"
    >
      <view class="logout">
        <view class="flex-1 top">
          <view class="logout-title">
            退出账户
          </view>
          <view class="logout-info">
            确定要退出当前账户吗？
          </view>
        </view>
        <view class="logout-button">
          <view
            class="logout-btn"
            @click="$emit('cancel')"
          >
            取消
          </view>
          <view
            class="logout-btn"
            @click="$emit('config')"
          >
            确定
          </view>
        </view>
      </view>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";

export default {
  name: "MineLawyerLoginOutPopup",
  components: { AppPopup },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style scoped lang="scss">
.logout {
  width: 295px;
  height: 162px;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .top {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  &-title {
    font-size: 16px;
    font-weight: bold;
    color: #111111;
    text-align: center;
  }

  &-info {
    text-align: center;
    font-size: 15px;
    color: #6b6b6f;
    margin-top: 10px;
  }

  &-button {
    height: 50px;
    display: flex;
    border-top: 1px solid #ececec;
    font-size: 16px;

    .logout-btn {
      line-height: 50px;
      flex: 1;
      text-align: center;

      &:nth-child(1) {
        border-right: 1px solid #ececec;
        color: #46474b;
      }

      &:nth-child(2) {
        color: #3887F5;
      }
    }
  }
}
</style>
