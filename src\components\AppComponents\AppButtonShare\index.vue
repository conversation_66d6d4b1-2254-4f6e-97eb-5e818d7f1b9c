<template>
  <div class="w-full h-full">
    <button
      open-type="share"
      @click="handleClick"
    >
      <slot />
    </button>
  </div>
</template>

<script>

export default {
  name: "AppButtonShare",
  props: {
    /* 埋点*/
    buryThePoint: {
      type: String,
      default: ""
    },
  },
  methods: {
    handleClick(){
      this.$emit("click");
    }
  }
};
</script>

<style lang="scss" scoped>
// 重置按钮样式
button {
  width: 100%;
  height: 100%;
  background: none;
  border-radius: 0;
  border: none;
  outline: none;
  padding: 0;
  margin: 0;
  font-size: inherit;
  color: inherit;
  display: block;
}

// 去除微信小程序button的默认边框
button::after {
  border: none;
}
</style>
