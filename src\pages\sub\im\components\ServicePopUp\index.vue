<template>
  <app-popup
    round="32rpx"
    :show="getShow"
  >
    <div class="title flex flex-align-center flex-space-between">
      <p class="placeholder" />
      主动发送您的1V1付费服务
      <i
        class="iconfont icon-guanbi"
        @click="close"
      />
    </div>
    <div class="server-container">
      <div
        v-for="i in list"
        :key="i.id"
        class="server-item flex flex-space-between flex-align-center"
      >
        <img
          class="logo"
          :src="i.icon"
          alt=""
        >
        <div class="flex flex-1 flex-column ">
          <p class="server-name">
            {{ i.serviceName }}
          </p>
          <p class="server-spec">
            规则：{{ i.serviceNum }}{{ i.unitLabel }}
          </p>
        </div>
        <div
          class="btn"
          @click.stop="handleSendServe(i)"
        >
          发送
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { serviceManegeGetVipServiceList } from "@/api/user.js";

export default {
  name: "ServicePopUp",
  components: { AppPopup },
  props: {
    show: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      list: []
    };
  },
  computed: {
    getShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit("update:show", val);
      }
    }
  },
  methods: {
    close(){
      this.$emit("update:show", false);
    },
    handleSendServe(data) {
      this.$emit("update:show", false);
      this.$emit("sendServe", data);
    }
  },
  watch: {
    show(val){
      if(val){
        serviceManegeGetVipServiceList().then(({ data = [] }) => {
          this.list = data;
        });
      }
    }
  }
};
</script>

<style scoped lang="scss">
.title{
  padding: 0 16px;
  font-size: 16px;
  height: 50px;
  font-weight: 500;
  color: #000000;
  .placeholder{
    width: 24px;
    height: 24px;
  }
  .iconfont{
    font-size: 24px;
    color: #000000;
  }
}
.server-container{
  padding:12px;
  overflow: hidden;
  max-height: 400px;
  overflow-y: auto;
}
.server-item{
  padding:13px 12px;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #EEEEEE;
  margin-top: 16px;
  &:first-child{

    margin-top: 0;
  }
  .logo{
    width: 44px;
    height: 44px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 12px;
  }
  .server-name{
    font-size: 16px;
    font-weight: 600;
    color: #333333;
  }
  .server-spec{
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    padding-top: 4px;
  }
  .btn{
    width: 80px;
    line-height: 32px;
    text-align: center;
    background: #3887F5;
    border-radius: 68px 68px 68px 68px;
    font-size: 14px;
    font-weight: 400;
    color: #FFFFFF;
  }
}
</style>
