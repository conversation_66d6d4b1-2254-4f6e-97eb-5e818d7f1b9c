<template>
  <div>
    <div class="text-[16px] font-bold text-[#333333] mt-[32px]">
      是否原创
    </div>
    <div class="mt-[8px] flex items-center space-x-[16px]">
      <div
        v-for="item in list"
        :key="item.value"
        :class="[
          'relative w-[87px] h-[36px] rounded-[8px] border-[1px] border-solid border-[#CCCCCC] text-[13px] text-[#333333] flex items-center justify-center',
          { '!border-[#3887F5] !bg-[#EBF3FE] !text-[#3887F5]': value === item.value },
        ]"
        @click="selectOriginality(item)"
      >
        <img
          v-if="value === item.value"
          alt=""
          class="absolute -right-[1px] -bottom-[1px] w-[16px] h-[16px] block"
          src="../../img/1.png"
        >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>
<script>


export default {
  name: "ContactOriginality",
  props: {
    value: {
      type: [Number, String],
      required: true,
      default: 0,
    },
  },
  data() {
    return {
      list: [
        {
          label: "原创",
          value: 1
        },
        {
          label: "非原创",
          value: 0
        }
      ]
    };
  },
  methods: {
    /**
     * 选择是否原创
     */
    selectOriginality(item) {
      this.$emit("input", item.value);
    },
  },
};
</script>

<style lang="scss" scoped></style>
