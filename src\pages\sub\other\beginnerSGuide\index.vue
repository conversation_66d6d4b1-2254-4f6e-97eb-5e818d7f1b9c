<template>
  <div>
    <div class="header position-relative">
      <img
        class="background-image"
        src="img/<EMAIL>"
      >
      <div class="video-container">
        <img
          v-if="!playState"
          alt=""
          class="placeholder"
          src="img/<EMAIL>"
          @click="player"
        >
        <video
          v-else
          id="myVideo"
          :show-center-play-btn="false"
          controls
          loop
          src="https://oss.imlaw.cn/test/videouniapp/2023/07/03/2ee972b6e0c540fda51773c64e00fdd0.mp4"
        />
      </div>
    </div>
    <u-sticky>
      <div class="tab">
        <u-tabs
          :current="current"
          :list="list1"
          :scrollable="false"
          @change="handleChange"
        />
      </div>
    </u-sticky>
    <div class="image-container">
      <!-- 合作模式     -->
      <div v-if="current===0">
        <img
          alt=""
          class="image-view"
          mode="widthFix"
          src="img/<EMAIL>"
        >
        <div class="background-white overflow-hidden">
          <div
            class="btn"
            @click="turnToRechargePage"
          >
            立即体验
          </div>
          <u-safe-bottom />
        </div>
      </div>
      <!--     关于法临 -->
      <div v-else>
        <img
          alt=""
          class="image-view"
          mode="widthFix"
          src="img/<EMAIL>"
        >
        <img
          alt=""
          class="image-view"
          mode="widthFix"
          src="img/<EMAIL>"
        >
      </div>
    </div>
  </div>
</template>

<script>
import UTabs from "@/uview-ui/components/u-tabs/u-tabs.vue";
import USticky from "@/uview-ui/components/u-sticky/u-sticky.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { turnToRechargePage } from "@/libs/turnPages.js";

export default {
  name: "Index",
  components: { USafeBottom, USticky, UTabs },
  data() {
    return {
      playState: false,
      current: 0,
      list1: [{
        name: "合作模式",
      }, {
        name: "关于法临",
      }]
    };
  },
  methods: {
    turnToRechargePage,
    player(){

      this.playState = true;
      this.$nextTick(() => {
        const videoContext = uni.createVideoContext("myVideo");
        videoContext.play();
      });
    },
    handleChange({ index }){
      this.current = index;
    }
  }
};
</script>

<style lang="scss" scoped>
.opacity-none{
  opacity: 0;
}
.header{
  width: 375px;
  height: 352px;
  .video-container{
    padding-top: 114px;
    text-align: center;
    .placeholder{
      margin: 0 auto;
      width:340px;
      height: 190px;
    }
    #myVideo{
      margin: 0 auto;
      width:340px;
      height: 190px;
    }
  }
}
.tab{
  margin-top: -30px;
  height: 44px;
  background: white;
  border-radius: 16px 16px 0px 0px;
}
.image-container{
  font-size: 0;
  overflow: hidden;
  .image-view{
    width: 100%;
  }
}
.btn{
  margin: 0 auto 8px;
  width: 343px;
  line-height: 44px;
  background: #3887F5;
  border-radius: 68px 68px 68px 68px;
  font-size: 16px;
  text-align: center;
  font-weight: 500;
  color: #FFFFFF;
}
</style>
