<template>
  <div>
    <div :style="style" />
    <u-safe-bottom v-if="showSafe" />
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { pxToScreenPx } from "@/libs/tools";

export default {
  name: "AppPlaceholder",
  components: { USafeBottom },
  props: {
    height: {
      type: [Number, String],
      default: 65,
    },
    /** 是否显示安全区域 */
    showSafe: {
      type: Boolean,
      default: true,
    },
    autoHeight: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    style() {
      let height = pxToScreenPx(uni.$u.getPx(this.height));

      return `height: ${height}px;`;
    },
  },
};
</script>

<style lang="scss" scoped></style>
