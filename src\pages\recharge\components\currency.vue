<template>
  <div class="currency">
    <div class="wrapper">
      <top-card
        :info="cardInfo"
        @numberClick="turnToMyWallet"
        @tipClick="turnToMyWallet"
      />
      <div class="card-wrapper">
        <div
          v-for="item in rechargeList"
          :key="item.id"
          class="card flex flex-align-center flex-space-center"
          :class="{active: current.id === item.id}"
          @click="cardClick(item)"
        >
          <div>
            <div class="price">
              {{ priceNumber(item.rechargeAmount) }}元
            </div>
            <div class="other">
              送{{ parseInt(item.giftAmount / 100) }}法临币
            </div>
            <img
              src="@/pages/recharge/img/VIPselect@2x(1).png"
              alt=""
              class="active-img"
            >
          </div>
        </div>
      </div>
      <div
        class="coupons flex flex-align-center flex-space-between"
        @click="couponsClick"
      >
        <span>现金券</span>
        <span
          v-if="couponList.length"
          class="selected"
        >
          -¥{{ priceNumber(couponInfo.spec) }}
          <span class="iconfont icon-erjiyoujiantou" />
        </span>
        <span
          v-else
          class="selected no-data"
        >暂无可用优惠券</span>
      </div>
    </div>
    <div class="fixed-btn">
      <div class="btn-wrapper ">
        <div
          class="btn"
          @click="toPay"
        >
          立即支付 ¥{{ priceNumber(payMoney) }}
        </div>
        <div
          class="xy flex flex-align-center"
          @click="check=!check"
        >
          <img
            v-if="!check"
            class="img"
            src="@/pages/recharge/img/check.png"
            alt=""
          >
          <img
            v-if="check"
            class="img"
            src="@/pages/recharge/img/check-active.png"
            alt=""
          >
          支付即代表同意 <span
            class="color-text"
            @click.stop="clickXy"
          >《法临币充值协议》</span>
        </div>
      </div>
      <coupon-popup
        :show.sync="couponShow"
        :list="couponList"
        :couponInfoProp="couponInfo"
        @selectedCoupon="selectedCoupon"
      />
      <u-safe-bottom />
    </div>
  </div>
</template>

<script>
import TopCard from "@/pages/recharge/components/topCard.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { listAndBalance } from "@/api";
import { priceNumber } from "@/libs/tool";
import { handleOrderPay } from "@/libs/pay";
import { pageCoupon } from "@/api/order";
import CouponPopup from "@/components/couponPopup/index.vue";
import { turnToMyWallet, turnToWebViewPage } from "@/libs/turnPages";
import { WebAddress } from "@/enum";
import {isNull} from "@/libs/basics-tools";

export default {
  components: { CouponPopup, USafeBottom, TopCard },
  data() {
    return {
      cardInfo: {
        title: "法临币余额",
        number: 0,
        type: "price"
      },
      check: false,
      couponShow: false,
      rechargeList: [],
      current: {},
      couponList: [],
      couponInfo: {
        id: null,
        spec: 0,
      },
    };
  },
  computed: {
    payMoney () {
      const res = this.current.rechargeAmount - this.couponInfo.spec;
      return res > 0 ? res : 0;
    }
  },
  methods: {
    turnToMyWallet,
    priceNumber,
    cardClick(item) {
      this.current = item;
      this.getPageCouponRequest();
    },
    toPay() {
      if(!this.check) {
        this.$toast("请先同意《法临币充值协议》");
        return;
      }
      handleOrderPay({
        type: 1,
        serviceId: this.current.id,
        couponLawyerRecordId: this.couponInfo.id,
      });
    },
    selectedCoupon(item) {
      this.couponInfo = item;
    },
    couponsClick() {
      if(this.couponList.length > 0) {
        this.couponShow = true;
      }
    },
    clickXy() {
      turnToWebViewPage({ src: WebAddress.lawyer_legal_currency_recharge_protocol });
    },
    // 获取优惠券数据
    getPageCouponRequest () {
      // 没有选取充值的时候（不获取优惠券数据）
      if (isNull(this.current?.id)) return false
      const params = {
        status: 0,
        type: 4, // 卡券类型[1.会员现金券,2.会员体验券 3法临币现金券 4 法临币充值券]
        lawyerRechargeId: this.current?.id,
        currentPage: 1,
        pageSize: 1000
      }
      pageCoupon(params).then(({data={}}) => {
        this.couponInfo = data.records[0] ? data.records[0] : this.couponInfo
        this.couponList = data.records || []
      })
    },

  },
  created() {
    listAndBalance().then(({ data = {} }) => {
      this.cardInfo.number = data.balanceV2Response?.totalBalance || 0;
      this.rechargeList = data.rechargeList || [];
      this.current = data.rechargeList[0];
      this.getPageCouponRequest();
    });
  }
};
</script>

<style lang="scss" scoped>
.currency{
  .wrapper{
    padding: 0 16px;
    .card-wrapper{
      margin-top: 16px;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-gap: 12px;
      .card{
        width: 106px;
        height: 120px;
        background: #FFFFFF;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #DDDDDD;
        text-align: center;
        position: relative;
        box-sizing: border-box;
        .price{
          font-size: 18px;
          font-weight: 600;
          color: #3887F5;
        }
        .other{
          font-size: 12px;
          font-weight: 400;
          color: #EB4738;
          margin-top: 12px;
        }
        .active-img{
          display: none;
        }
        &.active{
          box-shadow: 0 4px 12px 0 rgba(56,135,245,0.2);
          border-radius: 8px 8px 8px 8px;
          border: 2px solid #3887F5;
          .active-img{
            display: block;
            position: absolute;
            width: 32px;
            height: 32px;
            bottom: -1px;
            right: -1px;
          }
        }
      }
    }
    .coupons{
      height: 44px;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      border-bottom: 1px solid #eee;
      margin-top: 24px;
      .selected{
        font-size: 14px;
        color: #EB4738;
        font-weight: 500;
        .iconfont{
          color: #ccc;
        }
        &.no-data,  {
          font-weight: 400;
          color: #ccc;
        }
      }
    }
  }
  .fixed-btn{
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    .btn-wrapper{
      box-shadow: 0px -3px 10px 0px rgba(0,0,0,0.08);
      border-radius: 16px 16px 0px 0px;
      padding:8px 16px;
      .btn{
        text-align: center;
        line-height: 44px;
        background: linear-gradient(109deg, #FF913E 0%, #F54A3A 100%);
        border-radius: 22px 22px 22px 22px;
        font-size: 16px;
        font-weight: 500;
        color: #FFFFFF;
      }
      .xy{
        font-size: 12px;
        font-weight: 400;
        color: #666666;
        margin-top: 16px;
        .img{
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }
      }
    }

  }
}
</style>
