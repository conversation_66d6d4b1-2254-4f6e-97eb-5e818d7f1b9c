export const isNull = (o) => {
  return o === null || o === "" || o === undefined;
};
export const isObj = (o) => {
  // 是否对象
  return Object.prototype.toString.call(o).slice(8, -1) === "Object";
};
export const isObjNull = (o) => {
  if (isNull(o)) return false;
  return (Object.keys(o) && Object.keys(o).length) === 0;
};
export const isArray = (o) => {
  // 是否数组
  return Object.prototype.toString.call(o) === "[object Array]";
};
export const isFunction = (o) => {
  // 是否函数
  return Object.prototype.toString.call(o).slice(8, -1) === "Function";
};
export const isString = (o) => {
  // 是否字符串
  return Object.prototype.toString.call(o).slice(8, -1) === "String";
};
export const isFile = (o) => {
  // 是否文件
  return Object.prototype.toString.call(o).slice(8, -1) === "File";
};
export const isNumber = (o) => {
  return Object.prototype.toString.call(o).slice(8, -1) === "Number";
};
export const isArrNull = (o) => {
  if (isNull(o)) return false;

  return o.length === 0;
};

/* 判断链接是不是带有http||https*/
export const isHttp = (url) => url.indexOf("http://") > -1 || url.indexOf("https://") > -1;

const basicsTools = {
  isArray,
  isArrNull,
  isFile,
  isFunction,
  isNull,
  isNumber,
  isObj,
  isObjNull,
  isString,
};
basicsTools.install = function (Vue) {
  Vue.prototype.$basicsTools = basicsTools;
};
export default basicsTools;
