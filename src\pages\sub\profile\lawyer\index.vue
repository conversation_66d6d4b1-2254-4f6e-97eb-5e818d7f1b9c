<template>
  <div>
    <profile-header-tip />
    <div class="mg-tp-12">
      <profile-header-score />
    </div>
    <div class="app-form">
      <u-form
        ref="uForm"
        :labelStyle="{
          fontSize: '14px',
        }"
        :model="params"
        :rules="rules"
        labelWidth="78"
      >
        <div class="content">
          <div class="title">
            基础信息
          </div>
          <div class="form-item">
            <profile-header-update v-model="params.imgUrl" />
            <u-form-item
              borderBottom
              label="用户名"
            >
              <app-field
                :value="userInfo.userName"
                color="#999999"
              />
            </u-form-item>
            <u-form-item
              borderBottom
              label="姓名"
            >
              <app-field
                :value="userInfo.realName"
                color="#999999"
                placeholder=" "
              />
            </u-form-item>
            <u-form-item label="就职单位">
              <app-field
                :value="userInfo.lawyerOffice"
                color="#999999"
                placeholder=" "
              />
            </u-form-item>
          </div>
        </div>
        <div class="content mg-b-12">
          <div class="title">
            简介信息
          </div>
          <div class="form-item">
            <u-form-item
              borderBottom
              label="个人简介"
              prop="lawyerProfile"
              required
            >
              <app-field
                :lineClamp="2"
                :value="params.lawyerProfile"
                align="left"
                arrow
                color="#333333"
                placeholder="请输入"
                placeholderColor="#CCCCCC"
                @click="turnToProfilePage"
              />
            </u-form-item>
            <u-form-item
              borderBottom
              label="教育背景"
              prop="eduCheckList"
              required
            >
              <app-field
                :value="school"
                align="left"
                arrow
                color="#333333"
                placeholder="请输入"
                placeholderColor="#CCCCCC"
                @click="turnToEducationPage"
              />
            </u-form-item>
            <div>
              <u-form-item label="荣誉奖项">
                <app-field
                  :value="params.honor ? ' ' : '请输入'"
                  align="left"
                  arrow
                  color="#CCCCCC"
                  @click="turnToHonorPage"
                />
              </u-form-item>
              <div
                v-if="params.honor"
                class="honor-text"
              >
                {{ params.honor }}
              </div>
            </div>
          </div>
        </div>
      </u-form>
      <!-- 底部按钮 -->
      <div
        class="w-[326px] h-[44px] bg-[#3887F5] rounded-[40px] font-bold text-[15px] text-[#FFFFFF] flex items-center justify-center box-border py-[8px] mx-auto mt-[28px]"
        @click="submit"
      >
        提交资料
      </div>
      <u-safe-bottom />
    </div>
  </div>
</template>

<script>
import UFormItem from "@/uview-ui/components/u-form-item/u-form-item.vue";
import UForm from "@/uview-ui/components/u-form/u-form.vue";
import ProfileHeaderScore from "@/pages/sub/profile/lawyer/components/ProfileHeaderScore.vue";
import ProfileHeaderTip from "@/pages/sub/profile/lawyer/components/ProfileHeaderTip.vue";
import { updateAllLawyerInfo, userDetail } from "@/api/user";
import ProfileHeaderUpdate from "@/pages/sub/profile/lawyer/components/ProfileHeaderUpdate.vue";
import AppField from "@/components/AppComponents/AppField/index.vue";
import {
  turnToEducationPage,
  turnToHonorPage,
  turnToProfilePage,
} from "@/libs/turnPages";
import store from "@/store";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "ProfileLawyer",
  components: {
    USafeBottom,
    AppField,
    ProfileHeaderUpdate,
    ProfileHeaderTip,
    ProfileHeaderScore,
    UForm,
    UFormItem,
  },
  data() {
    return {
      /**
       * 个人资料
       */
      profileInfo: {},
      rules: {
        lawyerProfile: [
          {
            required: true,
            message: "请输入个人简介",
            trigger: "blur",
          },
        ],
        eduCheckList: [
          {
            validator: () => {
              return !!this.school;
            },
            message: "请填写学校名称",
            trigger: "blur",
          },
        ],
      },
    };
  },
  onReady() {
    // 如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
    this.$refs.uForm.setRules(this.rules);
  },
  computed: {
    /** 用户信息 */
    userInfo() {
      return this.profileInfo.userInfo;
    },
    /** 律师个人信息 */
    lawyerProfile() {
      return this.profileInfo.lawyerProfile;
    },
    params() {
      return store.state.lawyerProfile.lawyerProfile;
    },
    school() {
      return this.params.eduCheckList[0]?.school;
    },
  },
  created() {
    this.getProfileInfo();
  },
  methods: {
    turnToHonorPage,
    turnToEducationPage,
    turnToProfilePage,
    /** 获取个人资料 */
    getProfileInfo() {
      userDetail().then((res) => {
        this.profileInfo = res.data;

        // 如果有值，就用后端返回的值
        if (this.profileInfo.lawyerEduList.length > 0)
          this.params.eduCheckList = this.profileInfo.lawyerEduList;

        this.params.imgUrl = this.userInfo.imgUrl;
        this.params.lawyerProfile = this.lawyerProfile.lawyerProfile;
        this.params.honor = this.lawyerProfile.honor;
      });
    },
    /** 点击提交资料 */
    submit() {
      this.$refs.uForm
        .validate()
        .then(() => {
          updateAllLawyerInfo(this.params).then(() => {
            this.$toast("保存成功").then(() => {
              uni.navigateBack();
            });
          });
        })
        .catch((errors) => {
          console.log(errors);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/formStyles";

.content {
  width: 343px;
  margin-top: 12px;
  background: #ffffff;
  border-radius: 12px;
  opacity: 1;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;

  .title {
    padding: 16px;
    font-size: 16px;
    font-weight: bold;
    color: #111111;
  }

  .form-item {
    padding: 0 12px;
  }
}

.bottom-text {
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  padding: 8px 0;
}

.honor-text {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  padding-bottom: 8px;
}
</style>
