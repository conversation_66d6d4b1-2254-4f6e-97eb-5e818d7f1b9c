<template>
  <div
    v-if="dynamicInfoList.length"
    class="dynamic flex flex-align-center mg-tp-12 px-[16px] py-[12px] mx-[16px] bg-[#FFFFFF] rounded-[8px]"
  >
    <img
      alt=""
      class="dynamic-title"
      src="../img/dynamic.png"
    >
    <div>
      <div
        v-for="(item, index) in dynamicInfoList"
        :key="index"
        class="dynamic-item"
      >
        <u-parse :content="item" />
      </div>
    </div>
  </div>
</template>

<script>
import { lawyerLawyerZx } from "@/api/lawyer.js";

export default {
  name: "LawyerHomeDynamic",
  props: {
    lawyerInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      /** 动态信息 */
      dynamicInfo: [],
    };
  },
  computed: {
    /** 律师动态列表 */
    dynamicInfoList() {
      // 如未达到2条，此处返回空数组
      if (this.dynamicInfo.length < 2) {
        return [];
      }

      // 默认显示最近2条
      return this.dynamicInfo
        .slice(0, 2)
      // 正则匹配并且替换包括 接单了 后的内容
        .map((item) => item.replace(/(接单了|收到了)(.*)/g, " <span class='text-theme-color'>$1$2</span>"));
    },
  },
  watch: {
    lawyerInfo: {
      handler(val) {
        if (val.id) {
          this.getDynamicInfo();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    /** 获取动态信息 */
    getDynamicInfo() {
      lawyerLawyerZx({
        lawyerId: this.lawyerInfo.id,
      }).then((res) => {
        this.dynamicInfo = res.data || [];
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.dynamic {
  opacity: 1;
  background: #ffffff;
  box-sizing: border-box;

  &-title {
    flex-shrink: 0;
    display: block;
    width: 33px;
    height: 33px;
    margin-right: 8px;
  }

  &-item {
    font-size: 12px;
    font-weight: 400;
    color: #333333;

    &:nth-child(n) {
      margin-top: 6px;
    }

    &:nth-child(1) {
      margin-top: 0;
    }
  }
}
</style>
