<template>
  <div class="card-container">
    <div
      class="card flex position-relative"
      @click="jumpToAttorneyHomepage"
    >
      <!-- 左上角的图标 -->
      <div
        class="guide-card-icon"
        @click.stop="$emit('handleInfo')"
      >
        服务说明
      </div>
      <!-- 头像 -->
      <img
          mode="aspectFill"
        :src="lawyerInfo.imgUrl"
        alt=""
        class="head-portrait"
      >
      <div class="flex-1">
        <div class="flex">
          <p class="name">
            {{ lawyerInfo.realName }}
          </p>
          <p class="age-limit">
            执业{{ lawyerInfo.workTime }}年
          </p>
        </div>
        <p class="law-firm">
          {{ lawyerInfo.lawyerOffice || "未填写" }}
        </p>
        <div class="be-good-at flex">
          <p
            v-for="(i, index) in beGoodAtList"
            :key="index"
            class="item"
          >
            {{ i }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { toLawyerHome } from "@/libs/turnPages.js";

export default {
  name: "LawyerInfoCard",
  props: {
    /** 律师信息 */
    lawyerInfo: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {
      /** 擅长类别的个数 */
      beGoodAtNum: 3,
    };
  },
  computed: {
    beGoodAtList() {
      return this.lawyerInfo.workField?.slice(0, this.beGoodAtNum) || [];
    },
  },
  methods: {
    /** 跳转到律师主页 */
    jumpToAttorneyHomepage() {
      toLawyerHome({
        id: this.lawyerInfo.id,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.card-container {
  padding-top: 8px;
  opacity: 1;
}

.guide-card-icon {
  position: absolute;
  top: 0;
  right: 0;
  border-radius: 0 8px 0 8px;
  width: 60px;
  height: 18px;
  background: #3887F5;
  opacity: 1;
  font-size: 11px;
  font-weight: 400;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.card {
  padding: 16px;
  background: white;
  border-radius: 12px 12px 12px 12px;

  .head-portrait {
    width: 64px;
    height: 64px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 12px;
  }

  .name {
    font-size: 15px;
    font-weight: bold;
    color: #333333;
    padding-right: 12px;
  }

  .age-limit {
    font-size: 13px;
    font-weight: 400;
    color: #999999;
  }

  .law-firm {
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    padding: 4px 0 12px;
  }

  .be-good-at {
    .item {
      font-size: 11px;
      margin-right: 5px;
      border: 0.5px solid #dcebf8;
      border-radius: 4px 4px 4px 4px;
      font-weight: 400;
      color: #666666;
      padding: 1px 4px 1px;
      background: #edf3f7;
      // #ifdef  MP-HJLS
      border: 0.5px solid #EEEEEE;
      background: #F5F5F7 !important;
      // #endif
      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
