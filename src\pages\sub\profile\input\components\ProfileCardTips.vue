<template>
  <div class="position-relative">
    <slot />
    <div
      v-if="!hide || value"
      class="tips"
    >
      <img
        alt=""
        class="tips-icon"
        src="../img/group.png"
      >
      <div class="tips-text">
        {{ showText }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ProfileCardTips",
  props: {
    value: {
      default: false,
    },
    text: {
      type: String,
      default: "",
    },
    /** 已经有值 */
    default: {
      type: String,
      default: "重新上传",
    },
    /** 是否在没值的时候不展示 */
    hide: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    /** 展示文案 */
    showText() {
      return this.value ? this.default : this.text;
    },
  },
};
</script>

<style lang="scss" scoped>
.tips {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  padding: 5px 10px 5px 5px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 16px;
  opacity: 1;
  display: flex;
  align-items: center;
  pointer-events: none;

  &-icon {
    width: 22px;
    height: 22px;
  }

  &-text {
    margin-left: 6px;
    font-size: 14px;
    font-weight: 400;
    color: #ffffff;
  }
}
</style>
