<template>
  <div>
    <app-avatar-cropper
      :value="value"
      @input="(e) => $emit('input', e)"
    >
      <template #default="{ header }">
        <div class="tips">
          <div><span class="require mg-r-4">*</span> 请上传头像</div>
          <img
            alt=""
            class="tips-icon mg-l-4"
            src="../img/tips-icon.png"
            @click.stop.native="dialog=true"
          >
        </div>
        <div class="text">
          为确保真实性，照片上传脸无遮挡更能提高通过率
        </div>
        <div class="header flex flex-align-center flex-space-between">
          <div class="header-image">
            <img
              v-if="header"
              :src="header"
              alt=""
              class="header-image__yes"
            >
            <div
              v-else
              class="header-image__no"
            >
              <img
                alt=""
                class="header-image__no__icon"
                src="../img/Frame103.png"
              >
            </div>
            <div class="header-image__text">
              {{ header ? "重新上传" : "上传头像" }}
            </div>
          </div>
          <img
            alt=""
            class="case"
            src="../img/Frame1497.png"
          >
        </div>
      </template>
    </app-avatar-cropper>
    <app-popup
      :safeAreaInsetBottom="false"
      :show="dialog"
      mode="center"
    >
      <div class="popup">
        <div class="popup-title">
          温馨提示
        </div>
        <div class="popup-content">
          上传视为您许可法临将其用于APP宣传，
          使用方式、范围详见《用户服务协议》
        </div>
        <div
          class="popup-button"
          @click="dialog=false"
        >
          我知道了
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppUpload from "@/components/AppComponents/AppUpload/index.vue";
import AppAvatarCropper from "@/components/AppComponents/AppAvatarCropper/index.vue";
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";

export default {
  name: "ProfileInputHeader",
  components: { AppPopup, AppAvatarCropper, AppUpload },
  props: {
    value: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      /** 弹窗 */
      dialog: false,
    };
  }
};
</script>

<style lang="scss" scoped>
.tips {
  font-size: 16px;
  font-weight: bold;
  color: #111111;
  display: flex;
  align-items: center;

  .require {
    color: #eb4738;
  }
}

.text {
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  margin-bottom: 16px;
}

.tips-icon {
  display: block;
  width: 16px;
  height: 16px;
}

.case {
  display: block;
  width: 216px;
  height: 67px;
}

.header {

  &-image {
    margin-right: 23px;
    width: 80px;
    height: 80px;
    border-radius: 8px;
    opacity: 1;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;

    &__yes {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      border-radius: 8px;
    }

    &__no {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      background: #3887f5;
      padding-top: 16px;

      &__icon {
        width: 28px;
        height: 28px;
        display: block;
        margin: 0 auto;
      }
    }

    &__text {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 80px;
      height: 20px;
      background: rgba(0, 0, 0, 0.7);
      opacity: 1;
      font-size: 12px;
      font-weight: 400;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.popup {
  width: 311px;
  background: #FFFFFF;
  box-sizing: border-box;
  padding: 24px 24px 16px 24px;
  border-radius: 16px;
  opacity: 1;

  &-title {
    font-size: 16px;
    font-weight: bold;
    color: #333333;
    text-align: center;
  }

  &-content {
    margin-top: 12px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
  }

  &-button {
    margin-top: 24px;
    width: 263px;
    height: 36px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #3887F5;
    border-radius: 68px;
    opacity: 1;
    font-size: 14px;
    font-weight: bold;
    color: #FFFFFF;
  }
}
</style>
