<template>
  <div class="ChatRoomHeader">
    <!-- 案源卡片   -->
    <chat-room-top-case-source-card v-if="isCaseInfoItTheSourceOfTheCase" />
  </div>
</template>

<script>
import ChatRoomTopCaseSourceCard from "@/pages/sub/im/components/ChatRoomTop/ChatRoomTopCaseSourceCard.vue";
import { caseInfoStateProps } from "@/pages/sub/im/mixins/case-info-state.js";

export default {
  name: "ChatRoomTop",
  mixins: [caseInfoStateProps],
  components: { ChatRoomTopCaseSourceCard }
};
</script>

<style scoped lang="scss">
.ChatRoomHeader{
  padding: 16px 0;
}
</style>
