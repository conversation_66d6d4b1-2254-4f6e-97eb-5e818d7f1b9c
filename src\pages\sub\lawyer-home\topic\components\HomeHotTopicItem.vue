<template>
  <div class="flex flex-wrap">
    <div
      v-for="item in showDataList"
      :key="item.value"
      class="position-relative w-[175px] h-[82px] block"
      @click="toSpecialSession(item.label)"
    >
      <img
        :src="item.supplementDesc.icon"
        alt=""
        class="background-image"
      >
    </div>
  </div>
</template>
<script>
import { toSpecialSession } from "@/libs/turnPages";

export default {
  name: "HomeHotTopicItem",
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    /** 显示数据 */
    showDataList() {
      // 特殊处理
      return this.list;
    },
  },
  methods: {
    /** 跳转到专题 */
    toSpecialSession(typeValue) {
      toSpecialSession({ typeValue });
    },
  },
};
</script>
