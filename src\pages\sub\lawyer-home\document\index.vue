<template>
  <scroll-view
    class="w-screen h-screen"
    scrollY
    @scrolltolower="scrollToLower"
  >
    <div class="document-container">
      <p class="title">
        找到
        <span class="text-theme-color mg-l-4 mg-r-4">{{ caseTotal }}</span>
        条结果
      </p>
      <div class="document-box">
        <div
          v-for="item in caseList"
          :key="item.id"
          class="document-item"
          @click="goDetail(item.id)"
        >
          <lawyer-ruling-document :data="item" />
        </div>
        <app-divider />
      </div>
    </div>
  </scroll-view>
</template>

<script>
import LawyerRulingDocument from "@/pages/sub/lawyer-home/components/lawyer-ruling-document.vue";
import { bigdataLawyerCaseList, lawyerLawCase } from "@/api/lawyer.js";
import getLawyerInfo from "@/pages/sub/lawyer-home/mixins/getLawyerInfo.js";
import AppDivider from "@/components/AppComponents/AppDivider/index.vue";
import { toLawyerHomeJuDocumentDetail } from "@/libs/turnPages.js";

export default {
  name: "LawyerHomeDocument",
  components: { AppDivider, LawyerRulingDocument },
  mixins: [getLawyerInfo],
  data() {
    return {
      /** 案件列表 */
      caseList: [],
      /** 案件总数 */
      caseTotal: 0,
      /** 案件页码 */
      casePage: 1,
      /** 案件每页条数 */
      caseSize: 20,
      /* 是不是律师手动上传案例件*/
      isOfMeState: false,
    };
  },
  watch: {
    lawyerInfo: {
      handler() {
        this.getCaseList();
      },
      deep: true,
    },
  },
  methods: {
    /** 获取案件列表 */
    getCaseList() {
      const params = {
        lawyerName: this.lawyerInfo.realName,
        lawfirmName: this.lawyerInfo.lawyerOffice,
        currentPage: this.casePage,
        pageSize: this.caseSize,
      };

      if (this.isOfMeState) {
        this.getCaseListOfMe(params);
        return false;
      }

      bigdataLawyerCaseList(params).then((res) => {
        this.caseList = this.caseList.concat(res.data.caseList || []);
        this.caseTotal = res.data.total || 0;
        this.isOfMeState = this.caseTotal < 1;
        if (this.isOfMeState) {
          this.getCaseListOfMe(params);
        }
      });
    },
    /* 获取律师手动上传案例件*/
    getCaseListOfMe(data) {
      if (!this.lawyerInfo.id) return;
      /* lawyerId	是	string	律师ID
      currentPage	否	Integer	当前页码
      pageSize	否	Integer	每页大小*/
      lawyerLawCase({
        ...data,
        lawyerId: this.lawyerInfo.id,
      }).then(({ data }) => {
        this.caseTotal = data.total || 0;
        this.caseList = this.caseList.concat(data.records || []);
      });
    },
    /** 滚动到底部 */
    scrollToLower() {
      if (this.casePage * this.caseSize >= this.caseTotal) return;
      this.casePage++;
      this.getCaseList();
    },
    goDetail(id) {
      toLawyerHomeJuDocumentDetail({
        id,
        lawyerId: this.lawyerInfo.id,
        state: this.isOfMeState,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.document-container {
  margin: 12px;
}

.title {
  font-size: 12px;
  font-weight: 400;
  color: #666666;
}

.document-box {
  margin-top: 12px;
}

.document-item {
  padding: 12px;
  background: #ffffff;
  border-radius: 8px;
  opacity: 1;
  margin-bottom: 12px;
}
</style>
