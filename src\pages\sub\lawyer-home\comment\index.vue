<template>
  <div>
    <scroll-view
      class="w-screen h-screen"
      scrollY
      @scrolltolower="scrollToLower"
    >
      <div class="comments">
        <div
          v-for="item in commentsList"
          :key="item.id"
          class="comments-item"
        >
          <lawyer-home-score-item
            :data="item"
            showAll
          />
        </div>
        <app-divider />
      </div>
    </scroll-view>
    <app-placeholder
      :height="60"
      :showSafe="false"
    />
  </div>
</template>

<script>
import { getEvaluatePage } from "@/api/lawyer.js";
import LawyerHomeScoreItem from "@/pages/sub/lawyer-home/components/lawyer-home-score/lawyer-home-score-item.vue";
import { getCurrentPageRoute } from "@/libs/turnPages.js";
import AppDivider from "@/components/AppComponents/AppDivider/index.vue";
import AppPlaceholder from "@/components/AppComponents/AppPlaceholder/index.vue";

export default {
  name: "LawyerHomeComment",
  components: { AppPlaceholder, AppDivider, LawyerHomeScoreItem },
  data() {
    return {
      /** 评论列表 */
      commentsList: [],
      /** 评论总数 */
      commentsTotal: 0,
      /** 评论页码 */
      commentsPage: 1,
      /** 评论每页条数 */
      commentsSize: 20,
    };
  },
  mounted() {
    this.getEvaluatePage();
  },
  methods: {
    /** 获取评价数据 */
    getEvaluatePage() {
      const { id = "743" } = getCurrentPageRoute().query;

      getEvaluatePage({
        lawyerId: id,
        pageSize: this.commentsSize,
        currentPage: this.commentsPage,
      }).then((res) => {
        this.commentsList = this.commentsList.concat(res.data.records || []);
        this.commentsTotal = res.data.total || 0;
      });
    },
    /** 滚动到底部 */
    scrollToLower() {
      if (this.commentsPage * this.commentsSize >= this.commentsTotal) return;
      this.commentsPage++;
      this.getEvaluatePage();
    },
  },
};
</script>

<style>
page {
  background-color: #fff;
}
</style>

<style lang="scss" scoped>
.comments {
  padding: 8px 16px 0 16px;
}

.comments-item {
  margin-top: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f5f5f5;

  &:first-child {
    margin-top: 0;
  }
}
</style>
