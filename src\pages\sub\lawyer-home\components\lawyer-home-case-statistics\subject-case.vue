<template>
  <div>
    <div class="charts-box">
      <qiun-data-charts
        :canvas2d="true"
        :chartData="chartData"
        :opts="opts"
        canvasId="lMkugbKPY3aET5wG"
        type="column"
      />
    </div>
  </div>
</template>

<script>
import QiunDataCharts from "@/pages/sub/lawyer-home/ucharts/components/qiun-data-charts/qiun-data-charts.vue";

/**
 * 案件标的
 */
export default {
  name: "SubjectCase",
  components: { QiunDataCharts },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      chartData: {},
      opts: {
        color: ["#A0CFFB"],
        legend: {
          show: false,
        },
        fontSize: 9,
        xAxis: {
          disableGrid: true,
          fontSize: 10,
          fontColor: "#999999",
        },
        yAxis: {
          titleFontColor: "#999999",
          disableGrid: true,
        },
        extra: {
          column: {
            type: "group",
            width: 18,
            seriesGap: 5,
            barBorderRadius: [4, 4, 0, 0],
            activeBgOpacity: 0,
          },
          tooltip: {
            showBox: false,
            splitLine: false,
          },
        },
      },
    };
  },
  watch: {
    data: {
      handler() {
        this.getServerData();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    getServerData() {
      const data = [];
      const categories = [];

      this.data.forEach((item) => {
        data.push(Number(item.count));
        categories.push(item.label);
      });

      let res = {
        categories: categories,
        series: [
          {
            name: "目标值",
            data: data,
            textColor: "#3887F5",
            textSize: 10,
          },
        ],
      };

      this.chartData = JSON.parse(JSON.stringify(res));
      console.log(JSON.stringify(this.chartData));
    },
  },
};
</script>

<style lang="scss" scoped></style>
