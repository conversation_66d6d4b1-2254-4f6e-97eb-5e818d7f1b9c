<template>
  <div>
    <placeholder-container ref="pc">
      <!-- 更换律师     -->
      <error-tip
        v-if="isCaseInfoUserChangeLawyer===0"
        bgColor="#FAF2E8"
        textColor="#F78C3E"
        borderRadius="0"
        :borderStyle="''"
        closeIcon
        @close="initHeight"
      >
        用户申请更换律师，客服处理中
      </error-tip>
      <!-- 撤销更换律师     -->
      <error-tip
        v-if="isCaseInfoUserChangeLawyer===9"
        bgColor="#FAF2E8"
        textColor="#F78C3E"
        borderRadius="0"
        :borderStyle="''"
        closeIcon
        @close="initHeight"
      >
        用户撤销更换律师
      </error-tip>
      <!-- 案源通知   -->
      <case-source-notifications
        v-if="isCaseInfoItTheSourceOfTheCase"
        @close="initHeight"
      />
      <!-- 咨询卡片     -->
      <inquiry-card v-if="isCaseInfoPayOneToOne" />
      <!--   协作对方律师信息卡片   -->
      <collaborate-on-opposing-counsel-info-cards v-if="getConversationInfo.isLawyerHelp" />
    </placeholder-container>
  </div>
</template>

<script>
import CaseSourceNotifications from "@/pages/sub/im/components/ChatRoomHeader/CaseSourceNotifications.vue";
import PlaceholderContainer from "@/pages/sub/im/components/PlaceholderContainer/index.vue";
import { caseInfoStateProps, conversationInfoStateProps } from "@/pages/sub/im/mixins/case-info-state.js";
import ErrorTip from "@/pages/sub/im/components/ErrorTip/index.vue";
import InquiryCard from "@/pages/sub/im/components/ChatRoomHeader/InquiryCard.vue";
import CollaborateOnOpposingCounselInfoCards
  from "@/pages/sub/im/components/ChatRoomHeader/CollaborateOnOpposingCounselInfoCards.vue";

export default {
  name: "ChatRoomHeader",
  components: { CollaborateOnOpposingCounselInfoCards, InquiryCard, ErrorTip, PlaceholderContainer, CaseSourceNotifications },
  mixins: [caseInfoStateProps, conversationInfoStateProps],
  watch: {
    caseInfo: {
      handler(){
        this.initHeight();
      },
      deep: true
    }
  },
  methods: {
    initHeight() {
      this.$nextTick(() => {
        this.$refs.pc.initHeight();
      });
    }
  }
};
</script>

<style scoped lang="scss">
</style>
