<template>
  <div>
    <app-popup
      :show="show"
      :zIndex="99999"
    >
      <div class="dialog">
        <img
          alt=""
          class="server-top"
          mode="widthFix"
          src="../img/server-top.png"
        >
        <scroll-view
          class="server"
          scrollY
        >
          <img
            alt=""
            class="server-info"
            mode="widthFix"
            src="../img/server-info.png"
          >
        </scroll-view>
        <div
          class="bottom-button"
          @click="$emit('close')"
        >
          我知道了
        </div>
        <u-safe-bottom />
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "LawyerHomeServersPopup",
  components: { USafeBottom, AppPopup },
  props: {
    show: {
      type: <PERSON>olean,
      default: false,
    },
  },
};
</script>

<style lang="scss" scoped>
.dialog {
  padding: 16px 0;
  position: relative;
}

.server-top {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: -8px;
  width: 207px;
}

.server {
  margin-top: 40px;
  height: 438px;

  &-info {
    display: block;
    width: 343px;
    margin: 0 auto;
  }
}

.bottom-button {
  height: 46px;
  background: #ffffff;
  opacity: 1;
  font-size: 16px;
  font-weight: 400;
  color: #3887f5;
  line-height: 46px;
  text-align: center;
}
</style>
