
<template>
  <div
    :class="cardBoxClass"
    class="card-box scroll-bar-none"
  >
    <slot />
  </div>
</template>

<script>
export default {
  name: "PayLawyerGuideAdvisoryContainer",
  props: {
    /** item的数量 */
    num: {
      type: Number,
      default: 1,
    },
  },
  computed: {
    /** 根据item不同，盒子的CSS属性也不同 */
    cardBoxClass() {
      const length = this.num;

      if (length === 1) return "card-box--one";

      if (length === 2) return "card-box--two";

      return "card-box--more";
    },
  },
};
</script>

<style lang="scss" scoped>
.card-box {
  // 只有一个item的时候
  &--one {
    padding-right: 16px;
  }

  // 有两个item的时候
  &--two {
    display: grid;
    padding-right: 16px;
    margin-right: 0;
    grid-gap: 12px;
    grid-template-columns: repeat(2, 1fr);
  }

  // 有两个item以上的时候
  &--more {
    display: flex;
    overflow-x: auto;
    gap:12px;
   /* >*:nth-child(n) {
      margin-right: 12px;

      &:last-child {
        margin-right: 0;
      }
    }*/
  }

}
</style>
