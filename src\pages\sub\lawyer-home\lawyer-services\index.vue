<template>
  <login-layout>
    <lawyer-services-headers :scene="scene" />
    <div>
      <service-introduction :scene="scene" />
      <div class="mg-tp-12">
        <service-process :scene="scene" />
      </div>
      <div class="mg-tp-12">
        <lawyer-services-user-case :scene="scene" />
      </div>
    </div>
    <u-safe-bottom />
  </login-layout>
</template>

<script>
import ServiceIntroduction from "@/pages/sub/lawyer-home/lawyer-services/components/ServiceIntroduction.vue";
import LoginLayout from "@/components/login/login-layout.vue";
import ServiceProcess from "@/pages/sub/lawyer-home/lawyer-services/components/ServiceProcess.vue";
import LawyerServicesUserCase from "@/pages/sub/lawyer-home/lawyer-services/components/LawyerServicesUserCase.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import LawyerServicesHeaders from "@/pages/sub/lawyer-home/lawyer-services/components/LawyerServicesHeaders.vue";

export default {
  name: "LawyerHomeLawyerServices",
  components: {
    LawyerServicesHeaders,
    USafeBottom,
    LawyerServicesUserCase,
    ServiceProcess,
    LoginLayout,
    ServiceIntroduction,
  },
  data() {
    return {
      /** 当前页面的类型 */
      scene: null,
    };
  },
  async onLoad({ scene }) {
    this.scene = scene;
  },
};
</script>

<style lang="scss" scoped>
.lawyer-suggest {
  width: 375px;
  margin-top: 16px;
  border-radius: 16px 16px 0 0;
  opacity: 1;
  background-color: #f5f5f7;
  overflow: hidden;
}

.lawyer-suggest-header {
  height: 89px;
  background: linear-gradient(
    180deg,
    #ffffff 0%,
    rgba(245, 245, 247, 0) 100%,
    #f5f5f7 100%
  );
  opacity: 1;
  box-sizing: border-box;

  &-text {
    margin: 0 auto;
    width: 351px;
    height: 24px;
    font-size: 16px;
    font-weight: bold;
    color: #333333;
    padding-top: 12px;

    &-title {
      font-size: 18px;
      font-weight: bold;
      color: #222222;
    }

    &-subtitle {
      font-size: 14px;
      font-weight: 400;
      color: #999999;

      &-icon {
        display: block;
        width: 14px;
        height: 14px;
        margin-left: 4px;
      }
    }
  }
}

.lawyer-card {
  display: flex;
  width: 351px;
  background: #ffffff;
  padding: 16px;
  box-sizing: border-box;
  margin: 0 auto;
  border-radius: 8px;

  &:not(:last-child) {
    margin-bottom: 12px;
  }

  &-header {
    position: relative;
    width: 48px;
    height: 48px;

    &-icon {
      display: block;
      width: 48px;
      height: 48px;
      background: #c4c4c4;
      border-radius: 4px;

      &-vip {
        position: absolute;
        right: 0;
        bottom: 0;
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-left: 4px;
        z-index: 1;
      }
    }
  }

  &-info {
    flex: 1;
    margin-left: 12px;

    &-name {
      font-size: 16px;
      font-weight: bold;
      color: #333333;
    }

    &-city {
      margin-left: 8px;
      font-size: 12px;
      font-weight: 400;
      color: #666666;
    }

    &-price {
      width: 60px;
      text-align: center;
      font-size: 13px;
      font-weight: bold;
      color: #eb4738;
    }

    &-office {
      font-size: 12px;
      font-weight: 400;
      color: #999999;
    }

    &-buy {
      width: 60px;
      height: 24px;
      border-radius: 68px 68px 68px 68px;
      opacity: 1;
      border: 1px solid #3887f5;
      font-size: 12px;
      font-weight: 400;
      color: #3887f5;
      text-align: center;
      line-height: 24px;
    }
  }

  &-des {
    font-size: 14px;
    font-weight: 400;
    color: #666666;

    &-star {
      width: 14px;
      height: 14px;
      display: block;
      margin-right: 2px;
    }
  }

  &-work {
    &-item {
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      font-size: 12px;
      background: #edf3f7;
      border-radius: 4px;
      opacity: 1;
      width: 58px;
      height: 20px;
      margin-left: 4px;
      border: 0.5px solid #dcebf8;
      color: #666666;

      &:not(:last-child) {
        margin-right: 5px;
      }
    }
  }
}

.info-dialog-modal {
  width: 311px;
  background: #fff;
  border-radius: 16px;

  .info-dialog-content {
    padding: 24px;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 16px;

    .info-dialog-title {
      font-size: 18px;
      font-weight: bold;
      color: #333333;
      margin-bottom: 12px;
      text-align: center;
    }

    .info-dialog-nr {
      height: 300px;
      font-size: 14px;
      color: #666;
    }
  }

  .info-dialog-btn {
    height: 46px;
    line-height: 46px;
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    color: #3887f5;
    border-top: 1px solid #eeeeee;
  }
}

.tabs-icon {
  padding-right: 20px;
  padding-left: 10px;
  position: relative;
  z-index: 2;
  width: 16px;
  height: 16px;
}

.tabs-mark {
  width: 44px;
  height: 44px;
  background: linear-gradient(270deg, #f5f5f7 0%, rgba(245, 245, 247, 0) 100%);
  opacity: 1;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;
}
</style>
