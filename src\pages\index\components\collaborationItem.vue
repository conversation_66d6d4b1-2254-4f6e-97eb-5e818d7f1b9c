<template>
  <div
    class="px-[16px] py-[16px] bg-[#FFFFFF] rounded-[12px] mb-[12px]"
  >
    <div :class="[{ash:isGray}]">
      <!-- 律师信息头部 -->
      <div class="flex items-center justify-between pb-[16px] border-b-[1px] border-0 border-solid border-[#F7F6F6]">
        <div class="flex items-center">
          <img
            :src="data.fbLawyerAvatar"
            alt=""
            class="w-[40px] h-[40px] rounded-[20px] mr-[12px]"
          >
          <div class="flex flex-col">
            <div class="flex items-center mb-[4px]">
              <span class="text-[16px] font-medium text-[#333333] mr-[8px]">{{ data.fbLawyerName }}</span>
              <span class="px-[6px] py-[2px] rounded-[2px] border-[1px] border-solid border-[#EBDABF] text-[10px] text-[#7D6634] mr-[8px]">已认证</span>
              <span class="px-[6px] py-[2px] rounded-[2px] border-[1px] border-solid border-[#EBDABF] text-[10px] text-[#7D6634]">执业{{ data.workTime }}年</span>
            </div>
            <span class="text-[12px] text-[#999999]">{{ data.createTimeDesc }} 发布</span>
          </div>
        </div>
      </div>

      <div class="pl-[44px]">
        <!-- 协作类型和浏览量 -->
        <div class="flex items-center justify-between mb-[16px] mt-[12px]">
          <div class="flex items-center">
            <span class="text-[14px] text-[#666666] mr-[10px] shrink-0">协作类型</span>
            <span class="py-[2px] px-[6px] bg-[#EBF3FE] rounded-[4px] text-[12px] text-[#3887F5] mr-[16px]">
              {{ data.bizTypeLabel || collaborationStatus }}
            </span>
          </div>
          <div class="flex items-center">
            <i class="iconfont icon-liulan !text-[12px] text-[#999999] mr-[4px]" />
            <span class="text-[12px] text-[#999999]">{{ data.pv }}</span>
          </div>
        </div>

        <!-- 详细需求 -->
        <div class="mb-[16px] flex items-center">
          <div class="text-[14px] text-[#666666] mr-[10px] shrink-0">
            详细需求
          </div>
          <div class="text-[14px] text-[#333333] leading-[20px] text-ellipsis">
            {{ data.info }}
          </div>
        </div>

        <!-- 其他要求 -->
        <div class="mb-[16px] flex items-center">
          <div class="text-[14px] text-[#666666] mr-[10px] shrink-0">
            其他要求
          </div>
          <div class="flex items-center">
            <span
              v-for="(item,index) in data.labels"
              :key="index"
              class="px-[5px] py-[2px] text-[14px] text-[#EB4738] bg-[rgba(235,71,56,0.05)] rounded-[4px] border-[1px] border-solid border-[rgba(235,71,56,0.1)] mr-[4px]"
            >{{ item }}</span>
          </div>
        </div>

        <!-- 接单响应 -->
        <div class="mb-[16px] flex items-center">
          <div class="text-[14px] text-[#666666] mr-[10px] shrink-0">
            接单响应
          </div>
          <div class="flex items-center">
            <div class="flex items-center mr-[8px]">
              <img
                v-for="(item,index) in jdLawyerHeadImg"
                :key="index"
                :src="item"
                alt=""
                class="w-[24px] h-[24px] rounded-[12px] mr-[4px]"
              >
            </div>
            <span class="text-[14px] text-[#333333]">已有{{ data.currJdNum }}位律师响应</span>
          </div>
        </div>
        <div class="flex items-center justify-between mb-[10px]">
          <div class="flex items-center">
            <i class="text-[16px] text-[#666666] iconfont icon-dingwei mr-[4px]" />
            <span class="text-[13px] text-[#666666]">
              {{ data.provinceName?data.provinceName+'-':'' }}{{ data.regionName }}
            </span>
          </div>
          <span class="font-bold text-[18px] text-[#F78C3E]">
            ¥{{ amountFilterTwo(data.amount) }}
          </span>
        </div>

        <!-- 地址和价格 -->
        <div class="flex items-center justify-between pt-[12px] border-t-[1px] border-0 border-solid border-[#F7F6F6]">
          <app-button-share
            v-if="!isGray"
            @click="handleShare"
          >
            <div class="w-[118px] h-[40px] bg-[#EBF3FE] rounded-[50px] flex items-center justify-center box-border mr-[8px]">
              <i class="iconfont icon-fenxiang mr-[4px] text-[18px] text-[#3887F5]" />
              <div class="text-[16px] text-[#3887F5]">
                立即分享
              </div>
            </div>
          </app-button-share>
          <div
            v-else
            class="w-[118px] h-[40px] bg-[#EBF3FE] rounded-[50px] flex items-center justify-center box-border mr-[8px]"
          >
            <i class="iconfont icon-fenxiang mr-[4px] text-[18px] text-[#3887F5]" />
            <div class="text-[16px] text-[#3887F5]">
              立即分享
            </div>
          </div>
          <div 
            class="flex-1 h-[40px] bg-[linear-gradient(_116deg,_#71B5FF_0%,_#2676E4_100%)] rounded-[50px] text-[16px] text-[#FFFFFF] flex items-center justify-center box-border"
            @click="handleClick"
          >
            立即抢单
          </div>
        </div>
      </div>
    </div>
    <slot />
  </div>
</template>

<script>
import { amountFilterTwo } from "@/libs/filter";
import { COOPERATION_RECEIVE_STATUS, COOPERATION_TYPE_OBJ } from "@/enum";
import AppButtonShare from "@/components/AppComponents/AppButtonShare/index.vue";

export default {
  name: "CollaborationItem",
  components: { AppButtonShare },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    /* 是否需要置灰 协作列表已匹配需要置灰*/
    needMatched: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    /* 协作状态 文案*/
    collaborationStatus() {
      return ({
        [COOPERATION_TYPE_OBJ.CASE_COLLABORATION.value]: COOPERATION_TYPE_OBJ.CASE_COLLABORATION.label,
        [COOPERATION_TYPE_OBJ.CHECK_FILES_IN_DIFFERENT_PLACES.value]: COOPERATION_TYPE_OBJ.CHECK_FILES_IN_DIFFERENT_PLACES.label,
      }[this.data.type]) || "-";
    },
    /* 接单状态*/
    receiveStatus() {
      return COOPERATION_RECEIVE_STATUS[this.data.status] || { label: "-", color: "#999999" };
    },
    /* 是否是待接单*/
    isPendingOrders() {
      return this.data.status === 1;
    },
    /** 是否需要置灰 */
    isGray() {
      return this.needMatched && !this.isPendingOrders;
    },
    jdLawyerHeadImg(){
      return (this.data?.jdLawyerHeadImg.split(",") || []).slice(0, 3);
    },
  },
  methods: {
    amountFilterTwo,
    handleClick(){
      this.$emit("handleClick", { data: this.data, isPendingOrders: this.isPendingOrders });
    },
    handleShare(){
      this.$emit("share", this.data);
    }
  },
};
</script>

<style lang="scss" scoped>
.ash{
  opacity: 0.6;
}
</style>
