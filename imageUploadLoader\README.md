# 图片自动上传插件

[core.js](core.js)：为核心函数包，旨在兼容不同的打包框架，把核心函数独立出来。

[index.js](index.js)：提供两个函数。

- templateImagePlugin：用于vue-loader的扩展插件。
- babelPluginPath：用于babel的扩展插件。


## 使用方法

分别在`vue.config.js`和`babel.config.js`中引入插件。

### vue.config.js

在 chainWebpack 中引入插件。

```javascript
module.exports = {
  chainWebpack: (config) => {
    config.module.rule("vue").use("vue-loader").tap(templateImagePlugin(config)).end();
  }
};
```

### babel.config.js

在 plugins 中引入插件。

```javascript
const plugins = [babelPluginPath(config)];
```

### config 参数

`modPath`：储存在oss对应的文件夹下，该表示主要是用于区分不同的项目上传的图片。

## 注意事项

### require 引入

通过`require()`动态引入的图片，如果中间有通过变量引入的，那么图片不会被正确上传，例如：

```javascript
const img = [require(`../static/img/${name}.png`)];
```

推荐使用下面的写法：

```javascript
const imageUrls = [require("../static/img/1.png"), require("../static/img/2.png")];

return imageUrls[index];
```

使用这种写法时，图片会被正确上传。

### 异步上传

由于上传文件是异步行为，所以在第二次编译时才会使用oss地址，如果是在开发时该行为不会受到影响。

但如果没有运行开发模式，替换了图片后直接打包，那么第一次打包出的文件**依然用的本地图片**，可能会导致包体积增大。

## 缓存文件

在项目的跟目录下有一个 `state-lock.json` 文件，该文件用于记录上传后的图片地址，下次上传时，如果读取到该文件已经被上传，那么直接读取oss上的地址，不会再次上传。
