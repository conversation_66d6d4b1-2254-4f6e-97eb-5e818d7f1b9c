<template>
  <div
    v-if="show"
    class="w-[343px] bg-[rgba(0,0,0,0.75)] rounded-[8px] px-[16px] py-[10px] box-border flex items-center"
  >
    <img
      alt=""
      class="w-[16px] h-[16px] block shrink-0"
      src="../img/close-white.png"
      @click="handleClose"
    >
    <div class="text-[13px] text-[#FFFFFF] mx-[8px]">
      个人资料评分{{ score }}分，完善资料可提升网站曝光排名
    </div>
    <div
      class="w-[52px] h-[20px] bg-[#EB4738] rounded-[32px] text-[12px] text-[#FFFFFF] flex items-center justify-center"
      @click="turnToProfileInfoPage"
    >
      去完善
    </div>
  </div>
</template>

<script>
import { lawyerDataGetScore } from "@/api/user";
import { turnToProfileInfoPage } from "@/libs/turnPages";

export default {
  name: "HomeProfileScore",
  data() {
    return {
      score: 0,
      show: false,
    };
  },
  created() {
    this.getScore();
  },
  methods: {
    turnToProfileInfoPage,
    /** 获取评分 */
    getScore() {
      // 是否是首次进入
      const homeProfileScoreFirst = uni.getStorageSync("homeProfileScoreFirst");

      if (homeProfileScoreFirst) {
        this.show = false;
        return;
      }

      this.show = true;

      lawyerDataGetScore().then((res) => {
        this.score = res.data;
      });
    },
    /**
     * 关闭
     * 底部的悬浮引导资料完善卡片，用户手动关闭则不再显示，不关闭则一直显示！！
     */
    handleClose() {
      this.show = false;
      uni.setStorageSync("homeProfileScoreFirst", true);
    },
  },
};
</script>
