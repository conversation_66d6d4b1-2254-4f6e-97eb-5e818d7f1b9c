import { requestCommon } from "@/libs/axios";
import { isArray, isObj } from "@/libs/basics-tools";
import { clearNewUserStateStorage, getUserTokenStorage } from "@/libs/token";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath";

class LoginPoint {
  constructor() {
    this._loginPointData = uni.getStorageSync("loginPointData");
  }


  get loginPointData() {
    return this._loginPointData || {};
  }

  set loginPointData(data) {
    uni.setStorageSync("loginPointData", data);
    this._loginPointData = data;
  }

  buryPointLoginIn() {
    return buryPointChannelBasics(
      {
        code: "LAW_APPLET_PAGE_LOGIN",
        type: 1
      },
      {
        special: true
      }
    ).then(({ data = {} }) => {
      this.loginPointData = data;
      return data;
    });
  }
}

export const loginPoint = new LoginPoint();

/**
 * 获取当前的路由栈。
 * 这个函数首先调用 `getCurrentPages` 来获取当前的页面栈。
 * 如果页面栈存在，那么它会返回一个由页面路由组成的字符串，页面路由之间用逗号分隔。
 * 如果页面栈不存在，那么它会返回 null。
 *
 * @function getRouterStack
 * @returns {string|null} 返回一个由页面路由组成的字符串，或者在页面栈不存在时返回 null。
 */
function getRouterStack() {
  const pages = getCurrentPages();

  if (pages) return pages.map(item => item.route).join(",");

  return null;
}

/**
 * 渠道埋点类型
 * @type {{VI: string, IN: string, CK: string, CA: string}}
 */
export const BURY_POINT_CHANNEL_TYPE = {
  /** 曝光 */
  VI: "VI",
  /** 激活 */
  IN: "IN",
  /** 点击 */
  CK: "CK",
  /** 转化 */
  CA: "CA",
};

/**
 * CK-渠道统一埋点 无特殊要求 以后埋点 都走此接口
 * @param datas
 * @param options
 * @returns {Promise<unknown>}
 */
export const buryPointChannelBasics = (datas = [], options = {}) => {
  if (!getUserTokenStorage()) clearNewUserStateStorage();
  let data = {
    role: 1,
    type: 2
  };
  if (isArray(datas)) {
    data["code"] = datas[0];
    data["behavior"] = datas[1];
  } else if (isObj(datas)) {
    // const newUser = getNewUserStateStorage()

    // 不是新用户时，则不传入extra
    // const params = newUser
    //   ? { extra: { newUser, ...datas.extra } }
    //   : { extra: { ...datas.extra, ...(getUserTokenStorage() ? {} : { newUser: true }) } }

    // 埋点透传参数
    const buryPointQuery = uni.getStorageSync("buryPointQuery") || {};

    const { fromPage, clear } = options;
    // 从options中删除fromPage，避免埋点数据中出现fromPage字段
    delete options.fromPage;
    delete options.clear;

    /** 新增的归因参数 */
    let newFromPage = {};

    fromPage &&
      (newFromPage = {
        fromPage: buryPointTransformationPath.getDataSources({ clear })
      });

    const params = {
      extra: {
        // 添加时间戳 毫秒级
        timestamp: new Date().getTime(),
        path: getRouterStack(),
        ...datas.extra,
        ...buryPointQuery,
        ...newFromPage,
        ...loginPoint.loginPointData
      }
    };
    if (getUserTokenStorage()) {
      params.extra.newUser = true;
    } else {
      params.extra.deviceIdAppletNewUser = true;
    }
    data = { ...data, ...datas, ...params };
  }
  console.log("埋点数据===========", data);

  const { special = false } = options;
  delete options.special;

  if (special) {
    return requestCommon.post("/burypoint/specialPoint", data, options);
  } else {
    return requestCommon.post("/burypoint/channel/point", data, options);
  }
};


/* im埋点状态*/
export const IM_STATUS = {
  /** 认证 */
  AUTHENTICATED: 2,
  /** 认证失败 */
  AUTHENTICATION_FAILED: 3,
  /** 认证成功 */
  AUTHENTICATION_SUCCESS: 4,
  /** 错误捕获 */
  ERROR_CATCH: 6,
};
