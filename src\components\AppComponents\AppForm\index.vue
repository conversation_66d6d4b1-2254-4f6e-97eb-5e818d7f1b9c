<template>
  <u-form
    :borderBottom="borderBottom"
    :errorType="errorType"
    :labelAlign="labelAlign"
    :labelPosition="labelPosition"
    :labelStyle="labelStyle"
    :labelWidth="labelWidth"
    :model="model"
    :rules="rules"
  >
    <slot />
  </u-form>
</template>

<script>
import UForm from "@/uview-ui/components/u-form/u-form.vue";
import props from "@/uview-ui/components/u-form/props";

export default {
  name: "AppForm",
  components: { UForm },
  mixins: [props],
};
</script>

<style lang="scss" scoped></style>
