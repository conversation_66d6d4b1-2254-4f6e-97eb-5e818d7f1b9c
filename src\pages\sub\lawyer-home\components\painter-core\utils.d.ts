import { IView } from "./pen";
export declare function toPx(str: string | number, baseSize?: number): number;
export declare function setStringPrototype(screenK?: number, scale?: number): void;
interface Injection {
    loadImage: (url: string) => Promise<{
        img: any;
        width: number;
        height: number;
    }>;
    getRatio: () => number;
    customActions: {
        [type: string]: {
            layout: (view: IView, viewRects: {
                [id: string]: {
                    width: number;
                    height: number;
                    left: number;
                    top: number;
                    right: number;
                    bottom: number;
                };
            }) => {
                left: number;
                top: number;
                right: number;
                bottom: number;
            };
            draw: (view: IView, ctx: CanvasRenderingContext2D) => Promise<void>;
        };
    };
}
export declare let injection: Injection;
export declare function initInjection(inject: Partial<Injection>): void;
export declare function substr(str: any, start: any, length: any): any;
export declare function substring(str: any, start: any, end: any): any;
export declare function plusPx(origin?: string, plus?: string, baseSize?: number): string;
export {};
