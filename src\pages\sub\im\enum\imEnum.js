

/* im消息类型*/
export const imMessageType = {
  /* 用户发送手机号*/
  SERVER_CONTACT_PHONE: "server_contact_phone",
  /* 律师发送服务卡片*/
  SERVER_ONE_TO_ONE: "server_one_to_one",
  /** 2.2.0 im 支付成功 */
  SEVER_OF_PAY_SUCCESS: "sever_of_pay_success",
  /* 用户评价 */
  SERVER_EVALUATE: "lawyer_rate",
  /* 律师电话 */
  CONTACT_INFO_LAWYER: "contact_info_lawyer",
  // 退款
  REFUND_TIP: "refund_tip",
  /* 追问付费服务发送提示*/
  SERVER_QA_APPEND_COUNT_TIP: "server_qa_append_count_tip",
  /* 敏感词提示消息*/
  SERVER_QA_AUTO_SEND_CARD: "server_qa_auto_send_card"
};


/* 不需要显示头像的card*/
export const personLessMsgCardMap = [
  imMessageType.SERVER_EVALUATE,
  imMessageType.REFUND_TIP,
  "server_lawyer_comment",
  "invite_comment",
  "invite_complete",
  "lawyer_services_end",
  "service_instance",
  "server_upgrade",
  "one-on-one_qa",
  "server_QA_addCount",
  "server_change_lawyer_tips",
  imMessageType.SEVER_OF_PAY_SUCCESS,
  imMessageType.SERVER_QA_APPEND_COUNT_TIP,
  imMessageType.SERVER_QA_AUTO_SEND_CARD
];

/* im 刷新代码指令code*/
export const imDirectivesFlushedCodes = [
  "user_refund_already",
  "user_change_lawyer_apply",
  "user_change_lawyer_revocation",
  "user_change_lawyer_already",
  "lawyer_start_server",
  "user_refunding",
  "server_complete",
  "server_refresh"
];

/* 这些code指令要跟新消息 */
export const imDirectivesFlushedCodesNeedUpdate = [
  "EXCHANGE_MESSAGE_CMD"
];



/* !key是自定义消息的style  value是im消息对应的展示卡片*/
export const imCardMap = {
  [imMessageType.SERVER_CONTACT_PHONE]: "UserPhoneCard",
  [imMessageType.SERVER_ONE_TO_ONE]: "ServiceCard",
  [imMessageType.CONTACT_INFO_LAWYER]: "ContactInfoLawyerCard",
  [imMessageType.REFUND_TIP]: "refundTipCard",
  /* 自动回复*/
  lawyer_auto_reply: "LawyerAutoReplyCard",
  /* 法律意见书*/
  server_lawyer_idea: "SubmissionsCard",
  /* 用户提问卡片*/
  server_QA_asked: "UserQuestionsCard",
  [imMessageType.SERVER_QA_APPEND_COUNT_TIP]: "server-qa-append-count-tip-card",
  /* 敏感词提示消息*/
  [imMessageType.SERVER_QA_AUTO_SEND_CARD]: "sensitive-word-tip-card",
  /* 用户已申请换律师，服务已关闭*/
  server_change_lawyer_tips: "refundTipCard",
  /* 律师评价*/
  server_lawyer_comment: "CaseSourceEvaluationCard",
  /* 用户评论*/
  [imMessageType.SERVER_EVALUATE]: "UserReviews",
  /* 请求联系方式卡片 */
  EXCHANGE_MESSAGE: "EXCHANGE_MESSAGE",
  /* 微信或者手机号交换卡片 */
  EXCHANGE_MESSAGE_INFO: "EXCHANGE_MESSAGE_INFO",
};
