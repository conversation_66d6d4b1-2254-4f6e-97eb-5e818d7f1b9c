<template>
  <div>
    <u-picker
      ref="uPicker"
      :columns="columns"
      :defaultIndex="defaultIndex"
      :show="show"
      keyName="name"
      round="16"
      @cancel="show = false"
      @change="changeHandler"
      @confirm="confirm"
    />
    <div @click="show = true">
      <slot :text="text">
        <app-field
          :value="text"
          align="left"
          arrow
          :space="space"
          color="#333333"
          placeholder="请选择"
          placeholderColor="#CCCCCC"
        />
      </slot>
    </div>
  </div>
</template>

<script>
import UPicker from "@/uview-ui/components/u-picker/u-picker.vue";
import { getArea } from "@/api";
import AppField from "@/components/AppComponents/AppField/index.vue";
import { getLocation } from "@/libs/getLocation";
import { isEmpty } from "lodash-es";

export default {
  name: "AppArea",
  components: { AppField, UPicker },
  props: {
    value: {
      type: String,
      default: "",
    },
    space: {
      type: Number,
      default: 0,
    },
    /**
     * 默认值格式类型
     * cityCode: 510100
     * cityName: "成都"
     * provinceCode: 510000
     * provinceName: "四川省"
     */
    defaultValue: {
      type: [String, Object],
      default: () => ({}),
    },
    /**
     * ! 是否有默认值 一定要设置为true才能正确赋值默认值
     * 有默认值则不调用定位
     */
    hasDefault: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      show: false,
      columns: [],
      /** 选择的值 */
      selected: [],
      /** 默认值 */
      defaultIndex: [0, 0],
    };
  },
  computed: {
    /** 如果有值，则显示 */
    text() {
      // 没有默认值，则显示组件内部选择的值
      return (this.selected?.[1]?.provinceName || "") + (this.selected?.[1]?.name || "");
    },
  },
  watch: {
    // 因为是异步获取的数据，所以需要监听数据变化
    defaultValue: {
      handler(val) {
        // 如果在有默认值的情况下，value不为空，则赋值
        // 这里是为了防止请求多次的情况
        if (this.hasDefault) {
          if (!isEmpty(val)) {
            this.setDefaultIndex();
          }
        } else {
          this.setDefaultIndex();
        }
      },
      deep: true,
      // 首次加载就触发，起到created的作用
      immediate: true,
    },
  },
  methods: {
    /** 赋默认值 */
    async setDefaultIndex() {
      const { data: province } = await getArea({ type: 1 });

      let city;

      try {
        let local;

        // 如果有默认值，则使用默认值
        if (this.hasDefault) {
          local = this.defaultValue;
          this.selected = [
            {},
            {
              provinceName: local.provinceName,
              name: local.cityName,
            },
          ];
          console.log(this.defaultValue, "this.defaultValue");
        } else {
          // ! 无默认值则调用定位
          local = await getLocation();
        }

        const { data: cityData } = await getArea({
          type: 2,
          code: local.provinceCode,
        });

        city = cityData;

        // ! 如果是调用的定位，则设置默认值
        this.defaultIndex = [
          province.findIndex((item) => item.code === local.provinceCode),
          city.findIndex((item) => item.code === local.cityCode),
        ];
      } catch (e) {
        const { data: cityData } = await getArea({
          type: 2,
          code: province[0].code,
        });

        city = cityData;
      }

      this.columns = [province, city];
    },
    changeHandler(e) {
      const {
        columnIndex,
        value,
        // 微信小程序无法将picker实例传出来，只能通过ref操作
        picker = this.$refs.uPicker,
      } = e;

      console.log(e);

      // 当第一列值发生变化时，变化第二列(后一列)对应的选项
      if (columnIndex === 0) {
        getArea({ type: 2, code: value[0].code }).then((res) => {
          const { data } = res;

          // picker为选择器this实例，变化第二列对应的选项
          picker.setColumnValues(1, data);
        });
      }
    },
    // 回调参数为包含columnIndex、value、values
    confirm(e) {
      console.log(e, "选择地区后的值");
      this.selected = e.value;

      this.$emit("confirm", {
        workCity: e.value[1].code,
        cityName: e.value[1].name,
      });
      this.show = false;
    },
  },
};
</script>

<style lang="scss" scoped></style>
