<template>
  <div class="page-container">
    <!-- 问题部分 -->
    <div class="ask__header">
      <div class="header">
        <div class="content">
          <div class="flex flex-align-center flex-space-between">
            <span class="content__label"><img
              alt=""
              class="tag"
              src="img/Group913@2x(3).png"
            >{{ details.typeLabel }}</span>
            <div
              v-if="answerContent.length"
              class="content__right flex flex-align-center"
            >
              <img
                alt=""
                class="content__right__img"
                src="img/<EMAIL>"
              >
              <div class="content__right__text">
                律师已回复
              </div>
            </div>
          </div>
          <div class="content__detail">
            {{ details.detail }}
          </div>
        </div>
        <div class="info">
          <div class="info__date">
            {{ details.createTime }}
          </div>
        </div>
      </div>
    </div>
    <!-- 回答部分 -->
    <div class="answer position-relative">
      <img
        alt=""
        class="tag"
        src="img/Group913@2x(1).png"
      >
      <div
        v-if="!answerContent.length"
        class="wait-box"
      >
        <template v-if="details.qaMessagePayState === 0">
          <img
            alt=""
            class="img"
            src="img/bg_00000_iSpt.png"
          >
          <div class="text-999999">
            律师正在分析您的问题
          </div>
          <div class="text-999999">
            请耐心等待...
          </div>
        </template>
        <template v-else>
          <img
            alt=""
            class="img"
            src="img/bg_00000_iSpt2.png"
          >
          <div class="text-666666">
            平台已将您的问题<span class="text-[#EB4738]">置顶曝光</span>
            ，律师查看后会有回复
          </div>
        </template>
      </div>
      <div>
        <div
          v-for="item in answerContent"
          :key="item.id"
          class="answer__item position-relative"
        >
          <img
            v-if="item.hasAccepted === 1"
            alt=""
            class="answer__accept"
            src="@/pages/ask-details/components/ask-header/img/accept.png"
          >
          <div class="flex flex-space-between flex-align-center">
            <div
              class="lawyer__info flex flex-align-center"
              @click="toLawyerHome(item.lawyerId)"
            >
              <img
                :src="item.lawyerHeadImg"
                alt=""
                class="lawyer__info__header"
              >
              <div>
                <div class="lawyer__info__name">
                  {{ item.lawyerName }} 律师
                </div>
              </div>
            </div>
            <div class="flex flex-align-center">
              <img
                v-if="showAdoptBtn"
                alt=""
                class="answer__bottom__button__accept"
                src="img/accept-button.png"
                @click="handleAccept(item)"
              >
              <div
                class="answer__bottom__button"
                @click="toImPage(item)"
              >
                追问Ta
              </div>
            </div>
          </div>
          <div class="answer__content">
            <span class="answer__content__label">律师解答</span>
            <span class="parting">|</span>{{ item.content }}
          </div>
          <div class="answer__bottom flex flex-align-center flex-space-between">
            <div class="answer__bottom__time">
              解答于 {{ item.createTimeDesc }}
            </div>
            <div class="flex flex-align-center">
              <div
                v-if="item.hasAccepted === 1"
                class="answer__bottom__icon flex flex-align-center"
              >
                <img
                  alt=""
                  class="answer__bottom__icon__img"
                  src="@/pages/ask-details/components/ask-header/img/Frame938.png"
                >
                <div class="answer__bottom__icon__text">
                  已采纳
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <app-modal
      :show="showPopup"
      align="center"
      cancelText="我再想想"
      confirmText="确认采纳"
      content="您确定要采纳此律师的回复吗？采纳之后律师将得到您的悬赏奖励"
      title="采纳确认"
      @cancel="showPopup = false"
      @confirm="clickAccept"
    />
    <!-- 已悬赏无回复 -->
    <div v-if="qaMessagePay && !answerContent.length">
      <div class="pt-[62px]">
        <p class="font-bold px-[16px] text-[15px] text-[#333333]">
          以下律师已分析您的问题...
        </p>
        <p class="text-[13px] pt-[4px] px-[16px] text-[#999999]">
          如等待时间过长，推荐本地优选律师为您深度分析问题
        </p>
        <div class="mt-[12px] flex items-center overflow-x-auto min-h-[143px]">
          <div
            v-for="item in lawyerList"
            :key="item.id"
            class="w-[138px] ml-[16px] p-[12px] rounded-[8px] border-[0.5px] border-solid border-[#E3E3E3] box-border"
          >
            <div class="flex items-center">
              <img
                  mode="aspectFill"
                :src="item.imgUrl"
                alt=""
                class="w-[32px] h-[32px] block rounded-full"
              >
              <div class="ml-[8px]">
                <div class="font-bold text-[14px] text-[#333333]">
                  {{ item.realName }}
                </div>
                <div class="text-[10px] text-[#666666]">
                  执业{{ item.workTime }}年
                </div>
              </div>
            </div>
            <div class="mt-[8px] flex items-center">
              <div class="flex items-center">
                <img
                  alt=""
                  class="w-[12px] h-[12px] block"
                  src="@/pages/lawyer-im/components/lawyer-im/im-chatroom-type/card-invite-comment/img/icon.png"
                >
                <div class="font-bold text-[11px] text-[#F78C3E] ml-[2px]">
                  {{ (item.score || 0).toFixed(1) }}
                </div>
              </div>
              <div class="text-[11px] text-[#666666] ml-[12px] flex-shrink-0">
                已服务<span class="font-bold text-[#333333]">{{
                  item.serverNum
                }}</span>人
              </div>
            </div>
            <div class="mt-[4px] text-[10px] text-[#666666] min-h-[28px]">
              擅长：{{ (item.workField || []).join("、") }}
            </div>
            <div
              class="mt-[8px] w-[106px] h-[23px] rounded-[68px] border-[0.5px] border-solid border-[#3887F5] text-[11px] text-[#3887F5] flex items-center justify-center mx-auto"
              @click="toLawyerHomeFake(item)"
            >
              立即咨询
            </div>
          </div>
        </div>
      </div>
      <div class="h-[100px]" />
      <img
        alt=""
        class="bottom-img !fixed"
        mode="widthFix"
        src="img/<EMAIL>"
        @click="handleShow"
      >
    </div>

    <!-- 未悬赏无回复 -->
    <div
      v-if="!qaMessagePay && !answerContent.length"
      class="bottom-img"
    >
      <!-- 电话咨询      -->
      <div
        class="telephone-consultation flex flex-space-between flex-align-center"
      >
        <img
          :src="telephoneConsultationService.icon"
          alt=""
          class="logo"
        >
        <div class="flex-1">
          <p class="flex flex-align-center prod-name">
            电话咨询
            <i class="money">¥{{ priceNumber(telephoneConsultationService.servicePrice) }}</i>
          </p>
          <p class="desc">
            电话极速沟通，未服务可退款
          </p>
        </div>
        <div
          class="btn"
          @click="handleClick(telephoneConsultationService)"
        >
          立即咨询
        </div>
      </div>
      <!--悬赏      -->
      <img
        alt=""
        class="bounty"
        mode="widthFix"
        src="img/<EMAIL>"
        @click="payReward"
      >
    </div>
    <fastPayPopup
      v-model="show"
      :selectedService="selectedService"
      @click="handleClick"
    />
    <u-safe-bottom />
    <telephone-consultation-popup />
  </div>
</template>

<script>
import { qaMessageDetail } from "@/api/ask.js";
import myQuestionMixin from "@/pages/sub/lawyer-home/my-question/mixin/myQuestionMixin.js";
import AppModal from "@/components/AppComponents/app-modal/index.vue";
import { qaMessageContinueAsk } from "@/api/my-consultation-new.js";
import { toImChatPage } from "@/libs/turnPages.js";
import { qaMessageAcceptedQaMessageReply } from "@/api/im.js";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import fastPayPopup from "@/components/fastPayPopup/index.vue";
import { serviceManegeInfoCommon } from "@/api";
import { sceneType } from "@/libs/config";
import {
  getALocalLawyer,
  toConfirmOrder,
  toLawyerFakeImBefore,
} from "@/libs/tools";
import { priceNumber } from "@/libs/tool";
import TelephoneConsultationPopup from "@/components/telephone-consultation-popup/index.vue";

export default {
  name: "MyQuestion",
  components: {
    TelephoneConsultationPopup,
    USafeBottom,
    AppModal,
    fastPayPopup,
  },
  mixins: [myQuestionMixin],
  data() {
    return {
      lawyerList: [],
      details: {},
      showPopup: false,
      /** 获取数据 */
      getQaMessageDetail: null,
      /** 当前选中的数据 */
      currentData: null,
      selectedService: {},
      show: false,
      id: null,
      telephoneConsultationService: {},
    };
  },
  onLoad(params) {
    this.getQaMessageDetail = () => {
      qaMessageDetail({
        qaMessageId: params.id,
      }).then(({ data }) => {
        this.details = data;
      });
    };
    this.getQaMessageDetail();
    serviceManegeInfoCommon(sceneType.ptzx2).then(({ data }) => {
      this.selectedService = data;
    });

    /* 未悬赏无回复 电话咨询*/
    serviceManegeInfoCommon(sceneType.sydb).then(({ data }) => {
      this.telephoneConsultationService = data;
    });
    this.id = params.id;
    this.getTabData();
  },
  methods: {
    priceNumber,
    /** 获取数据 */
    async getTabData() {
      getALocalLawyer().then((list) => {
        this.lawyerList = list;
      });
    },
    toLawyerHomeFake(data) {
      toLawyerFakeImBefore({
        lawyerInfo: data,
        itemClassType: 1,
      });
    },
    handleShow() {
      this.show = true;
    },
    /** 点击采纳按钮 */
    handleAccept(data) {
      this.showPopup = true;
      this.currentData = data;
    },
    /** 确认采纳 */
    clickAccept() {
      qaMessageAcceptedQaMessageReply({
        qaMessageReplyId: this.currentData.id,
      }).then(() => {
        this.showPopup = false;
        this.getQaMessageDetail();
        uni.showToast({
          title: "采纳成功",
          icon: "none",
        });
      });
    },
    /** 点击追问 */
    toImPage(obj) {
      qaMessageContinueAsk({
        qaMessageReplyId: obj.id,
      }).then(({ data }) => {
        // 去免费咨询IM页面
        toImChatPage({
          lawyerId: obj.lawyerId,
          conversationId: data.imSessionId,
          caseSourceId: data.caseSourceServerId,
        });
      });
    },
    /** 付费悬赏 */
    payReward() {
      serviceManegeInfoCommon(sceneType.wdxs).then(({ data }) => {
        this.handleClick(data);
      });
    },
    handleClick(data) {
      toConfirmOrder({
        serviceCode: data?.serviceCode,
        synWdId: this.id,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  position: relative;
  min-height: 100vh;
  background: #fff;

  .bottom-img {
    position: absolute;
    bottom: 16px;
    left: 16px;
    width: 343px;
    .bounty {
      padding-top: 12px;
      width: 343px;
    }
    .telephone-consultation {
      background: #eef5ff;
      border-radius: 8px 8px 8px 8px;
      padding: 12px;
      .logo {
        width: 36px;
        height: 36px;
        margin-right: 4px;
        border-radius: 6px;
        overflow: hidden;
      }
      .prod-name {
        font-size: 15px;
        font-weight: 600;
        color: #333333;
        .money {
          font-size: 13px;
          font-weight: 400;
          color: #f34747;
          padding-left: 4px;
          font-style: normal;
        }
      }
      .desc {
        font-size: 12px;
        font-weight: 400;
        padding-top: 2px;
        color: #999999;
      }
      .btn {
        width: 72px;
        line-height: 29px;
        background: #3887f5;
        border-radius: 40px 40px 40px 40px;
        font-size: 12px;
        font-weight: 400;
        color: #ffffff;
        text-align: center;
      }
    }
  }
}

.answer__accept {
  top: 8px;
  right: 74px;
  position: absolute;
  display: block;
  width: 67px;
  height: 53px;
}

.answer {
  padding-top: 20px;
  .lawyer__info__header {
    width: 28px;
    height: 28px;
  }

  .lawyer__info__name {
    font-size: 15px;
  }
}

.answer__bottom__button__accept {
  width: 66px;
  height: 27px;
  display: block;
  margin-right: 8px;
}

.content__right {
  &__img {
    width: 12px;
    height: 12px;
    margin-right: 4px;
  }

  &__text {
    font-size: 13px;
    font-weight: 400;
    color: #22bf7e;
  }
}
.wait-box,
.answer {
  .tag {
    top: 0;
    left: 16px;
    width: 22px;
    height: 24px;
    position: absolute;
    z-index: 1;
  }
}
.wait-box {
  position: relative;
  border-radius: 16px 16px 0 0;
  margin-top: -16px;
  background: #fff;
  text-align: center;
  font-size: 13px;
  font-weight: 400;

  .img {
    width: 140px;
    height: 140px;
    margin-top: 36px;
  }
}
</style>
