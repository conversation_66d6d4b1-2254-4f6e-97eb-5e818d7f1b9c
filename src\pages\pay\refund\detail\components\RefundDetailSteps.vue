<template>
  <div class="steps">
    <ul class="steps-box">
      <li
        v-for="(item, index) in title"
        :key="index"
        class="steps__item"
        :class="[
          {
            'steps__item--active': current > index,
          },
        ]"
      >
        <div class="number">
          <div
            v-if="current > index"
            class="iconfont icon-a-zhengquewancheng"
          />
          <div v-else>
            {{ index + 1 }}
          </div>
        </div>
        <div class="text">
          {{ item }}
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: "RefundDetailSteps",
  props: {
    current: {
      type: Number,
      default: 1,
    },
    title: {
      type: Array,
      default: ["申请退单", "平台审核", "审核通过"],
    },
  },
};
</script>

<style scoped lang="scss">
.steps {
  width: 343px;
  height: 84px;
}

.steps-box {
  height: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  place-items: center;
}

.steps__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;

  .number {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #cccccc;
    color: #fff;
  }

  .text {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 400;
    color: #999999;
  }

  &:nth-child(1) {
    &::before {
      content: "";
      position: absolute;
      top: 11px;
      left: 35px;
      width: 100px;
      height: 2px;
      background-color: #cccccc;
    }
  }

  &:nth-child(2) {
    &::before {
      content: "";
      position: absolute;
      top: 11px;
      left: 35px;
      width: 100px;
      height: 2px;
      background-color: #cccccc;
    }
  }


  &--active {
    .number {
      background-color: #3887f5;
    }

    .text {
      color: #333333;
    }

    &:nth-child(1) {
      &::before {
        background-color: #3887f5;
      }
    }

    &:nth-child(2) {
      &::before {
        background-color: #3887f5;
      }
    }
  }

}
</style>
