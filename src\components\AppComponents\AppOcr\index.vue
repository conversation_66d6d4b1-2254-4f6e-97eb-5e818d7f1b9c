<template>
  <div>
    <ocr-navigator
      :opposite="opposite"
      certificateType="idCard"
      @onSuccess="success"
    >
      <slot />
    </ocr-navigator>
  </div>
</template>

<script>
import { getUserTokenStorage } from "@/libs/token";
import { axiosBaseHeadersConfig } from "@/libs/config";

export default {
  name: "AppOcr",
  props: {
    /** 是否显示身份证的反面，默认为 true 显示反面 */
    opposite: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    success(e) {
      console.log(e.detail, "请求成功");

      let uploadObj = {}; // uploadFile 参数
      let token = getUserTokenStorage();
      uploadObj = {
        filePath: e.detail.image_path,
        name: "file",
      };

      // * 将临时地址上传到服务器
      uni.uploadFile({
        url: process.env.VUE_APP_ENV_BASE_URL + "/core/upload/workRemark/image",
        Accept: "multipart/form-data",
        header: {
          "Content-Type": "multipart/form-data",
          token: token,
          osversion: axiosBaseHeadersConfig.osVersion,
        },
        ...uploadObj,
        success: (res) => {
          let data;
          // 如果是反面
          if (this.opposite) {
            data = {
              authority: e.detail.authority.text,
              validDate: e.detail.valid_date.text,
              imageUrl: JSON.parse(res.data).data,
            };
          } else {
            data = {
              name: e.detail.name.text,
              gender: e.detail.gender.text,
              nationality: e.detail.nationality.text,
              address: e.detail.address.text,
              id: e.detail.id.text,
              imageUrl: JSON.parse(res.data).data,
            };
          }

          this.$emit("success", data);
        },
        fail: (error) => {
          console.log("error:", error);
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
