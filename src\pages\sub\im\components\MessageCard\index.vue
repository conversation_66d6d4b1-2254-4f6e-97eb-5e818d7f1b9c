<template>
  <div
    v-if="componentName"
    :key="data.msgId"
    class="message-card flex"
    :class="'message-card_'+position"
  >
    <img
      v-if="!isNull(avatar)"
      class="avatar"
      :src="avatar"
      alt=""
    >
    <cards
      :componentName="componentName"
      :data="data"
      :class="{'flex-1':position==='center'}"
      :theme="getTheme"
    />
  </div>
</template>

<script>
import { isNull } from "@/libs/basics-tools.js";
import Cards from "@/pages/sub/im/components/Cards/index.vue";
import { WS_MESSAGE_TYPE } from "yoc-im-web";
import { imCardMap } from "@/pages/sub/im/enum/imEnum.js";
import { customComputed } from "@/pages/sub/im/mixins/card-props.js";

export default {
  name: "MessageCard",
  components: { Cards },
  mixins: [customComputed],
  props: {
    avatar: {
      type: String,
      default: ""
    },
    position: {
      type: String,
      default: "center",
      validator: (value) => ["left", "right", "center"].indexOf(value) !== -1
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      componentName: ""
    };
  },
  computed: {
    getTheme() {
      return this.position === "left" ? "white" : "blue";
    },
    msgType() {
      return this.data.msgType;
    },
  },
  methods: {
    isNull,
    getComponentName(){
      switch (this.msgType) {
      /* 基本卡片*/
      case WS_MESSAGE_TYPE.TEXT:
        return "textCard";
      case WS_MESSAGE_TYPE.SYSTEM:
        return  "systemCard";
      case WS_MESSAGE_TYPE.IMG:
        return "imageCard";
      case WS_MESSAGE_TYPE.FILE:
        try {
          for (const i of this.allowType) {
            if (this.data.msg.fileName && this.data.msg.fileName.indexOf(i) > -1) {
              return "imageCard";
            }
          }
        } catch (e) {
          console.log(e);
        }
        return "";
        /* 自定义卡片*/
      case WS_MESSAGE_TYPE.CUSTOM:
        const TYPE = this.customExts.style;
        if (imCardMap[TYPE]) {
          return imCardMap[TYPE];
        }
        return "";
      default:
        return "";
      }
    }
  },
  mounted() {
    this.componentName = this.getComponentName();
    if(this.componentName){
      this.$emit("loadComponent", this.componentName);
    }
  }
};
</script>

<style scoped lang="scss">
.message-card{
  &_left{
    .avatar{
      margin-right: 8px;
    }
  }
  &_right{
    flex-direction: row-reverse;
    .avatar{
      margin-left: 8px;
    }
  }
  &_center{
    justify-content: center;
  }
  .avatar{
    width: 36px;
    height: 36px;
    border-radius: 18px 18px 18px 18px;
    overflow: hidden;
  }
}
</style>
