import apiVersion from "./apiVersion";

/** 缓存key */
export const LOCAL_STORAGE_KEY = {
  /** 最近联系的律师 */
  RECENT_LAWYER: "recentLawyerList"
};

export const axiosBaseHeadersConfig = {
  // #ifdef MP-WEIXIN
  osVersion: "16",
  // #endif
  platform: "applets",
  ...apiVersion.origin,
};

/**
 * 分享字典参数
 */
export const SHARE_DICTIONARY = {
  /* 首页 */
  INDEX: "INDEX",
  /* 协作详情 */
  COOPERATIONDETAIL: "COOPERATIONDETAIL",
};

/** 指标名称 */
export const INDICATOR_NAME = {
  /** 代理案件数 */
  caseTotal: "caseTotal",
  /** 常去法院统计 */
  courtName: "courtName",
  /** 案由分布统计 */
  caseReason: "caseReason",
  /** 最近案件数量统计 */
  recentCase: "recentCase",
  /** 标的额统计 */
  caseAmt: "caseAmt"
};

/* 协议连接*/
export const protocols = {
  /* 隐私政策*/
  law_privacy_protocol:
    process.env.VUE_APP_ENV_PROTOCOL + "law_privacy_protocol.html",
  /* 用户协议*/
  law_user_protocol:
    process.env.VUE_APP_ENV_PROTOCOL + "law_user_protocol.html",
  /* 购买协议*/
  law_buy_protocol:
    process.env.VUE_APP_ENV_PROTOCOL + "law_buy_protocol.html?header=0",
};

export const WS_MESSAGE_TYPE = {
  TEXT: 1,
  IMG: 2,
  VIDEO: 3,
  CUSTOM: 4,
  FILE: 5,
  SYSTEM: 6,
};

export const newServiceCode = {
  /** 律师1v1 */
  LAWYERCONSUL1TO1: 300001,
  /** 律师咨询 */
  LAWYERCONSULTATION: 100003,
  /** 案件委托 */
  ENTRUSTEDSERVICE: 100002,
  /** 极速咨询 */
  SPEEDCONSULTATION: 100001,
};

export const TRANSFORMATION_PATH = {
  // 'LANDPAGE': 1, //	落地页
  [newServiceCode["LAWYERCONSUL1TO1"]]: 2, //	找律师1v1
  [newServiceCode["LAWYERCONSULTATION"]]: 2, //	找律师
  [newServiceCode["SPEEDCONSULTATION"]]: 3, //	律师咨询
  [newServiceCode["ENTRUSTEDSERVICE"]]: 4, //	案件委托
};
/* 极速咨询订单中退款状态类型*/
export const speedRefundType = {
  /* 未退款 */
  UNREFUNDED: 2000,
  /* 退款中 */
  REFUNDING: 2001,
  /* 退款成功 */
  REFUNDSUCESS: 2002,
  /* 退款失败 */
  REFUNDFAILED: 2003,
  /** 已撤销 */
  REVOKED: 2004,
};

/**
 * 律师是否认证
 * 认证状态 1:待认证,2.已认证,3.未认证,4.认证失败
 */
export function lawyerCertStatus(certStatus) {
  return Number(certStatus) === 2;
}
