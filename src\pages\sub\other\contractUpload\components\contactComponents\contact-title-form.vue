<template>
  <div class="title-item">
    <p class="title">
      合同模板标题
    </p>
    <div class="input-content flex flex-align-center">
      <u--input
        v-model="title"
        :cursorSpacing="50"
        :disabled="disabled"
        :maxlength="20"
        :placeholder="placeholder"
        :showConfirmBar="false"
        autoHeight
        border="none"
        disabledColor="#ffffff"
        fixed
        placeholderStyle="font-size: 15px;color: #cccccc;"
      />
    </div>
  </div>
</template>

<script>


export default {
  name: "ContactTitleForm",
  props: {
    value: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      disabled: false,
      placeholder: "请输入"
    };
  },
  computed: {
    title: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit("input", value);
      },
    },
  }
};
</script>

<style lang="scss" scoped>
.title-item{
  .title{
    font-size: 16px;
    font-weight: 600;
    color: #333333;
    padding: 16px 0;
  }
  .input-content{
    width: 343px;
    height: 45px;
    background: #F5F5F7;
    border-radius: 8px 8px 8px 8px;
    font-size: 15px;
    box-sizing: border-box;
    padding: 0 12px;
  }
  //::v-deep .u-input {
  //  height: 45px !important;
  //  width: auto !important;
  //  border-radius: 8px !important;
  //}
}

</style>
