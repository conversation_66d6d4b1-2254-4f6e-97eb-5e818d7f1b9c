
export const rewardHost = {
  development: "http://testapi.imlaw.cn",
  production: ""
};


export const formatDate = (t) => {
  const now = new Date(t);
  const year = now.getFullYear();
  const month = now.getMonth() + 1;
  const date = now.getDate();
  const m = month < 9 ? "0" + month : month;
  const d = date < 9 ? "0" + date : date;
  return `${year}-${m}-${d}`;
};


// 顶部导航栏设置
export const setNavBar = (pageName = "法临网") => {

  // uni.setNavigationBarTitle({
  // 	 title:pageName+" - 法临网"

  // });

  // if (page.navigationBarTextStyle) {
  // uni.setNavigationBarTitle({
  // 	title:pageName
  // });
  // uni.setNavigationBarColor({
  // 	frontColor: navigationBarTextStyle.value === 'black' ? '#000000' : '#ffffff',
  // 	backgroundColor: navigationBarBackgroundColor.value
  // });
  // }
};


// 富文本图片问题处理
export const formatRichText = (html, obj) => {
  let newContent = html.replace(/<img[^>]*>/gi, function(match, capture) {
    match = match.replace(/style="[^"]+"/gi, "").replace(/style='[^']+'/gi, "");
    match = match.replace(/width="[^"]+"/gi, "").replace(/width='[^']+'/gi, "");
    match = match.replace(/height="[^"]+"/gi, "").replace(/height='[^']+'/gi, "");
    return match;
  });
  newContent = newContent.replace(/style="[^"]+"/gi, function(match, capture) {
    match = match.replace(/width:[^;]+;/gi, "max-width:100%;").replace(/width:[^;]+;/gi, "max-width:100%;");
    return match;
  });
  newContent = newContent.replace(/<br[^>]*\/>/gi, "");
  if (obj) {
    newContent = newContent.replace(/\<img/gi,
      "<img style=\\\"max-width:100%;height:auto;display:inline-block;margin:10rpx auto;\\\"");
  } else {
    newContent = newContent.replace(/\<img/gi,
      "<img style=\"max-width:100%;height:auto;display:inline-block;margin:10rpx auto;\"");
  }
  return newContent;
};

// H5端获取参数
export const getUrlSearch = name => {
  if (!name) return null;
  var after = window.location.search;
  after = after.substr(1) || window.location.hash.split("?")[1];
  if (!after) return null;
  if (after.indexOf(name) === -1) return null;
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
  var r = decodeURI(after).match(reg);
  if (!r) return null;
  return r[2];
};



// 获取今天
export const getNowDate = () => {
  const year = new Date().getFullYear(),
    month = (new Date().getMonth() + 1) < 10 ? "0" + (new Date().getMonth() + 1) : (new Date().getMonth() + 1),
    day = new Date().getDate() < 10 ? "0" + (new Date().getDate()) : (new Date().getDate());
  return year + "-" + month + "-" + day;
};

// 获取明天
export const getTomorrowDate = () => {
  var day3 = new Date();
  day3.setTime(day3.getTime() + 24 * 60 * 60 * 1000);
  const mouth = (day3.getMonth() + 1) < 10 ? "0" + (day3.getMonth() + 1) : (day3.getMonth() + 1);
  const day = day3.getDate() < 10 ? "0" + day3.getDate() : day3.getDate();
  var s3 = day3.getFullYear() + "-" + mouth + "-" + day;
  return s3;
};

// 计算天数
export const getDaysBetween = (sDate1, sDate2) => {
  var dateSpan, tempDate, iDays;
  sDate1 = Date.parse(sDate1);
  sDate2 = Date.parse(sDate2);
  dateSpan = sDate2 - sDate1;
  dateSpan = Math.abs(dateSpan);
  iDays = Math.floor(dateSpan / (24 * 3600 * 1000));
  return iDays;
};

// 获取两个时间段之间的时间数组
export const formatEveryDay = (start, end) => {
  let dateList = [];
  var startTime = getDate(start);
  var endTime = getDate(end);
  let minTime = startTime.getTime();
  let maxTime = endTime.getTime();
  for (let i = minTime; i <= maxTime; i += 24 * 60 * 60 * 1000) {
    var year = startTime.getFullYear();
    var month = startTime.getMonth() + 1 < 10 ? "0" + (startTime.getMonth() + 1) : startTime.getMonth() + 1;
    var day = startTime.getDate().toString().length == 1 ? "0" + startTime.getDate() : startTime.getDate();
    dateList.push(year + "-" + month + "-" + day);
  }
  return dateList;
};


function getDate(datestr) {
  var temp = datestr.split("-");
  var date = new Date(temp[0], temp[1] - 1, temp[2]);
  return date;
}

// 是否是在微信客户端
export const isWechat = () => {
  var ua = window.navigator.userAgent.toLowerCase();
  if (ua.match(/micromessenger/i) == "micromessenger") {
    return true;
  } else {
    return false;
  }
};
// 微信H5端支付
function jsApiCall(data, callback_succ_func, callback_error_func) {
  // 使用原生的，避免初始化appid问题
  WeixinJSBridge.invoke("getBrandWCPayRequest", {
    appId: data["appId"],
    timeStamp: data["timeStamp"],
    nonceStr: data["nonceStr"],
    package: data["package"],
    signType: data["signType"],
    paySign: data["paySign"],
  },
  function(res) {
    var msg = res.err_msg ? res.err_msg : res.errMsg;
    switch (msg) {
    case "get_brand_wcpay_request:ok": // 支付成功时
      if (callback_succ_func) {
        callback_succ_func(res);
      }
      break;
    default: // 支付失败时
      WeixinJSBridge.log("支付失败!" + msg + ",请返回重试.");
      if (callback_error_func) {
        callback_error_func({
          msg: msg
        });
      }
      break;
    }
  });
}

// WeixinJSBridge判断
export const wxWepPay = (data, callback_succ_func, callback_error_func) => {
  if (typeof WeixinJSBridge == "undefined") {
    if (document.addEventListener) {
      document.addEventListener("WeixinJSBridgeReady", jsApiCall, false);
    } else if (document.attachEvent) {
      document.attachEvent("WeixinJSBridgeReady", jsApiCall);
      document.attachEvent("onWeixinJSBridgeReady", this.jsApiCall);
    }
  } else {
    jsApiCall(data, callback_succ_func, callback_error_func);
  }
};


// 微信支付
export const weixinPay = params => {
  uni.requestPayment({
    provider: "wxpay",
    timeStamp: params.timeStamp,
    nonceStr: params.nonceStr,
    package: params.package,
    signType: "MD5",
    paySign: params.paySign,
    success: res => {
      uni.navigateTo({
        url: "/pages/mine_order/index"
      });
    },
    fail: function(err) {
      console.log(err);
    },
    complete: function(complete) {
      console.log("complete:" + JSON.stringify(complete));
    }
  });
};

// uniapp 获取当前页面路由
export const getRoute = () => {
  var pages = getCurrentPages();
  return  pages[pages.length - 1].route;
};
// uniapp 获取当前页面路由参数
export const getRouteOptions = () => {
  var pages = getCurrentPages();
  return  pages[pages.length - 1].options;
};
// 保存图片到本地
export const downloadImgTolocal = (imgUrl) => {
  console.log("相册授权");
  uni.authorize({
    scope: "scope.writePhotosAlbum",
    success: () => {
      console.log("相册已授权");
      uni.showLoading({
        mask: true,
        title: "二维码生成中..."
      });
		
      uni.downloadFile({
        url: imgUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            uni.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: function (path) {
                uni.showToast({
                  title: "已保存至系统相册",
                  duration: 500,
                  icon: "none",
                });
              },
              fail: function (err) {
                console.log(err, "errerrerr");
              },
            });
          }
        },
        complete: () => {
          uni.hideLoading();
        }
      });
    },
    fail: () => {
      uni.showToast({
        title: "相册授权调用失败，请手动开启相册权限，或截图保存",
        icon: "none",
      });
    }
  });
};
