<template>
  <theme-layout-card :theme="theme">
    <p class="title">
      请查看您的法临咨询意见书
    </p>
    <div class="submissions-card flex">
      <img
        class="logo"
        src="../../../imgs/submissions-card.png"
        alt=""
      >
      <div class="flex-1 flex flex-column flex-space-around">
        <p class="card-title">
          法临咨询意见书
        </p>
        <p
          class="look flex flex-align-center"
          @click="toSubmissionsDetail"
        >
          点击查看 <i class="iconfont icon-erjiyoujiantou" />
        </p>
      </div>
    </div>
  </theme-layout-card>
</template>

<script>
import ThemeLayoutCard from "@/pages/sub/im/components/Cards/ThemeLayoutCard/index.vue";
import cardProps, { customComputed } from "@/pages/sub/im/mixins/card-props.js";
import { turnPages } from "@/libs/turnPages.js";
import { caseInfoStateProps } from "@/pages/sub/im/mixins/case-info-state.js";

export default {
  name: "SubmissionsCard",
  components: { ThemeLayoutCard },
  mixins: [cardProps, customComputed, caseInfoStateProps],
  methods: {
    toSubmissionsDetail(){
      turnPages({
        path: "/pages/sub/im/submissions/index",
        query: {
          id: this.caseSourceServerV2Id
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.title{
  padding-bottom: 8px;
}
.submissions-card{
  background: #FFFFFF;
  padding: 10px;
  border-radius: 8px 8px 8px 8px;
  .logo{
    width: 44px;
    height: 44px;
    padding-right: 12px;
  }
  .card-title{
    font-size: 14px;
    font-weight: 500;
    color: #333333;
  }
  .look{
    font-size: 12px;
    font-weight: 400;
    color: #3887F5;
    .iconfont{
      font-size: 12px;
      padding-left: 4px;
    }
  }
}
</style>
