<template>
  <div
    class="w-[343px] h-[108px] bg-[#FFFFFF] rounded-[8px] mx-auto p-[12px_8px_12px_16px] box-border flex items-center justify-between"
  >
    <div class="space-y-[12px]">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="flex items-center"
      >
        <img
          :src="item.icon"
          alt=""
          class="w-[20px] h-[20px] mr-[8px] block"
        >
        <div class="text-[14px] text-[#333333] w-[110px] mr-[16px]">
          {{ item.title }}
        </div>
        <div class="font-bold text-[16px] text-[#EB4738]">
          {{ item.value }}
        </div>
      </div>
    </div>
    <img
      alt=""
      class="w-[88px] h-[72px] block"
      src="../img/image4832x.png"
    >
  </div>
</template>

<script>
import { getLawyerByIdRank } from "@/api/lawyer";

export default {
  name: "LawyerHomeOtherInfo",
  props: {
    lawyerInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      data: {},
    };
  },
  computed: {
    list() {
      return [
        {
          icon: require("@/pages/sub/lawyer-home/img/2x.png"),
          title: "今日展示排名",
          value: this.data.nowRank === 0 ? "未上榜" : this.data.nowRank,
        },
        {
          icon: require("@/pages/sub/lawyer-home/img/2x1.png"),
          title: "今日网站曝光数",
          value: this.data.exposureCount,
        },
        {
          icon: require("@/pages/sub/lawyer-home/img/2x2.png"),
          title: "今日1v1咨询次数",
          value: this.data.today1V1Count,
        },
      ];
    },
  },
  watch: {
    lawyerInfo: {
      handler(val) {
        if (val.id) this.getLawyerByIdRank();
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    /** 请求数据 */
    getLawyerByIdRank() {
      getLawyerByIdRank({
        type: 8,
        lawyerId: this.lawyerInfo.id,
      }).then((res) => {
        this.data = res.data || {};
      });
    },
  },
};
</script>
