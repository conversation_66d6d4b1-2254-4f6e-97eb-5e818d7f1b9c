const state = () => {
  return {
    /* 登录弹窗显示状态*/
    loginPopupState: false,
    /* 下载页面显示弹窗*/
    downloadPopupState: false,
    /* 加载页面显示*/
    loadingPopupState: false,
    /* 微信绑定弹窗显示状态*/
    wechatBindPopupState: false,
  };
};

const getters = {
  getLoginPopupState: (state) => state.loginPopupState,
  getDownloadPopupState: (state) => state.downloadPopupState,
  getLoadingPopupState: (state) => state.loadingPopupState,
  getWechatBindPopupState: (state) => state.wechatBindPopupState,
};

const mutations = {
  SET_LOGIN_POPUP_STATE(state, status) {
    state.loginPopupState = status;
  },

  SET_DOWNLOAD_POPUP_STATE(state, status) {
    state.downloadPopupState = status;
  },

  SET_LOADING_POPUP_STATE(state, status) {
    state.loadingPopupState = status;
  },

  SET_WECHAT_BIND_POPUP_STATE(state, status) {
    state.wechatBindPopupState = status;
  },
};

const actions = {};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
