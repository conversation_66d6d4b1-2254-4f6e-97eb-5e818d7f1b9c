<template>
  <div class="flex items-center space-x-[4px]">
    <div
      v-for="(item,index) in labels"
      :key="index"
      class="px-[5px] flex items-center justify-center box-border h-[20px] bg-[rgba(235,71,56,0.05)] rounded-[4px] border-[1px] border-solid border-[rgba(235,71,56,0.1)]"
    >
      <div class="text-[12px] text-[#EB4738]">
        {{ item }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CollaborationItemLabels",
  props: {
    labels: {
      type: Array,
      default: () => [],
      required: true
    }
  }
};
</script>
