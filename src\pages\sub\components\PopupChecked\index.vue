<template>
  <app-popup
    :show="getShow"
    :round="16"
    closeable
    mode="bottom"
    @cancel="getShow = false"
  >
    <div>
      <slot />
      <div
        class="px-[16px] grid grid-cols-3 gap-x-[24px] gap-y-[20px] pb-[26px]"
      >
        <p
          v-for="i in list"
          :key="i.value"
          class="w-[96px] text-[13px] text-[#333333] h-[38px] bg-[#F5F5F7] rounded-[20px] flex items-center justify-center"
          :class="[
            data.includes(i.value) ? '!bg-[#EBF3FE] !text-[#3887F5]' : '',
          ]"
          @click="handleSelect(i)"
        >
          {{ i.label }}
        </p>
      </div>
      <div class="px-[16px] pb-[8px]">
        <div
          :class="[
            {
              'opacity-60': isMinSelect,
            },
          ]"
          class="font-bold text-[16px] text-[#FFFFFF] flex items-center justify-center h-[44px] bg-[#3887F5] rounded-[68px]"
          @click="handleSubmit"
        >
          选好了
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";

export default {
  name: "PopupChecked",
  components: { AppPopup },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
    value: {
      type: Array,
      default: () => [],
    },
    minSelect: {
      type: Number,
      default: 3,
    },
  },
  data() {
    return {
      data: [],
    };
  },
  computed: {
    getShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit("update:show", val);
      },
    },
    /* 是否最小选择数量 */
    isMinSelect() {
      return this.data.length < this.minSelect;
    },
  },
  watch: {
    getShow: {
      handler(val) {
        if (!val) return;
        this.data = this.value;
      },
      immediate: true,
    },
  },
  methods: {
    handleSelect(data) {
      const { value } = data;
      if (this.data.includes(value)) {
        this.data = this.data.filter((i) => i !== value);
      } else {
        this.data.push(value);
      }
    },
    handleSubmit() {
      if (this.isMinSelect) {
        uni.showToast({
          title: `至少选择${this.minSelect}个服务类型哦`,
          icon: "none",
        });
        return;
      }
      this.$emit("submit", this.data);
      this.getShow = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
