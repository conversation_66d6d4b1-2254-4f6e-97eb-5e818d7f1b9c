<template>
  <div @click="chooseAvatar">
    <slot :header="value" />
  </div>
</template>

<script>
import { turnToCropImagePage } from "@/libs/turnPages";
import { axiosBaseHeadersConfig } from "@/libs/config";
import { getUserTokenStorage } from "@/libs/token";

export default {
  name: "AppAvatarCropper",
  props: {
    value: {
      type: String,
      default: "",
    },
    /** 输出图片宽度，高等于宽，单位px */
    destWidth: {
      type: Number,
      default: 300,
    },
    /** 裁剪框宽度，高等于宽，单位px */
    rectWidth: {
      type: Number,
      default: 300,
    },
    /** 输出的图片类型，如果'png'类型发现裁剪的图片太大，改成"jpg"即可 */
    fileType: {
      type: String,
      default: "jpg",
    },
  },
  created() {
    // 监听从裁剪页发布的事件，获得裁剪结果
    uni.$on("uAvatarCropper", (path) => {
      this.avatar = path;
      console.log(path);

      const token = getUserTokenStorage();

      const uploadObj = {
        filePath: path,
        name: "file",
      };

      // 可以在此上传到服务端
      uni.uploadFile({
        url: process.env.VUE_APP_ENV_BASE_URL + "/core/upload/image",
        Accept: "multipart/form-data",
        header: {
          "Content-Type": "multipart/form-data",
          token: token,
          osversion: axiosBaseHeadersConfig.osVersion,
        },
        ...uploadObj,
        success: (res) => {
          this.$emit("input", JSON.parse(res.data).data);
        },
        fail: (error) => {
          console.log("error:", error);
        },
      });
    });
  },
  methods: {
    chooseAvatar() {
      turnToCropImagePage({
        destWidth: this.destWidth,
        rectWidth: this.rectWidth,
        fileType: this.fileType,
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
