<template>
  <div class="lawyer-container position-relative">
    <navigator-link url="/pages/rapid-consultation-confirm-order/fast/index">
      <img
        class="lawyer-btn"
        src="../img/Group9912.png"
        alt=""
      >
    </navigator-link>
    <img
      class="lawyer"
      mode="widthFix"
      src="../img/<EMAIL>"
      alt=""
    >
  </div>
</template>

<script>
import NavigatorLink from "@/components/navigator-link/index.vue";

export default {
  name: "LawyerHomeSelfCard",
  components: { NavigatorLink },
  props: {
    lawyerInfo: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  mounted() {
    uni.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#3B3D40"
    });
  }
};
</script>

<style lang="scss" scoped>
.lawyer-container{
  min-height: 100vh;
  background: #3B3D40;
  .lawyer{
    width: 100%;
    object-fit: cover;
  }
  .lawyer-btn{
    position: absolute;
    bottom: 100px;
    width: 100%;
    height: 72px;
    margin: auto;
    z-index: 999;
  }
}
</style>
