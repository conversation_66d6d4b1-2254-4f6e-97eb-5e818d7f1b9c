<template>
  <app-popup
    :safeAreaInsetBottom="false"
    :show="show"
    mode="center"
  >
    <div class="w-[311px] bg-[#FFFFFF] rounded-[16px]">
      <img
        class="block w-[160px] h-[120px] mx-auto mt-[24px]"
        src="@/pages/index/imgs/<EMAIL>"
        alt=""
      >
      <p class="w-[263px] mx-auto text-align-center text-[15px] text-[#323233] pt-[12px] pb-[24px]">
        今日您主动联系律师次数已用完，如有异地协作需求，<span class="text-[#3887F5]">可前往接单广场</span>发布公开需求哦
      </p>
      <div class="px-[24px] pb-[16px]">
        <div
          class="h-[36px] bg-[#3887F5] rounded-[68px] font-bold text-[14px] text-[#FFFFFF] flex items-center justify-center"
          @click="show=false"
        >
          知道了
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";

export default {
  name: "ContactALawyerReachesTheUpperLimit",
  components: { AppPopup },
  props: {
    value: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  computed: {
    show: {
      get(){
        return this.value;
      },
      set(val){
        this.$emit("input", val);
      }
    }
  },
};
</script>

<style scoped lang="scss">

</style>