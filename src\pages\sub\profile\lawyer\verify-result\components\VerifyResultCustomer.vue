<template>
  <div class="customer">
    <img
      :src="info.customServiceQrCode"
      alt=""
      class="customer-qr"
      showMenuByLongpress
    >
    <div>
      <div class="flex flex-align-center flex-space-between">
        <div class="customer-title">
          修改执业信息
        </div>
        <div
          class="customer-call"
          @click="turnToServiceCenterPage"
        >
          联系客服
        </div>
      </div>
      <div class="customer-tips">
        如需修改执业认证信息, 可联系平台客服
      </div>
    </div>
  </div>
</template>
<script>
import { consultingAssistant } from "@/api";
import { turnToServiceCenterPage } from "@/libs/turnPages";

export default {
  name: "VerifyResultCustomer",
  data() {
    return {
      info: {},
    };
  },
  created() {
    this.getInfo();
  },
  methods: {
    turnToServiceCenterPage,
    /** 获取信息 */
    getInfo() {
      consultingAssistant().then((res) => {
        this.info = res.data;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.customer {
  display: flex;
  align-items: center;
  width: 343px;
  height: 104px;
  box-sizing: border-box;
  margin: 12px auto 0 auto;
  background: #ebf3fe;
  border-radius: 8px;
  opacity: 1;
  border: 1px solid #a0cffb;
  padding: 12px;

  &-qr {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 4px;
    opacity: 1;
    box-sizing: border-box;
    margin-right: 8px;
  }

  &-title {
    font-size: 16px;
    font-weight: bold;
    color: #000000;
  }

  &-call {
    font-size: 12px;
    font-weight: 400;
    color: #3887f5;
  }

  &-tips {
    margin-top: 18px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
  }
}
</style>
