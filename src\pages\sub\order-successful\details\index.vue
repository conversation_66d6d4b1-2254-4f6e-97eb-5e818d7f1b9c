<template>
  <div class="position-relative">
    <div class="position-relative w-[375px] h-[104px]">
      <img
        alt=""
        class="background-image"
        src="@/pages/sub/order-successful/details/img/<EMAIL>"
      >
      <div
        v-if="detailInfo.redPacketTotalNum === 0"
        class="pt-[36px] pl-[19px]"
      >
        <img
          alt=""
          class="block w-[240px] h-[38px]"
          src="@/pages/sub/order-successful/details/img/Frame1321314771.png"
        >
      </div>
      <div
        v-if="canOpen"
        class="pt-[10px] pl-[19px] font-bold text-[20px] text-[#E7455B] [text-shadow:0px_2px_2px_#FDBBA2] flex items-center justify-between"
      >
        <div>
          <div>点击红包沾喜气</div>
          <div class="flex items-center mt-[4px]">
            <div class="mr-[4px]">
              瓜分{{ detailInfo.redPacketTotalNum }}个现金红包
            </div>
            <img
              alt=""
              class="block w-[40px] h-[19px] scale"
              src="@/pages/sub/order-successful/details/img/<EMAIL>"
            >
          </div>
        </div>
        <img
          alt=""
          class="block w-[130px] h-[84px]"
          src="@/pages/sub/order-successful/details/img/sdfesc.png"
          @click="redPopup = true"
        >
      </div>
      <div
        v-if="alreadyReceived"
        class="pt-[10px] pl-[19px] font-bold text-[20px] text-[#E7455B] [text-shadow:0px_2px_2px_#FDBBA2] flex items-center justify-between"
      >
        <div>
          <div>恭喜您！</div>
          <div class="mt-[4px]">
            已成功抢到红包哦
          </div>
        </div>
        <img
          alt=""
          class="block w-[130px] h-[84px]"
          src="@/pages/sub/order-successful/details/img/Frame8701@2x(1).png"
          @click="redPopup = true"
        >
      </div>
      <div
        v-if="hasReceived"
        class="pt-[10px] pl-[19px] font-bold text-[20px] text-[#E7455B] [text-shadow:0px_2px_2px_#FDBBA2] flex items-center justify-between"
      >
        <div>
          <div>来晚啦！</div>
          <div class="mt-[4px]">
            {{ detailInfo.redPacketTotalNum }}个现金红包已瓜分完
          </div>
        </div>
        <img
          alt=""
          class="block w-[130px] h-[84px]"
          src="@/pages/sub/order-successful/details/img/<EMAIL>"
        >
      </div>
    </div>
    <div class="absolute top-[82px] left-[12px]">
      <details-swiper />
    </div>
    <div
      class="bg-[#FFFFFF] rounded-tl-[16px] rounded-br-none rounded-tr-[16px] rounded-bl-none px-[16px] pt-[20px] pb-[16px]"
    >
      <img
        alt=""
        class="block w-[250px] h-[23px]"
        src="@/pages/sub/order-successful/details/img/Frame1321314799.png"
      >
      <div class="font-bold text-[18px] text-[#333333] mt-[16px]">
        {{ detailInfo.title }}
      </div>
      <div
        class="flex items-center mt-[16px] pb-[16px] border-0 border-dotted border-b border-[#EEEEEE]"
      >
        <div class="text-[12px] text-[#999999] mr-[8px]">
          {{ detailInfo.adoptTime }}
        </div>
        <div class="text-[12px] text-[#3887F5] mr-[8px]">
          #{{ detailInfo.typeLabel }}
        </div>
        <div class="text-[12px] text-[#999999] mr-[2px]">
          {{ detailInfo.totalPv }}
        </div>
        <div class="text-[12px] text-[#999999]">
          浏览
        </div>
      </div>
      <div class="mt-[12px] mb-[10px]">
        <order-details-title title="成案律师" />
      </div>
      <div
        class="w-[343px] h-[65px] bg-[#F5F5F7] rounded-[8px] flex items-center justify-between px-[12px] box-border"
      >
        <div class="flex items-center">
          <img
            :src="lawyerInfo.lawyerAvatar"
            alt=""
            class="block w-[40px] h-[40px] shrink-0 rounded-[8px]"
          >
          <div class="ml-[8px]">
            <div class="flex items-center">
              <div class="font-bold text-[16px] text-[#333333]">
                {{ lawyerInfo.lawyerName }}律师
              </div>
              <div class="text-[12px] text-[#999999] ml-[8px]">
                {{ lawyerInfo.workCityName }}
              </div>
            </div>
            <div class="flex items-center">
              <div
                class="px-[4px] box-border h-[17px] bg-[#FFF8EC] rounded-[3px] border-[1px] border-solid border-[rgba(189,147,76,0.3)] mt-[6px] text-[11px] text-[#BD934C] flex items-center"
              >
                擅长{{ workFieldName }}
              </div>
              <div
                class="ml-[4px] px-[4px] box-border h-[17px] bg-[#FFF8EC] rounded-[3px] border-[1px] border-solid border-[rgba(189,147,76,0.3)] mt-[6px] text-[11px] text-[#BD934C] flex items-center"
              >
                官方认证
              </div>
            </div>
          </div>
        </div>
        <div class="text-[12px] text-[#3887F5] text-center">
          <div>案源消耗数</div>
          <div class="mt-[4px]">
            <span class="text-[16px]">{{ detailInfo.serverCaseNumCount }}</span>次
          </div>
        </div>
      </div>
      <div class="mt-[20px]">
        <order-details-title title="案件信息" />
      </div>
      <div class="mt-[12px] position-relative">
        <img
          alt=""
          class="block w-[88px] h-[88px] absolute top-[10px] right-[72px]"
          src="@/pages/sub/order-successful/details/img/4v3mxm.png"
        >
        <details-info
          :dataSource="infoData"
          :props="infoProps"
        />
      </div>
      <div class="mt-[32px]">
        <order-details-title title="案件回顾" />
      </div>
      <div class="text-[14px] text-[#333333] mt-[16px]">
        {{ detailInfo.caseReview }}
      </div>
      <div class="mt-[40px]">
        <order-details-title title="案件详情" />
      </div>
      <div class="text-[14px] text-[#333333] mt-[16px]">
        {{ detailInfo.caseDetail }}
      </div>
      <div
        class="w-[343px] box-border bg-[#F4F9FF] rounded-[4px] px-[16px] py-[8px] flex items-center mt-[12px] position-relative"
      >
        <div class="font-bold text-[14px] text-[#3887F5] mr-[13px]">
          案件说明
        </div>
        <div class="text-[13px] text-[#3887F5]">
          本案例已通过平台回访核实
        </div>
        <img
          alt=""
          class="block w-[34px] h-[32px] absolute top-[4px] right-[10px]"
          src="@/pages/sub/order-successful/details/img/Frame613.png"
        >
      </div>
      <div
        class="w-[343px] box-border p-[8px] bg-[#F5F5F7] rounded-[4px] mt-[28px]"
      >
        <div class="text-[12px] text-[#999999]">
          本文版权归原作者所有，内容仅代表作者本人观点，不代表法临平台的立场。如有任何疑问或需要删除请通过<span
            class="text-[#3887F5]"
            @click="turnToServiceCenterPage"
          >【客服中心】</span>联系我们。
        </div>
      </div>
    </div>
    <div class="font-bold text-[18px] text-[#333333] p-[16px]">
      精选案例
    </div>
    <div class="px-[12px] space-y-[12px]">
      <div
        v-for="item in caseList"
        :key="item.id"
      >
        <order-details-card :data="item" />
      </div>
    </div>
    <red-envelopes-popup
      v-model="redPopup"
      :data="detailInfo"
      :lawyerInfo="lawyerInfo"
      @getRedPacket="getRedPacket"
    />
    <u-safe-bottom />
  </div>
</template>

<script>
import OrderDetailsTitle from "@/pages/sub/order-successful/details/components/OrderDetailsTitle.vue";
import RedEnvelopesPopup from "@/pages/sub/order-successful/details/components/RedEnvelopesPopup.vue";
import DetailsSwiper from "@/pages/sub/order-successful/details/components/DetailsSwiper.vue";
import OrderDetailsCard from "@/pages/sub/order-successful/components/OrderDetailsCard.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { turnToServiceCenterPage } from "@/libs/turnPages";
import DetailsInfo from "@/pages/sub/order-successful/details/components/DetailsInfo.vue";
import { goodNewsLawyerUploadDetail, goodNewsPage, updateGoodNewsDetailPv } from "@/api";
import { getInfoById } from "@/api/lawyer";
import { shareAppMessage } from "@/libs/tools";

export default {
  name: "OrderSuccessfulDetails",
  components: {
    DetailsInfo,
    USafeBottom,
    OrderDetailsCard,
    DetailsSwiper,
    RedEnvelopesPopup,
    OrderDetailsTitle,
  },
  data() {
    return {
      infoProps: [
        {
          label: "成案线索类型",
          value: "type",
        },
        {
          label: "案件类型",
          value: "typeLabel",
        },
        {
          label: "成案地点",
          value: "regionName",
        },
        {
          label: "成案周期",
          value: "caseSuccessDay",
          suffix: "天",
        },
        {
          label: "涉案金额",
          value: "amount",
          suffix: "元",
        },
        {
          label: "成案委托金额",
          value: "caseEntrustAmount",
          suffix: "元",
        },
      ],
      caseList: [],
      detailInfo: {},
      lawyerInfo: {},
      getRedRes: {},
      redPopup: false,
    };
  },
  onShareAppMessage() {
    if (this.alreadyReceived)
      return shareAppMessage({
        title: `恭喜您，沾沾${this.lawyerInfo.lawyerName}律师的成单喜气已成功抢到红包哦`,
        imageUrl: require("@/pages/sub/order-successful/details/img/Group87157.png"),
      });

    if (this.isOver)
      return shareAppMessage({
        title: `很遗憾，沾沾${this.lawyerInfo.lawyerName}律师的成单喜气，未成功抢到红包哦`,
        imageUrl: require("@/pages/sub/order-successful/details/img/Group8721.png"),
      });

    return shareAppMessage({
      title: `热烈祝贺${this.lawyerInfo.lawyerName}律师在${this.detailInfo.regionName}成案，标的额¥${this.infoData.amount}元`,
      imageUrl: require("@/pages/sub/order-successful/details/img/Group8715.png"),
    });
  },
  onLoad(params) {
    const { id, lawyerId } = params;

    this.getList(id);
    this.getLawyerInfo(lawyerId);
    this.getDetailInfo(params);
    this.updateUV(params);
  },
  computed: {
    /** 点击后是否红包已经领取完 */
    isOver() {
      return this.getRedRes.status === 2;
    },
    /** 是否可以打开了红包 */
    canOpen() {
      if (this.detailInfo.redPacketTotalNum === 0) return false;

      if (this.hasReceived) return false;

      if (this.getRedRes.status) return false;

      if (this.detailInfo.alreadyReceived) return false;

      return true;
    },
    /** 是否已经领取过红包 */
    alreadyReceived() {
      return this.getRedRes.status === 1 || this.detailInfo.alreadyReceived;
    },
    /** 红包是否已经被领取完毕 */
    hasReceived() {
      // 如果已经领取
      if (this.alreadyReceived) return false;

      if (this.isOver) return true;

      return (
        this.detailInfo.redPacketTotalNum &&
        this.detailInfo.redPacketTotalNum - this.detailInfo.redPacketGetNum ===
          0
      );
    },
    workFieldName() {
      return this.lawyerInfo.lawyerWorkFieldList?.[0]?.workFieldName;
    },
    infoData() {
      return {
        ...this.detailInfo,
        caseEntrustAmount: this.detailInfo.caseEntrustAmount / 100 || 0,
      };
    },
  },
  methods: {
    turnToServiceCenterPage,
    getRedPacket(data) {
      this.getDetailInfo(this.detailInfo);
      this.getRedRes = data;
    },
    getList(id) {
      goodNewsPage({
        currentPage: 1,
        pageSize: 3,
        id,
      }).then(({ data = {} }) => {
        this.caseList = data.records;
      });
    },
    getDetailInfo({ id, lawyerId }) {
      goodNewsLawyerUploadDetail({
        id,
        lawyerId,
      }).then((res) => {
        this.detailInfo = res.data;
      });
    },
    getLawyerInfo(lawyerId) {
      getInfoById({
        lawyerId,
      }).then((res) => {
        this.lawyerInfo = res.data;
      });
    },
    updateUV({ id, lawyerId }) {
      updateGoodNewsDetailPv({
        id,
        lawyerId,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@keyframes scale-animation {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.8);
  }
  100% {
    transform: scale(1);
  }
}

.scale {
  animation: scale-animation 1s linear infinite;
}
</style>
