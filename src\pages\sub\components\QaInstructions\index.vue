<template>
  <div>
    <app-popup
      :show="show"
    >
      <div class="pt-[24px]  px-[16px]">
        <div class="h-[490px] overflow-y-auto">
          <img
            alt=""
            class="block w-full"
            mode="widthFix"
            src="@/pages/sub/imgs/<EMAIL>"
          >
          <div class="mt-[24px]">
            <img
              alt=""
              class="w-[96px] h-[32px] block"
              src="@/pages/sub/imgs/<EMAIL>"
            >
            <div class="p-[16px] bg-[#EBF3FE] rounded-tl-none rounded-br-none rounded-tr-[12px] rounded-bl-none text-[14px] text-[#333333]">
              <p>单个律师每天上限：直接回复{{ replyLimit }}个</p>
              <p>单个问答回复上限：可被{{ qaLimit }}名律师回复</p>
              <div class="py-[16px] overflow-hidden">
                问答服务过程中, 请注意言语规范，不可向当事人发送或索要个人联系方式，详见
                <span
                  class="text-[#3887F5]"
                  @click.stop="lookProtocol"
                >
                  《法临网律师服务公约》
                </span>
              </div>
              <p>服务过程中, 您可推荐有意向的当事人购买电话咨询(发送付费卡片) 以此获取当事人联系方式</p>
            </div>
          </div>
        </div>
        <div class="py-[8px]">
          <div
            class="font-bold text-align-center flex items-center justify-center text-[16px] text-[#FFFFFF] h-[44px] bg-[#3887F5] rounded-[68px]"
            @click="handleClick"
          >
            我知道了
          </div>
        </div>
      </div>
    </app-popup>
    <protocol-pop-up
      v-model="protocolModal"
      :protocolContent="protocolA"
      protocolTitle="法临网律师服务公约"
    />
  </div>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { getCommonConfigKey } from "@/api";
import ProtocolPopUp from "@/components/protocolPopUp/index.vue";
import { LAWYER_SERVICE_AGREEMENT } from "@/protocol/qa";

export default {
  name: "QaInstructions",
  components: { ProtocolPopUp, AppPopup },
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      /* 律师回复上限 */
      replyLimit: 100,
      /* 问答回复上限 */
      qaLimit: 10,
      protocolModal: false,
      protocolA: LAWYER_SERVICE_AGREEMENT
    };
  },
  mounted() {
    getCommonConfigKey({
      paramName: "qa_message_rush_answer_max_count",
    }).then(({ data = { paramValue: 10 } }) => {
      this.qaLimit = data.paramValue;
    });
    getCommonConfigKey({
      paramName: "qa_message_reply_max_count",
    }).then(({ data = { paramValue: 100 } }) => {
      this.replyLimit = data.paramValue;
    });
  },
  methods: {
    handleClick(){
      this.$emit("update:show", false);
    },
    lookProtocol(){
      this.protocolModal = true;
    }
  }
};
</script>

<style lang="scss" scoped>
</style>
