<template>
  <div class="min-h-[100vh] bg-[#FFFFFF]">
    <div class="bg-[#3A4965] pb-[32px] pt-[16px] px-[16px]">
      <div class="pb-[16px] flex">
        <p class="px-[4px] py-[2px] text-[11px] text-[#FFFFFF] bg-[linear-gradient(_133deg,_#769CFF_0%,_#4377FF_100%)] rounded-[4px]">
          用户问题
        </p>
      </div>
      <div class="break-all text-[16px] text-[#FFFFFF]">
        {{ details.detail }}
      </div>
    </div>
    <div class="bg-[#FFFFFF] rounded-[16px] mt-[-16px] pt-[24px] px-[16px] overflow-hidden">
      <div class="pb-[16px] flex">
        <p class="px-[4px] py-[2px] text-[11px] text-[#FFFFFF] bg-[linear-gradient(_90deg,_#79D9A6_0%,_#57C398_100%)] rounded-[4px]">
          您的解答
        </p>
      </div>
      <textarea
        v-model="info"
        :placeholder="placeholder"
        :placeholder-style="placeholderStyle"
        class="[word-wrap:break-word] whitespace-pre-line w-full"
        maxlength="300"
      />
    </div>
    <div class="fixed bottom-0 left-0 right-0 px-[24px]">
      <div
        :class="[{'opacity-[0.6]':isEmpty}]"
        class=" bg-[#3887F5] rounded-[68px] text-align-center leading-[44px] font-bold text-[16px] text-[#FFFFFF]"
        @click.stop="handleSubmit"
      >
        提交
      </div>
      <u-safe-bottom />
    </div>
  </div>
</template>

<script>
import { pxToRpx } from "@/libs/tools";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { isNull } from "@/libs/basics-tools";
import { qaMessageDetail, qaMessageRushAnswer } from "@/api/qa";
import { turnToLawyerAuthResultPageToLogin } from "@/libs/turnPages";

export default {
  name: "Index",
  components: { USafeBottom },
  data() {
    return {
      placeholderStyle: `font-size: ${pxToRpx(14)};color: #CCCCCC; `,
      placeholder: "回复后当事人可主动向您进行追问\n注：问答仅可回复1次，单次回复不得低于15个字",
      info: "",
      details: {}
    };
  },
  onLoad({ id = "" }){
    if(isNull(id)) return;
    qaMessageDetail({
      qaMessageId: id,
      detailRequestType: 1
    }).then(({ data = {} }) => {
      this.details = data;
    });
  },

  computed: {
    /* info是不是空的 */
    isEmpty() {
      return isNull(this.info.trim());
    }
  },
  methods: {
    handleSubmit(){
      if(this.isEmpty) return;
      const info = this.info.trim();
      if(info.length < 15 || info.length > 300){
        return uni.showToast({
          title: "回复内容字数限制15-300字",
          duration: 2000,
          icon: "none",
        });
      }
      turnToLawyerAuthResultPageToLogin(() => {
        qaMessageRushAnswer({
          qaMessageId: this.details.id,
          content: this.info
        }).then(() => {
          uni.showToast({
            title: "提交成功",
            duration: 2000,
            icon: "none",
          });
          uni.navigateBack();
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
