import { mapActions, mapState } from "vuex";
import { checkAppLoginAndGetCode } from "@/libs/token";
import { commonLoginRequest } from "@/api/lawLogin";
import { messageSendSms, userLogin } from "@/api/user";

export default {
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo,
      token: (state) => state.user.token,
    }),
  },
  methods: {
    ...mapActions({
      setToken: "user/setToken",
      setUserIfo: "user/setUserInfo",
      setOpenid: "user/setOpenid",
      userLoginIM: "im/userLogin",
    }),
    handleCodeClick(data) {
      return messageSendSms(data);
    },
    handleClick(data) {
      if(this.isTest) {
        this.handleCodeClick(data).then(() => {
          // 按钮点击事件
          userLogin({
            ...data,
          }).then((res) => {
            this.setData(res.data.token);
          });
        });

        return;
      }

      // 按钮点击事件
      userLogin({
        ...data,
      }).then((res) => {
        this.setData(res.data.token);
      });
    },
    /** 手机号授权登录 */
    getPhoneNumberHandler(e) {
      if (e.detail.encryptedData) {
        this.check = true;
        this.login(e.detail);
      } else {
        uni.showToast({
          title: "您拒绝了授权手机号快捷登录",
          icon: "none",
        });
      }
    },
    async login(eniv) {
      uni.showLoading({
        title: "登录中",
      });
      const params = {
        code: uni.getStorageSync("loginCode"),
        encryptedData: eniv.encryptedData,
        iv: eniv.iv,
      };

      const clickId = uni.getStorageSync("clickId");

      if (clickId) params.clickId = clickId;
      const lawyerShareCode = uni.getStorageSync("lawyerShareCode");
      if (lawyerShareCode) params.lawyerShareCode = lawyerShareCode;
      try {
        const result = await commonLoginRequest(params);
        if (result.code === 0) {
          // 登录成功的回调
          this.setData(result.data.token);
          this.setOpenid(result.data.openid);
          uni.showToast({
            title: "登录成功",
            icon: "none",
          });
        }
        uni.hideLoading();
      } catch (error) {
        checkAppLoginAndGetCode();
      }
    },
    setData(token) {
      this.setToken(token);
      // ! 获取用户信息，获取成功后，执行登录成功的回调
      this.setUserIfo().then(() => {
        this.loginSuccess();
        this.getUserChannelId();
        // ! 连接im
        this.userLoginIM();
      });
    },
    getUserChannelId() {
      // getDyXcxChannel().then(({ data = {} }) => {
      //   if (data && data.channelId) {
      //     uni.setStorageSync("shareChannelId", data.channelId);
      //   }
      // });
    },
  },
};
