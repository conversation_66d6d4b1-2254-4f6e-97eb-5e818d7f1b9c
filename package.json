{"name": "cl-uni-app", "version": "0.1.0", "private": true, "scripts": {"upload": "node miniProgramsUploadStart.js", "serve": "npm run dev:h5", "build": "npm run build:h5", "tailwindcss": "tailwindcss -i ./src/styles/tailwind.css -o ./src/styles/tailwindout.css --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "dev:mp-weixin-test": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin UNI_OUTPUT_DIR=dist/dev/mp-weixin-test vue-cli-service uni-build --mode test  --watch --minimize", "build-mp": "npm run build:mp-baidu && npm run build:mp-weixin && npm run build:mp-to<PERSON><PERSON>", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-360": "cross-env NODE_ENV=production UNI_PLATFORM=mp-360 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-kuaishou": "cross-env NODE_ENV=production UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build:quickapp-native": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-native vue-cli-service uni-build", "build:quickapp-webview": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview vue-cli-service uni-build", "build:quickapp-webview-huawei": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build", "build:quickapp-webview-union": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-360": "cross-env NODE_ENV=development UNI_PLATFORM=mp-360 vue-cli-service uni-build --watch", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-kuaishou": "cross-env NODE_ENV=development UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-toutiao-test": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao UNI_OUTPUT_DIR=dist/dev/mp-toutiao vue-cli-service uni-build --mode test --watch", "dev:quickapp-native": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-native vue-cli-service uni-build --watch", "dev:quickapp-webview": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview vue-cli-service uni-build --watch", "dev:quickapp-webview-huawei": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build --watch", "dev:quickapp-webview-union": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve:quickapp-native": "node node_modules/@dcloudio/uni-quickapp-native/bin/serve.js", "test:android": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=android jest -i", "test:h5": "cross-env UNI_PLATFORM=h5 jest -i", "build-test:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu  vue-cli-service uni-build --mode test", "build-test:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao  vue-cli-service uni-build --mode test", "test:ios": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=ios jest -i", "test:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin jest -i", "upload:mp-weixin-test": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin UNI_OUTPUT_DIR=dist/dev/mp-weixin-test vue-cli-service uni-build --mode test --minimize && node miniProgramsUpload UNI_PLATFORM=mp-weixin FILE_NAME=mp-weixin-test UPLOAD_ENV=dev", "upload:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build && node miniProgramsUpload UNI_PLATFORM=mp-weixin FILE_NAME=mp-weixin UPLOAD_ENV=build"}, "dependencies": {"@dcloudio/uni-app": "^2.0.2-3080420230530001", "@dcloudio/uni-app-plus": "^2.0.2-3080420230530001", "@dcloudio/uni-h5": "^2.0.2-3080420230530001", "@dcloudio/uni-i18n": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-360": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-alipay": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-baidu": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-jd": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-kuaishou": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-lark": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-qq": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-toutiao": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-vue": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-weixin": "^2.0.2-3080420230530001", "@dcloudio/uni-mp-xhs": "^2.0.2-3080420230530001", "@dcloudio/uni-quickapp-native": "^2.0.2-3080420230530001", "@dcloudio/uni-quickapp-webview": "^2.0.2-3080420230530001", "@dcloudio/uni-stacktracey": "^2.0.2-3080420230530001", "@dcloudio/uni-stat": "^2.0.2-3080420230530001", "@vue/shared": "^3.0.0", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "dayjs": "^1.11.8", "flyio": "^0.6.2", "lodash-es": "^4.17.21", "miniprogram-ci": "^1.9.15", "uqrcodejs": "^4.0.7", "uuid": "^9.0.0", "vue": "^2.6.11", "vuex": "^3.2.0", "yoc-im-web": "^0.1.143"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "^2.0.2-3080420230530001", "@dcloudio/uni-cli-i18n": "^2.0.2-3080420230530001", "@dcloudio/uni-cli-shared": "^2.0.2-3080420230530001", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-migration": "^2.0.2-3080420230530001", "@dcloudio/uni-template-compiler": "^2.0.2-3080420230530001", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.2-3080420230530001", "@dcloudio/vue-cli-plugin-uni": "^2.0.2-3080420230530001", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.2-3080420230530001", "@dcloudio/webpack-uni-mp-loader": "^2.0.2-3080420230530001", "@dcloudio/webpack-uni-pages-loader": "^2.0.2-3080420230530001", "@types/lodash-es": "^4.17.12", "@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-service": "~4.5.15", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.2", "eslint": "^8.43.0", "eslint-loader": "^4.0.2", "eslint-plugin-vue": "^9.15.1", "jest": "^25.4.0", "mini-types": "*", "miniprogram-api-typings": "*", "postcss-comment": "^2.0.0", "postcss-pxtorpx": "^1.0.0", "sass": "^1.63.4", "tailwindcss": "^3.4.1", "unocss": "^66.1.3", "unocss-preset-weapp": "^0.58.9", "unocss-webpack-uniapp2": "^0.2.2", "vue-inset-loader": "^1.2.6", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4", "ios >= 8"], "uni-app": {"scripts": {}}, "volta": {"node": "22.14.0"}}