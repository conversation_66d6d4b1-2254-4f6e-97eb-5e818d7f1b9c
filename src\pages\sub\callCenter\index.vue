<template>
  <div class="position-relative">
    <img
      alt=""
      class="block w-[375px] h-[724px]"
      src="@/pages/sub/callCenter/img/Frame8727.png"
    >
    <div
      class="absolute top-[411px] left-[20px] w-[335px] h-[211px] bg-[#FFFFFF] rounded-[16px] px-[16px] box-border"
    >
      <div
        class="w-[208px] h-[38px] bg-[#FFF3E0] rounded-tl-none rounded-br-[19px] rounded-tr-none rounded-bl-[19px] flex items-center justify-center box-border mx-auto"
      >
        <div class="font-bold text-[16px] text-[#FF8A36]">
          添加客服微信享专属服务
        </div>
      </div>
      <div class="flex items-center mt-[24px]">
        <img
          :src="customerCode"
          alt=""
          class="w-[124px] h-[124px] block"
          showMenuByLongpress
        >
        <div class="ml-[21px]">
          <div class="font-bold text-[16px] text-[#000000]">
            专属客服
          </div>
          <div class="text-[12px] text-[#666666] mt-[4px]">
            一对一解答各种问题
          </div>
          <div class="text-[12px] text-[#666666] mt-[10px]">
            <div>工作时间：</div>
            <div>工作日{{ serviceTime }}</div>
          </div>
          <div
            class="w-[88px] h-[32px] bg-[linear-gradient(_116deg,_#71B5FF_0%,_#2676E4_100%)] rounded-[68px] flex items-center justify-center mt-[13px]"
            @click="turnPages({ path: '/pages/sub/h5-touch/index' })"
          >
            <div class="text-[14px] text-[#FFFFFF]">
              添加客服
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { turnPages } from "@/libs/turnPages.js";
import { getCommonConfigKey } from "@/api";

export default {
  name: "CallCenter",
  data() {
    return {
      /* 服务时间*/
      serviceTime: "",
      customerCode: ""
    };
  },
  mounted() {
    this.getCustomerCode();

    getCommonConfigKey({
      paramName: "CUSTOM_USER_SERVICE_INFO",
    }).then(({ data }) => {
      try {
        this.serviceTime = JSON.parse(data.paramValue).serviceTime;
      } catch (e) {
        console.log(e);
      }
    });
  },
  methods: {
    /** 获取客服二维码 */
    getCustomerCode() {
      let paramName = "cl_customer_qr_code";

      getCommonConfigKey({ paramName }).then(({ data }) => {
        this.customerCode = data.paramValue;
      });
    },
    turnPages,
  },
};
</script>
