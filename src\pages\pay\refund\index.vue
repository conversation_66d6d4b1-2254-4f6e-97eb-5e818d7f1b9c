<template>
  <div>
    <pay-refund-header />
    <div class="refund-content">
      <div>
        <div class="refund-content-title flex">
          <span class="text-eb4738 mg-r-4">*</span>退单原因
        </div>
        <ul class="reason">
          <li
            v-for="item in reasonList"
            :key="item.value"
            :class="[
              {
                'reason__item--selected': item.value === params.feedbackValue,
              },
            ]"
            class="reason__item"
            @click="selectReason(item)"
          >
            {{ item.label }}
          </li>
        </ul>
      </div>

      <div v-if="isNeedActualRegionCode">
        <div class="refund-content-title mg-tp-24 flex">
          <span class="text-eb4738 mg-r-4">* </span>该案源实际城市
        </div>
        <app-area
          @confirm="areaChange"
        >
          <div class="city">
            <span
              :class="[{
                'city--selected': params.actualRegionCode,
              }]"
            >{{ cityName || "请选择" }}</span>
            <i class="iconfont icon-erjiyoujiantou" />
          </div>
        </app-area>
      </div>
      <div>
        <div class="refund-content-title mg-tp-24 flex">
          <span class="text-eb4738 mg-r-4">* </span>补充说明和截图
        </div>
        <div class="content">
          <div class="textarea">
            <textarea
              v-model="params.feedbackReason"
              maxlength="100"
              placeholder="请输入内容"
              placeholder-style="color: #CCC;font-size: 14px;"
            />
            <p class="number font15">
              <span>{{ params.feedbackReason.length }}</span>/100
            </p>
          </div>
          <app-upload v-model="params.attachFile" />
        </div>
      </div>
      <div class="mg-tp-24">
        <pay-refund-introduce showRefundInfo />
      </div>
      <app-bottom>
        <div
          class="submit"
          @click="submit"
        >
          提交申请
        </div>
      </app-bottom>
    </div>
  </div>
</template>

<script>
import PayRefundHeader from "@/pages/pay/refund/components/PayRefundHeader.vue";
import PayRefundIntroduce from "@/pages/pay/refund/components/PayRefundIntroduce.vue";
import AppUpload from "@/components/AppComponents/AppUpload/index.vue";
import { caseSourceServerV2Feedback } from "@/api/pay";
import { dataDetailList } from "@/api";
import { turnToRefundDetailPage } from "@/libs/turnPages";
import AppArea from "@/components/AppComponents/AppArea/index.vue";
import { appValidator } from "@/libs/validator";
import AppBottom from "@/components/AppComponents/AppBottom/index.vue";

export default {
  name: "PayRefund",
  components: { AppBottom, AppArea, AppUpload, PayRefundIntroduce, PayRefundHeader },
  data() {
    return {
      /** 参数 */
      params: {
        caseSourceServerId: null,
        /** 退款原因 */
        feedbackReason: "",
        feedbackLabel: "",
        feedbackValue: null,
        /** 图片凭证 */
        attachFile: [],
        /** 案源实际地区编码（反馈标签选择案源城市不匹配时必传） */
        actualRegionCode: undefined,
      },
      cityName: "",
      /** 退款原因选项 */
      reasonList: [],
      rules: {
        feedbackValue: [
          {
            required: true,
            message: "请选择退款原因",
          },
        ],
        actualRegionCode: [
          {
            validator: () => {

              if (!this.isNeedActualRegionCode) return true;

              return !!this.params.actualRegionCode;
            },
            message: "请选择案源城市",
          },
        ],
        feedbackReason: [
          {
            required: true,
            message: "请填写补充说明",
          },
        ],
        attachFile: [
          {
            validator: () => {
              return this.params.attachFile.length > 0;
            },
            message: "请上传截图凭证",
          },
        ],
      },
    };
  },
  computed: {
    /** 是否需要补充案源城市 */
    isNeedActualRegionCode() {
      const feedbackValue = Number(this.params.feedbackValue);

      return feedbackValue === 8;
    },
  },
  onLoad({ id }) {
    this.params.caseSourceServerId = id;

    this.getReasonList();
  },
  methods: {
    /** 获取退款原因 */
    getReasonList() {
      dataDetailList({
        groupCode: "SERVER_FEEDBACK_INVALID",
      }).then((res) => {
        this.reasonList = res.data;
      });
    },
    /** 选择退款原因 */
    selectReason(item) {
      this.params.feedbackLabel = item.label;
      this.params.feedbackValue = item.value;
    },
    /** 地区选择 */
    areaChange(data) {
      this.cityName = data.cityName;
      this.params.actualRegionCode = data.workCity;
    },
    /** 点击提交 */
    submit() {
      const params = {
        ...this.params,
        // ! 判断是否需要传地区code
        actualRegionCode: this.isNeedActualRegionCode ? this.params.actualRegionCode : undefined,
      };

      appValidator(params, this.rules).then(() => {
        caseSourceServerV2Feedback({
          ...this.params,
          attachFile: this.params.attachFile?.map((item) => item.url).join(","),
        }).then(({ data = {} }) => {
          this.$toast("提交成功").then(() => {
            console.log(data.id, "退单id");
            turnToRefundDetailPage({ id: data.id, isReplace: true });
          });
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/pages/pay/refund/styles/refund.scss";

.submit {
  width: 343px;
  height: 44px;
  background: #3887f5;
  border-radius: 68px;
  opacity: 1;
  margin: 8px auto;
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.city {
  width: 343px;
  height: 44px;
  background: #F5F5F7;
  border-radius: 8px;
  opacity: 1;
  box-sizing: border-box;
  padding: 0 16px;
  display: flex;
  color: #999999;
  font-size: 14px;
  align-items: center;
  justify-content: space-between;

  &--selected {
    color: #666666;
  }
}

.reason {
  display: grid;
  grid-template-columns: 116px 172px;
  grid-gap: 16px 12px;

  &__item {
    padding: 6px 16px;
    background: #f5f5f7;
    border-radius: 68px;
    opacity: 1;
    font-size: 14px;
    font-weight: 400;
    color: #333333;

    &--selected {
      background: #e9f0ff;
      color: #3887f5;
    }
  }
}

.content {
  background: #f5f5f7;
  width: 343px;
  border-radius: 8px;
  padding: 8px 8px 0 8px;
  box-sizing: border-box;

  .textarea {
    background: #f5f5f7;

    textarea {
      resize: none;
      border: none;
      background: #f5f5f7;
      width: 327px;
      height: 49px;
      color: #222;
      box-sizing: border-box;
      font-size: 14px;
    }

    .number {
      font-size: 12px;
      font-weight: 400;
      color: #999999;
      text-align: right;
    }
  }
}
</style>
