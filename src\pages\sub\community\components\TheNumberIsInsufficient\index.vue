<template>
  <app-popup
    :safeAreaInsetBottom="false"
    :show="getShow"
    mode="center"
  >
    <div class="pt-[24px] w-[311px]">
      <img
        class="w-[160px] h-[120px] block mx-auto"
        src="@/pages/sub/community/imgs/<EMAIL>"
        alt=""
      >
      <p class="font-bold text-[16px] text-[#333333] pt-[12px] text-center">
        进群次数不足
      </p>
      <p class="text-[14px] text-[#666666] text-center pt-[12px] pb-[24px]">
        进群次数为 <span class="text-[#366EFF]">0</span>，分享群聊次数<span class="text-[#366EFF]">+1</span>
      </p>
      <div class="border-0 border-t-[1px] border-solid border-[#EEEEEE] flex">
        <div class="flex-1 text-[16px] text-[#000000] center h-[46px]" @click="getShow = false">
          放弃机会
        </div>
        <div class="flex-1 text-[16px] text-[#3887F5] center border-0 border-l-[1px] border-solid border-[#EEEEEE]  h-[46px]">
          分享邀请律师
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";

export default {
  name: "TheNumberIsInsufficient",
  components: { AppPopup },
  props: {
    value: {
      type: Boolean,
      default: false
    },
  },
  computed: {
    getShow: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
};
</script>

<style scoped lang="scss">

</style>