<template>
  <app-popup
    :safeAreaInsetBottom="false"
    :show="getShow"
    mode="center"
  >
    <div class="w-[311px] bg-[#FFFFFF] rounded-[16px]">
      <p class="font-bold text-[16px] text-[#333333] pt-[24px] text-align-center">
        进群次数说明
      </p>
      <p class="w-[263px] mx-auto  text-[15px] text-[#323233] pt-[12px] pb-[24px]">
        每日免费进群上限<span class="text-[#3887F5]">{{ getLawyerGroupConfig.dailyMaxBenefitCount }}次</span>，可分享社群增加 次数（限{{ getLawyerGroupConfig.dailyMaxShareBenefitCount }}次）
      </p>
      <div class="px-[24px] pb-[16px]">
        <div
          class="h-[36px] bg-[#3887F5] rounded-[68px] font-bold text-[14px] text-[#FFFFFF] flex items-center justify-center"
          @click="getShow=false"
        >
          知道了
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";

export default {
  name: "TheNumberOfGroupsIsExplainedPopup",
  components: { AppPopup },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    getShow: {
      get(){
        return this.value;
      },
      set(val){
        this.$emit("input", val);
      }
    },
    /* 获取进群权益 */
    getLawyerGroupConfig(){
      return this.$store.getters["user/getLawyerGroupConfig"];
    }
  }
};
</script>

<style scoped lang="scss">

</style>