<template>
  <div class="flex items-center justify-between">
    <div class="flex items-center">
      <img
        :src="data.icon"
        alt=""
        class="w-[32px] h-[32px] block rounded-[8px] shrink-0"
      >
      <div class="ml-[12px]">
        <div class="flex items-center">
          <div class="text-[16px] font-bold text-[#333333] mr-[8px]">
            {{ data.serviceName }}
          </div>
          <div class="text-[12px] text-[#666666]">
            ¥{{ (data.servicePrice ? data.servicePrice : 0) | amountFilter }}/{{
              data.serviceNum + data.unitLabel
            }}
          </div>
        </div>
        <div
          class="text-[12px] text-[#999999] mt-[2px] text-ellipsis w-[180px]"
        >
          {{ data.info }}
        </div>
      </div>
    </div>
    <div
      class="relative w-[82px] box-border h-[30px] bg-[linear-gradient(_116deg,_#71B5FF_0%,_#2676E4_100%)] [box-shadow:0px_4px_8px_0px_rgba(56,135,245,0.3)] rounded-[68px] flex items-center justify-center"
      @click="handleClick"
    >
      <img
        v-if="tipsShow"
        alt=""
        class="absolute w-[60px] h-[16px] block -right-[4px] -top-[11px]"
        src="../img/tips.png"
      >
      <div class="font-bold text-[13px] text-[#FFFFFF]">
        分享下单
      </div>
      <img
        alt=""
        class="w-[12px] h-[12px] block"
        src="../img/right-arrow.png"
      >
    </div>
  </div>
</template>

<script>

export default {
  name: "LawyerHomeServersItemServiceOld",
  props: {
    data: {
      type: Object,
      default: () => ({}),
      required: true,
    },
  },
  data(){
    return {
      tipsShow: false
    };
  },
  mounted() {
    this.showTips();
  },
  methods: {
    showTips() {
      this.tipsShow = true;
    },
    handleClick() {
      this.$emit("click", this.data);
    },
  }
};
</script>
