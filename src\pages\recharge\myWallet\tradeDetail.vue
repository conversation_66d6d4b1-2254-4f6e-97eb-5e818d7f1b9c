<template>
  <div class="tradeDetail">
    <div class="title">
      {{ detail.title }}
    </div>
    <div class="num">
      <span class="symbol">{{ detail.type===1 ? '+' : '-' }}</span>
      {{ priceNumber(detail.amount) }}
    </div>
    <div class="sub-title">
      {{ detail.remark }}
    </div>
    <div class="line flex flex-space-between">
      <div class="label">
        交易类型
      </div>
      <div class="value">
        {{ type }}
      </div>
    </div>
    <div class="line flex flex-space-between">
      <div class="label">
        订单编号：
      </div>
      <div class="value">
        {{ detail.orderNo }}
      </div>
    </div>
    <div class="line flex flex-space-between">
      <div class="label">
        创建时间：
      </div>
      <div class="value">
        {{ detail.updateTime }}
      </div>
    </div>
  </div>
</template>

<script>
import { lawyerBillDetail } from "@/api/recharge";
import { dataDetailList } from "@/api";
import { priceNumber } from "@/libs/tool";

export default {
  data() {
    return {
      detail: {},
      typeList: []
    };
  },
  methods: { priceNumber },
  onLoad(query) {
    dataDetailList({ groupCode: "LAWYER_BILL_ACTION_TYPE" }).then(({ data = [] }) => {
      this.typeList = data;
    });
    lawyerBillDetail(query.id).then(({ data = {} }) => {
      this.detail = data;
    });
  },
  computed: {
    type() {
      return this.typeList.find(item => Number(item.value) === this.detail.action)?.label;
    }
  }
};
</script>

<style lang="scss" scoped>
.tradeDetail {
  padding: 32px 16px 0;
  min-height: 100vh;
  background: #fff;
  text-align: center;
  .title{
    font-size: 14px;
    font-weight: 500;
    color: #333333;
  }
  .num{
    font-size: 39px;
    font-weight: 600;
    color: #333333;
    margin-top: 16px;
    .symbol{
      font-size: 32px;
    }
  }
  .sub-title{
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    margin-top: 12px;
    padding-bottom: 32px;
    border-bottom: 0.5px solid #eee;
  }
  .line{
    font-size: 14px;
    font-weight: 400;
    line-height: 48px;
    .label{
      color: #999999;
    }
    .value{
      color: #666666;
    }
  }
}
</style>
