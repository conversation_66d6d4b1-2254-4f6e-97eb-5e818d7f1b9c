<template>
  <div>
    <app-popup
      closeable
      :show="value"
      @cancel="$emit('input',false)"
    >
      <div>
        <div class="title">
          确认结束跟进吗？
        </div>
        <div class="content">
          结束案源跟进后，案源状态将会变成已完成，可到我的案源-已完成查看详情哦
        </div>
        <div
          class="button"
          @click="$emit('conform')"
        >
          确认
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";

export default {
  name: "ClueRightsDetailCardPopup",
  components: { AppPopup },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style scoped lang="scss">
.title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  padding: 13px 16px;
  text-align: center;

  .iconfont {
    float: right;
    font-size: 18px;
    font-weight: 400;
  }
}

.content {
  margin-top: 12px;
  padding: 0 16px;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
}

.button {
  width: 343px;
  height: 44px;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  background: #3887F5;
  border-radius: 22px;
  opacity: 1;
  box-sizing: border-box;
  margin: 20px auto 24px auto;
}
</style>
