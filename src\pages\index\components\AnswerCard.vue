<template>
  <div
    :style="{ borderRadius: radius }"
  >
    <div @click="toAsk">
      <div class="flex items-center">
        <img
          alt=""
          class="w-[16px] h-[16px] block mr-[5px] shrink-0"
          src="@/pages/index/imgs/<EMAIL>"
        >
        <div class="font-bold text-[16px] text-[#333333] text-ellipsis">
          {{ data.detail }}
        </div>
      </div>
      <div class="mt-[12px] flex items-center">
        <img
          alt=""
          class="w-[16px] h-[16px] block mr-[5px] shrink-0"
          src="@/pages/index/imgs/<EMAIL>"
        >
        <div class="text-[14px] text-[#666666] text-ellipsis">
          {{ data.replyContent }}
        </div>
      </div>
      <div
        v-if="!isArrNull(lawyerHeadImgs)"
        class="flex items-center justify-between mt-[20px]"
      >
        <div class="pl-[4px] pr-[8px] py-[3px] bg-[#F5F5F7] rounded-[60px] flex items-center">
          <div class="lawyer-avatar flex items-center">
            <img
              v-for="(i, index) in lawyerHeadImgs"
              :key="index"
              :src="i"
              alt=""
              class="avatar block w-[22px] h-[22px] rounded-full"
            >
          </div>
          <div class="text-[13px] text-[#333333] ml-[4px] flex items-center">
            已有<span class="text-[#3887F5] mx-[4px]">{{ data.rushAnswerCount }}</span>位律师已解答
          </div>
        </div>
        <div
          class="w-[76px] h-[28px] bg-[#EBF3FE] rounded-[40px] flex items-center justify-center text-[12px] text-[#3887F5]"
        >
          我要解答
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { isArrNull } from "@/libs/basics-tools.js";
import { toAnswerDetails } from "@/libs/turnPages";

export default {
  name: "AnswerCard",
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    radius: {
      type: String,
      default: ""
    },
  },
  computed: {
    /* 律师头像最多3个*/
    lawyerHeadImgs() {
      return (this.data.lawyerHeadImgs || "").split(",").slice(0, 3);
    }
  },
  methods: {
    isArrNull,
    toAsk() {
      toAnswerDetails({
        id: this.data.id
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.lawyer-avatar {
  padding-right: 4px;

  .avatar {
    border: 1px solid #ffffff;
    margin-right: -8px;
    position: relative;

    &:last-child {
      margin-right: 0;
    }

    @for $i from 1 through 4 {
      &:nth-child(#{$i}) {
        z-index: calc(4 - #{$i});
      }
    }
  }
}
</style>
