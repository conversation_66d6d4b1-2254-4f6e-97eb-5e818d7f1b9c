<template>
  <div>
    <div :style="style" />
    <u-safe-bottom v-if="showSafe" />
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "AppPlaceholder",
  components: { USafeBottom },
  props: {
    height: {
      type: [Number, String],
      default: 65,
    },
    /** 是否显示安全区域 */
    showSafe: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    style() {
      let height = uni.$u.getPx(this.height, true);

      console.log(height, "height================");

      return `height: ${height};`;
    },
  },
};
</script>

<style lang="scss" scoped></style>
