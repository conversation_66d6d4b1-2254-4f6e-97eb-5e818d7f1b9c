<template>
  <div>
    <!--      审核已通过-->
    <div
      v-if="data.auditStatus===1"
      class="flex px-[16px] py-[8px] text-[14px] text-[#3887F5]  items-center bg-[#EBF3FE] rounded-[6px]"
    >
      <img
        class="w-[24px] h-[24px] flex-shrink-0"
        src="@/pages/sub/community/imgs/<EMAIL>"
        alt=""
      >
      <p class="pl-[8px] text-ellipsis">
        审核已通过
      </p>
    </div>
    <!--      审核中..-->
    <div
      v-else-if="data.auditStatus===0"
      class="flex px-[16px] py-[8px] text-[14px] text-[#F78C3E]  items-center bg-[#FAF2E8] rounded-[6px]"
    >
      <img
        class="w-[24px] h-[24px] flex-shrink-0"
        src="@/pages/sub/community/imgs/Frame1321314991@3x_1.png"
        alt=""
      >
      <p class="pl-[8px] text-ellipsis">
        审核中..
      </p>
    </div>
    <!--      审核不通过-->
    <div
      v-else-if="data.auditStatus===2"
      class="flex px-[16px] py-[8px] text-[14px] text-[#EB4738]  items-center bg-[#FCF1ED] rounded-[6px]"
    >
      <img
        class="w-[24px] h-[24px] flex-shrink-0"
        src="@/pages/sub/community/imgs/Frame1321314991@3x_2.png"
        alt=""
      >
      <p class="pl-[8px] text-ellipsis">
        不通过，原因：与行业无关
      </p>
    </div>
    <!--      已过期 或者 已关闭-->
    <div
      v-else-if="data.status===0||data.isExpired===1"
      class="flex px-[16px] py-[8px] text-[14px] text-[#999999]  items-center bg-[#F7F8FC] rounded-[6px]"
    >
      <img
        class="w-[24px] h-[24px] flex-shrink-0"
        src="@/pages/sub/community/imgs/Frame1321314991@2x_3.png"
        alt=""
      >
      <p class="pl-[8px] text-ellipsis">
        {{ data.status===0?"已关闭，原因：内容涉嫌违规":"已过期" }}
      </p>
    </div>
  </div>
</template>

<script>
export default {
  name: "CommunityRecordItemState",
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
};
</script>

<style scoped lang="scss">

</style>