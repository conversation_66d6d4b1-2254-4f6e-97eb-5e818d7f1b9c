<template>
  <app-popup
    :show="getShow"
    mode="center"
    :safeAreaInsetBottom="false"
    round="16"
    @close="close"
  >
    <div class="w-[305px] box-border">
      <div class="flex  py-[24px] flex-col items-center">
        <div class="font-bold text-[16px] text-[#333333]">
          {{ wechatPopupData.lawyerName }}律师的微信
        </div>
        <div class="flex items-center pt-[12px]">
          <img
            class="block w-[24px] h-[24px]"
            alt=""
            src="@/pages/sub/collaboration-details/imgs/btn_icon_weixin(1).png"
          >
          <div class="pl-[10px] text-[16px] text-[#666666]">
            {{ wechatPopupData.lawyerWeChatId }}
          </div>
        </div>
      </div>
      <div class=" flex border-0 border-solid border-t-[1px] border-[#EEEEEE]">
        <div
          class="h-[48px] border-0 border-solid border-r-[1px] border-[#EEEEEE] center flex-1 text-[16px] text-[#000000]"
          @click.stop="close"
        >
          取消
        </div>
        <div
          class="h-[48px] center flex-1 text-[16px] text-[#3887F5]"
          @click.stop="copyWeChat"
        >
          复制
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";

export default {
  name: "LawyerWeChatPopup",
  components: { AppPopup },
  props: {
    wechatPopupData: {
      type: Object,
      default: () => ({}),
    },
    value: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    getShow: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
  methods: {
    close() {
      this.getShow = false;
    },
    copyWeChat() {
      this.close();
      uni.setClipboardData({
        data: this.wechatPopupData.lawyerWeChatId,
        success: () => {
          this.showSharePopup = false;

          uni.showToast({
            title: "复制成功",
            icon: "none",
          });
        },
        fail: (err) => {
          console.log(err);

          uni.showToast({
            title: "复制失败，请检查权限",
            icon: "none",
          });
        },
      });
    },
  },
};
</script>
