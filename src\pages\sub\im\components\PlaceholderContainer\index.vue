<template>
  <div
    :style="[placeholderStyle]"
    class="placeholder-container"
  >
    <div
      :id="fixedId"
      class="fixed-container"
    >
      <slot />
    </div>
  </div>
</template>

<script>

/* 占位组件 解决position: fixed; 占位问题*/

import { guid } from "@/uview-ui/libs/function";
import { isNull } from "@/libs/basics-tools.js";

export default {
  name: "PlaceholderContainer",
  data() {
    return {
      fixedId: guid(),
      placeholderStyle: {

      }
    };
  },
  mounted() {
    this.initHeight();
  },
  methods: {
    initHeight(){
      this.$nextTick(() => {
        try {
          const query = uni.createSelectorQuery().in(this);
          query.select("#" + this.fixedId).boundingClientRect((data = {}) => {
            console.log(data);
            const height = data.height;
            if(!isNull(height)){
              this.placeholderStyle = {
                height: height + "px"
              };
            }
          }).exec();
        }catch (e) {
          console.log(e);
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.fixed-container{
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 3;
}
</style>
