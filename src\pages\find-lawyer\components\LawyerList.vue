<template>
  <div>
    <u-sticky>
      <div>
        <slot />
        <div
          v-if="isContactLawyerOverLimit"
          class="flex bg-[linear-gradient(_91deg,_#EBF1FF_0%,_#DDEDFF_100%)] items-center pl-[6px] pr-[16px] overflow-hidden relative"
        >
          <div class="flex items-end w-[48px] h-[60px]" />

          <img
            class="  w-[48px] h-[60px] absolute left-[6px] bottom-[0]"
            src="../../sub/imgs/<EMAIL>"
            alt=""
          >
          <p class="flex items-center pl-[10px] w-[224px] text-[14px] text-[#1D7FE1] py-[14px] pr-[7px]">
            今日打招呼次数已用完，如有异地协作需求，请到接单广场发布公开需求
          </p>
          <div
            class="flex items-center justify-center font-bold text-[12px] text-[#FFFFFF] w-[64px] h-[29px] bg-[linear-gradient(_90deg,_#FA700D_0%,_#F34747_100%)] [box-shadow:0px_4px_6px_0px_rgba(203,220,238,0.6)] rounded-[17px]"
            @click.stop="toPublishCollaboration"
          >
            去发布
          </div>
        </div>
        <div
          v-else
          class="flex px-[16px] pt-[13px] pb-[16px] bg-[#f5f5f7]"
        >
          <img
            class="w-[150px] h-[20px]"
            src="@/pages/find-lawyer/imgs/<EMAIL>"
            alt=""
          >
          <p class="flex items-center pl-[10px] text-[14px] text-[#1D7FE1]">
            今日打招呼次数：{{ getRemainContactLawyer }}/<span class="text-[#FA700D] pl-[4px]">{{ getTotalContactLawyer }}次</span>
          </p>
        </div>
        <find-lawyer-search-tab-bar @searchChange="handleSearch" />
      </div>
    </u-sticky>
    <div
      v-if="isArrNull(list)&&isRequest"
      class="flex items-center justify-center pt-[24px]"
    >
      <img
        alt=""
        class="w-[240px] h-[216px]"
        src="../../sub/imgs/<EMAIL>"
      >
    </div>
    <div class="px-[12px] pb-[20px]">
      <div
        v-for="i in list"
        :key="i.id"
        class="px-[16px] pt-[12px] mt-[12px] first-of-type:mt-[0] rounded-[12px] bg-[#FFFFFF]"
      >
        <lawyer-card :data="i" />
      </div>
    </div>
    <tip-popup />
  </div>
</template>

<script>
import { bindOnHook } from "@/libs/hooks";
import FindLawyerSearchTabBar from "@/pages/find-lawyer/components/searchTabBar.vue";
import LawyerCard from "@/pages/find-lawyer/components/LawyerCard.vue";
import TipPopup from "@/pages/find-lawyer/components/TipPopup.vue";
import { mapGetters } from "vuex";
import { isArrNull } from "@/libs/basics-tools";
import { toAnswerDetails, toPublishCollaboration, turnToLawyerAuthResultPageToLogin } from "@/libs/turnPages";
import { findLawyerByToosPageList } from "@/api/lawyer";

export default {
  name: "LawyerList",
  components: { TipPopup, LawyerCard, FindLawyerSearchTabBar },
  data() {
    return {
      query: {
        currentPage: 1,
        pageSize: 10,
      },
      isRequest: false,
      isLastPage: false,
      list: []
    };
  },
  computed: {
    ...mapGetters(
      "user",
      ["getTotalContactLawyer", "getRemainContactLawyer", "isContactLawyerOverLimit"]
    )
  },
  mounted() {
    const parent = bindOnHook.call(this, "onReachBottom", this.onReachBottomCallback);
    const parentPullDownRefresh = bindOnHook.call(this, "onPullDownRefresh", this.onPullDownRefreshCallback);
    this.$on("hook:destroyed", () => {
      parent.$off("hook:onReachBottom", this.onReachBottomCallback);
      parentPullDownRefresh.$off("hook:onPullDownRefresh", this.onPullDownRefreshCallback);
    });
  },
  methods: {
    isArrNull,
    onReachBottomCallback(){
      this.query.currentPage++;
      this.getList();
    },
    onPullDownRefreshCallback(){
      this.$store.dispatch("user/updateRemainContactLawyer");
      console.log("refresh");
      this.resetData();
      this.getList().finally(() => {
        uni.stopPullDownRefresh();
      });
    },
    toPublishCollaboration() {
      turnToLawyerAuthResultPageToLogin(() => {
        toPublishCollaboration();
      });
    },
    /* 搜索 */
    handleSearch(data) {
      this.query = {
        ...this.query,
        ...data
      };
      this.resetData();
      this.getList();
    },
    /** 重置数据 */
    resetData() {
      this.list = [];
      this.query.currentPage = 1;
      this.isLastPage = false;
      this.isRequest = false;
    },
    getList() {
      if (this.isLastPage) {
        return Promise.resolve();
      }
      return findLawyerByToosPageList(this.query).then(res => {
        this.list = [...this.list, ...res.data.records];
        if (this.list.length >= res.data.total) {
          this.isLastPage = true;
        }
        return this.list;
      })
        .finally(() => {
          this.isRequest = true;
        });
    },
    toDetail(data) {
      toAnswerDetails({
        id: data.id
      });
    }
  }
};
</script>

<style scoped lang="scss">

</style>