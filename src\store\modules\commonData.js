import { CASE_GRADE } from "@/enum/index.js";
import {remainClueNum} from "@/api";

export default {
  namespaced: true,
  state: {
    // 案件等级
    CASE_GRADE: CASE_GRADE,
    // 获取剩余条数数
    remainClueNumList: []
  },
  mutations: {
    SET_remainClueNum(state, data) {
      state.remainClueNumList = data;
    }
  },
  actions: {
    // 获取剩余条数数
    setRemainClueNum({ commit }) {
      remainClueNum().then(({ data = [] }) => {
        commit("SET_remainClueNum", data);
      });
    }
  },
  getters: {
    getCaseGrade(state) {
      return state.CASE_GRADE;
    },
    getRemainClueNumList(state) {
      return state.remainClueNumList;
    }
  }
};
