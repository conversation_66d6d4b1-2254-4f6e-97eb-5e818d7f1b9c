<template>
  <div>
    <app-popup
      :show="show"
      round="16"
    >
      <div class="popup">
        <div class="header">
          <i
            class="iconfont icon-a-cuowuguanbi"
            @click="show = false"
          />
        </div>
        <div class="content">
          <div class="title">
            选择您擅长的领域
          </div>
          <div class="tips">
            我们将根据您的设置，个性化向您推送资源
          </div>
          <div class="areas">
            <div class="areas-title">
              全部领域
            </div>
            <div>最多选择{{limit}}个</div>
          </div>
          <ul class="select">
            <li
              v-for="item in areas"
              :key="item.value"
              :class="[
                {
                  'select-item--active':
                    selected.findIndex((i) => i.value === item.value) > -1,
                },
              ]"
              class="select-item"
              @click="handleClick(item)"
            >
              {{ item.label }}
            </li>
          </ul>
          <!-- 底部确认按钮 -->
          <div
            class="button"
            @click="confirm"
          >
            选好了（{{ selected.length }}/{{limit}}）
          </div>
        </div>
      </div>
    </app-popup>
    <div @click="show = true">
      <slot>
        <app-field
          align="left"
          arrow
        >
          <div class="text-cccccc">
            <div v-if="value.length <= 0">
              请选择
            </div>
          </div>
        </app-field>
      </slot>
    </div>
  </div>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { dataDetailList } from "@/api";
import AppField from "@/components/AppComponents/AppField/index.vue";

export default {
  name: "AppAreas",
  components: { AppField, AppPopup },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    /** 限制数量 */
    limit: {
      type: Number,
      default: 3,
    },
    /** value 字段 */
    valueKey: {
      type: String,
      default: "workFieldCode",
    },
    /** label 字段 */
    labelKey: {
      type: String,
      default: "workFieldName",
    },
  },
  data() {
    return {
      show: false,
      /** 擅长数据 */
      areas: [],
      /** 选择的数据 */
      selected: [],
    };
  },
  created() {
    this.getAreas();
  },
  watch: {
    value: {
      handler(val) {
        this.selected = val.map((item) => ({
          value: item[this.valueKey],
          label: item[this.labelKey],
        }));
      },
      immediate: true,
    },
  },
  methods: {
    /** 请求擅长数据 */
    getAreas() {
      dataDetailList({
        groupCode: "LAWYER_SPECIALITY",
      }).then(({ data = {} }) => {
        this.areas = data;
      });
    },
    handleClick(item) {
      const index = this.selected.findIndex((i) => i.value === item.value);

      if (index > -1) {
        this.selected.splice(index, 1);
      } else {
        if (this.selected.length >= this.limit) {
          return;
        }

        this.selected.push(item);
      }
    },
    /** 确认 */
    confirm() {
      const data = this.selected.map((item) => ({
        [this.valueKey]: item.value,
        [this.labelKey]: item.label,
      }));

      this.$emit("input", data);
      this.show = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 16px;
  height: 48px;
  opacity: 1;

  .iconfont {
    font-size: 24px;
  }
}

.content {
  padding: 0 16px;

  .title {
    font-size: 20px;
    font-weight: bold;
    color: #333333;
  }

  .tips {
    margin-top: 12px;
    font-size: 14px;
    font-weight: 400;
    color: #999999;
  }

  .areas {
    margin-top: 24px;
    align-items: flex-end;
    display: flex;
    font-size: 12px;
    font-weight: 400;
    color: #333333;

    &-title {
      margin-right: 16px;
      font-size: 16px;
      font-weight: bold;
      color: #333333;
    }
  }

  .select {
    margin-top: 12px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 16px;

    &-item {
      display: flex;
      justify-content: center;
      align-items: center;
      background: #f5f5f7;
      border-radius: 68px;
      opacity: 1;
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      height: 36px;
      box-sizing: border-box;

      &--active {
        background: #3887f5;
        color: #ffffff;
      }
    }
  }

  .button {
    margin-top: 52px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 343px;
    height: 44px;
    background: #3887f5;
    border-radius: 68px;
    opacity: 1;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
  }
}
</style>
