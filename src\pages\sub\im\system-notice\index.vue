<template>
  <div class="system-notice">
    <div
      v-if="isArrNull(list)"
      class="placeholder-container"
    >
      <img
        class="tip-img"
        src="../../../message-notice/imgs/no-data3.png"
        alt=""
      >
      <p class="tip">
        一条消息都没有哦
      </p>
    </div>
    <div v-else>
      <notice-card
        v-for="i in list"
        :key="i.id"
        :type="type"
        :data="i"
        :showAvatar="false"
      />
      <u-divider
        v-if="isEnd"
        text="已经是最后一条了"
      />
      <u-safe-bottom />
    </div>
  </div>
</template>

<script>
import UDivider from "@/uview-ui/components/u-divider/u-divider.vue";
import NoticeCard from "@/pages/sub/im/components/NoticeCard/index.vue";
import { coreMessageAllRead, coreMessageList } from "@/api/im.js";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { onReachBottomPage } from "@/libs/tools.js";
import { SYSTEM_MSG_TYPE } from "@/enum/imCardEnum.js";
import { isArrNull } from "@/libs/basics-tools.js";

export default {
  name: "Index",
  components: { USafeBottom, NoticeCard, UDivider },
  data() {
    return {
      list: [],
      type: "",
      onReachBottomPage: {},
      isEnd: false
    };
  },
  onLoad({ type }){
    this.type = type;
    this.onReachBottomPage = onReachBottomPage((data) => {
      return coreMessageList({ ...data, messageCenterEnum: type });
    });
    uni.setNavigationBarTitle({
      title: type === SYSTEM_MSG_TYPE.SYSTEM ? "系统通知" : "服务助手"
    });
    this.requestList().then(() => {
      if(!isArrNull(this.list)){
        coreMessageAllRead({
          messageCenterEnum: type
        });
      }
    });

  },
  methods: {
    isArrNull,
    requestList(){
      return this.onReachBottomPage().then(({ list = [], paginationState }) => {
        this.isEnd = paginationState.isEnd;
        this.list = [...this.list, ...list];
        return list;
      });
    }
  },
  onReachBottom(){
    this.requestList();
  }
};
</script>

<style scoped lang="scss">
.system-notice{
  padding: 0 12px;
}
.placeholder-container{
  padding-top: 114px;
  text-align: center;
  .tip-img{
    width: 240px;
    height: 180px;
  }
  .tip{
    padding-top: 16px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
  }
}
</style>
