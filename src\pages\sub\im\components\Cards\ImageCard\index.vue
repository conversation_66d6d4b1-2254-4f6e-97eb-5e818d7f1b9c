<template>
  <div :class="[`div-container-${getImgClass}`]">
    <img
      :src="getSrc"
      :class="[`image-${getImgClass}`]"
      alt=""
      @click="previewImg(data.msg)"
    >
  </div>
</template>

<script>
import cardProps from "@/pages/sub/im/mixins/card-props.js";

export default {
  name: "ImageCard",
  inheritAttrs: false,
  mixins: [cardProps],
  computed: {
    getImgClass() {
      /* 宽图（h：w＞1）*/
      if (this.data.width > this.data.height) {
        return "width";
        /* 高图（h：w＜1）*/
      } else if (this.data.width < this.data.height) {
        return "height";
      }else{
        /* 方图（h：w=1）*/
        return "square";
      }
    },
    getSrc(){
      return this.data.msg;
    }
  },
  methods: {
    previewImg(url){
      uni.previewImage({
        urls: [url]
      });
    }
  }
};
</script>

<style scoped lang="scss">
img{
  object-fit: cover;
}
.div-container-width{
  width: 209px;
  height: 140px;
}

.div-container-height{
  width: 140px;
  height: 170px;
}

.div-container-square{
  width: 140px;
  height: 140px;
}

.image-width{
  max-width: 209px;
  max-height: 140px;
}

.image-height{
  max-width: 140px;
  max-height: 170px;
}

.image-square{
  max-width: 140px;
  max-height: 140px;
}
</style>
