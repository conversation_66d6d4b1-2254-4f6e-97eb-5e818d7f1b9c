<template>
  <app-popup
    :show="show"
    mode="center"
    :round="16"
    closeable
    :safeAreaInsetBottom="false"
    @cancel="show = false"
  >
    <div class="w-[311px] box-border">
      <div class="px-[24px] pt-[24px] pb-[16px]">
        <div class="text-center mb-[12px]">
          <p class="font-bold text-[16px] text-[#333333]">
            新增手机号
          </p>
        </div>
        <div class="mb-[24px]">
          <div
            class="flex items-center bg-[#F5F5F7] rounded-[8px] px-[12px] py-[10px]"
          >
            <input
              v-model="phone"
              class="flex-1 bg-transparent text-[14px] text-[#333333] outline-none"
              placeholder="请填写新手机号"
              placeholder-style="color: #999999;font-weight: 400;"
            >
            <i
              v-if="phone"
              class="iconfont icon-cuowu !text-[14px] !text-[#999999] ml-[8px]"
              @click="clearInput"
            />
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div
            class="w-[84px] h-[44px] rounded-[68px] border-[1px] border-solid border-[#CCCCCC] text-[16px] text-[#333333] flex items-center justify-center"
            @click="handleCancel"
          >
            取消
          </div>
          <div
            class="w-[167px] h-[44px] bg-[#3887F5] rounded-[68px] text-[16px] text-[#FFFFFF] flex items-center justify-center"
            :class="{ 'opacity-60': !phone }"
            @click="handleConfirm"
          >
            确定
          </div>
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { lawyerUpdateOtherPhone } from "@/api/user";
import store from "@/store";

export default {
  name: "OtherPhoneBindPopup",
  components: { AppPopup },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      phone: "",
    };
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  methods: {
    clearInput() {
      this.phone = "";
    },
    handleClose() {
      this.show = false;
    },
    handleCancel() {
      this.show = false;
    },
    async handleConfirm() {
      const phone = this.phone.trim();

      if (!phone) {
        uni.showToast({
          title: "请填写手机号",
          icon: "none",
        });
        return;
      }

      try {
        await lawyerUpdateOtherPhone({ phone });

        uni.showToast({
          title: "绑定成功",
          icon: "none",
        });

        this.show = false;
        
        store.dispatch("user/setUserInfo");
      } catch (error) {
        console.error(error);
      }
    },
  },
};
</script>