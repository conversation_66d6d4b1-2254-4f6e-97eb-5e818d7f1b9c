<template>
  <login-layout>
    <tabs
      :activeTabIndex="0"
      :list="MY_COLLABORATION_TABS()"
      mode="router"
    />
    <div class="px-[16px] pt-[12px] flex items-center">
      <p
        v-for="(i, index) in tabList"
        :key="index"
        :class="[activeTabIndex === index ? '!bg-[#3887F5] text-[#FFFFFF]' : '']"
        class="px-[16px] py-[6px] text-[14px] text-[#666666] bg-[#FFFFFF] rounded-[17px] mr-[8px] last-of-type:mr-[0]"
        @click="handoff(index)"
      >
        {{ i.name }}
      </p>
    </div>
    <div v-if="isArrNull(list) && isRequest">
      <collaboration-no-data
        :btnText="currentTab.btnText"
        :desc="currentTab.noDataDesc"
        deter
        @handleClick="jump"
      />
    </div>
    <template v-else>
      <div
        v-for="i in list"
        :key="i.id"
        class="px-[12px] pt-[12px]"
      >
        <collaboration-item
          :data="i"
          @handleClick="handleClick"
        >
          <div
            v-if="activeTabIndex === 0"
            class="pb-[16px] pt-[8px]"
            @click.stop="handleAgainPublish(i)"
          >
            <div
              class="center opacity-100 w-[200px] h-[36px] bg-[#3887F5] rounded-[100px] mx-auto text-[14px] text-[#FFFFFF]"
            >
              再次发布
            </div>
          </div>
        </collaboration-item>
      </div>
    </template>
    <u-safe-bottom />
  </login-layout>
</template>

<script>
import CollaborationNoData from "@/pages/index/components/collaborationNoData.vue";
import {
  toCollaboration,
  toCollaborationDetails,
  toPublishCollaboration,
} from "@/libs/turnPages";
import CollaborationItem from "@/pages/index/components/collaborationItem.vue";
import { isArrNull } from "@/libs/basics-tools";
import {
  lawyerCollaborationMyAcceptOrder,
  lawyerCollaborationMyPublishPage,
} from "@/api/collaboration";
import Tabs from "@/components/AppTabs/index.vue";
import { MY_COLLABORATION_TABS } from "@/enum/collaboration";
import LoginLayout from "@/components/login/LoginLayout.vue";
import { whetherToBindWechat } from '@/libs/tools';

export default {
  name: "CollaborationLists",
  components: { Tabs, CollaborationItem, CollaborationNoData, LoginLayout },
  data() {
    return {
      tabList: [
        {
          name: "我的发单",
          noDataDesc: "您还未发布过协作需求，赶紧去发布吧～",
          btnText: "去发布",
          jump: toPublishCollaboration,
          api: lawyerCollaborationMyPublishPage,
        },
        {
          name: "我的接单",
          noDataDesc: "您还未接单过需求，去广场看看吧～",
          btnText: "去广场",
          jump: toCollaboration,
          api: lawyerCollaborationMyAcceptOrder,
        },
      ],
      activeTabIndex: 0,
      list: [],
      query: {
        currentPage: 1,
        pageSize: 20,
      },
      /** 是否请求过 */
      isRequest: false,
      isLastPage: false,
    };
  },
  computed: {
    /* 当前选中的tab*/
    currentTab() {
      return this.tabList[this.activeTabIndex];
    },
  },
  mounted() {
    whetherToBindWechat(() => {
      this.resetData();
      this.getData();
    });
  },
  onReachBottom() {
    this.query.currentPage++;
    this.getData();
  },

  onPullDownRefresh() {
    console.log("refresh");
    this.resetData();
    this.getData().finally(() => {
      uni.stopPullDownRefresh();
    });
  },
  methods: {
    handleAgainPublish(data){
      toPublishCollaboration({ id: data.id });
    },
    MY_COLLABORATION_TABS() {
      return MY_COLLABORATION_TABS;
    },
    isArrNull,
    handleClick({ data }) {
      toCollaborationDetails(data);
    },
    handoff(index) {
      if (this.activeTabIndex === index) return;
      this.activeTabIndex = index;
      this.resetData();
      this.getData();
    },
    jump() {
      this.currentTab.jump();
    },
    /** 请求数据 */
    getData() {
      if (this.isLastPage) {
        return Promise.resolve();
      }

      return this.currentTab
        .api(this.query)
        .then((res) => {
          this.list = [...this.list, ...res.data.records];

          if (this.list.length >= res.data.total) {
            this.isLastPage = true;
          }
          return this.list;
        })
        .finally(() => {
          this.isRequest = true;
        });
    },
    /** 重置数据 */
    resetData() {
      this.list = [];
      this.query.currentPage = 1;
      this.isLastPage = false;
      this.isRequest = false;
    },
  },
};
</script>

<style lang="scss" scoped></style>
