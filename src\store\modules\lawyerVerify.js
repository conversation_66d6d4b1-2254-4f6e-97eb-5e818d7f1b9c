const lawyerVerify = {
  /** @type {number} 角色类型 0-兼职律师 1-自营律师 2-实习律师 */
  role: 0,
  /** @type {string} 律师个人头像地址,请用不带水印的图片上传接口上传即可. */
  imgUrl: undefined,
  /** @type {string} 律师姓名 */
  realName: undefined,
  /** @type {string} 地区代码 */
  workCity: undefined,
  /** @type {string} 地区名称 */
  cityName: undefined,
  /** @type {Array} 擅长领域 */
  workFields: [],
  /** @type {string} 律师执业证照片 角色类型为律师时,必传 */
  certificatePic: undefined,
  /** @type {string} 工作证照片 角色类型为实习律师时,必传 */
  workPic: undefined,
  /** @type {string} (律所名称或工作单位) */
  lawyerOffice: undefined,
  /** @type {number} 从业开始时间 */
  workStartTime: undefined,
  /** @type {string} 身份证人像面 地址律师端小程序必传 */
  idCardBackPic: undefined,
  /** @type {string} 身份证国徽面 地址律师端小程序必传 */
  idCardPosPic: undefined,
  /** @type {string} 身份证号码律师端小程序必传 */
  idCard: undefined,
  /** @type {string} 律师执业证号 */
  certificateId: undefined,
};

export default {
  namespaced: true,
  state: { lawyerVerify: { ...lawyerVerify } },
  mutations: {
    /** 设为默认值 */
    RESET_STORE(state) {
      state.lawyerVerify = { ...lawyerVerify };
    },
  },
};
