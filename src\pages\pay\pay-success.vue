<template>
  <div class="wrap">
    <div class="main">
      <div class="pay-img">
        <img
          alt=""
          class="img"
          src="./img/pay-success/success.png"
        >
      </div>
      <div class="info">
        <p class="p">
          支付成功
        </p>
      </div>
      <template v-if="orderInfo.type===6">
        <div class="msg">
          <div class="text">
            恭喜您已成功购买{{ detail.name }}
          </div>
          <div>使用线索包权益即时锁定本地案源线索</div>
        </div>
      </template>
    </div>
    <div class="btn-container">
      <div
        class="btn"
        @click="jump"
      >
        <p class="download-text">
          {{ orderInfo.type===6 ? '开始接案' : '立即抢单' }}
        </p>
      </div>
    </div>
  </div>
</template>

<script>

import { getCaseSourceGoodsDetail, queryOrderStatus } from "@/api/recharge";
import { turnToCaseSourceSquare } from "@/libs/turnPages";

export default {
  // mixins: [nightTime],
  data() {
    return {
      orderId: "",
      orderInfo: {},
      detail: {},
    };
  },
  onLoad(option) {
    this.queryOrder(option.id);
  },
  onShow() {
  },
  methods: {
    /** 查询订单 获取数据 */
    async queryOrder(id) {
      const obj = await queryOrderStatus({ orderId: id });
      this.orderInfo = obj?.data || {};
      getCaseSourceGoodsDetail(this.orderInfo.serviceId).then(({ data = {} }) => {
        this.detail = data;
      });
    },
    /** 去接案 */
    jump() {
      turnToCaseSourceSquare();
    },
  }
};
</script>

<style lang="scss" scoped>
.wrap {
  background: #ffffff !important;
  height: 100vh;
  overflow: hidden;
}


.main {
  width: 319px;
  padding-top: 124px;
  margin-left: 28px;

  display: flex;
  flex-direction: column;
  align-items: center;

  .pay-img {
    height: 120px;

    .img {
      width: 120px;
      height: 120px;
    }
  }

  .info {
    // flex: 1;
    padding-top: 12px;
    width: 255px;
    height: 22px;
    font-size: 18px;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: bold;
    color: #333333;
    line-height: 19px;

    .p {
      text-align: center;
    }
  }

  .msg {
    width: 319px;
    height: 40px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    text-align: center;
    margin-top: 16px;
    .text{
      margin-bottom: 3px;

    }
    .indent {
      color: #3887f5;
      margin: 0 3px;
    }
  }
}

.btn-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
  flex-direction: column;
  align-items: center;

  .btn {
    width: 160px;
    height: 32px;
    background: #3887f5;
    border-radius: 68px 68px 68px 68px;
    border: 1px solid #3887f5;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 24px;

    // &.toutiao {
    //   margin-top: 40px;
    // }

    .download-text {
      height: 20px;
      font-size: 14px;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 800;
      color: #ffffff;
      line-height: 20px;
    }
  }

  .btn-text {
    font-size: 12px;
    font-weight: 400;
    color: #3887f5;
    margin-top: 8px;
    text-align: center;
  }
}
</style>
