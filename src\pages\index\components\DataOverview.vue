<template>
  <div class="py-[16px] rounded-[8px] mx-[12px] bg-white">
    <div class="flex items-center pl-[16px] pb-[12px]">
      <img
        class="w-[20px] h-[20px]"
        src="@/pages/index/imgs/<EMAIL>"
        alt=""
      >
      <p class="pl-[4px] text-[14px] text-[#363739]">
        您好，xxx律师
      </p>
    </div>
    <div class="flex pt-[12px]">
      <div
          v-for="(item, index) in list"
          :key="index"
          class="flex-1 flex-shrink-0 flex flex-col items-center"
      >
        <p class="font-[500] text-[18px] text-[#333333]">
          3
        </p>
        <p class="text-[12px] text-[#9A9A9A] pt-[4px]">
          {{ item.title }}
        </p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "DataOverview",
  data() {
    return {
      list: [{
        title: "我发布的协作"
      }, {
        title: "我参与的协作"
      }, {
        title: "我发布的社群"
      }]
    };
  },
};
</script>

<style scoped lang="scss">

</style>