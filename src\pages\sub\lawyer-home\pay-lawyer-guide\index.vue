<template>
  <login-layout>
    <div>
      <login-layout>
        <div class="pd-lt-12 pd-rt-12 wrap">
          <div
            v-if="!!serverCode"
            class="mg-b-12"
          >
            <!-- 律师卡片 -->
            <pay-lawyer-guide-card
              :defaultService="defaultService"
              :lawyerInfo="lawyerInfo"
              @handleInfo="serviceExplainVisible = true"
            />
          </div>
          <div
            v-else
            class="lawyer-info"
          >
            <lawyer-info-card
              :lawyerInfo="lawyerInfo"
              @handleInfo="serviceExplainVisible = true"
            />
          </div>
          <my-litigate-container>
            <view class="pd-tp-12">
              <!--            <p class="description">请描述问题</p>-->
              <!-- 输入框 -->
              <pay-lawyer-guide-textarea
                v-model="saveAppointParams.info"
                placeholderText="为了律师能更加准确解答您的问题，请您正确描述问题类型，大致描述你需要解决的问题以及您需要的帮助（10-200字）"
                spanText=" "
                @select="selectType"
                @selectPopFun="selectPopFunVal"
              />
              <!-- 兼容支付宝              -->
              <!--  #ifndef MP-ALIPAY     -->
              <view class="im-history">
                <im-history
                  customStyle="background: #fff;"
                  theme="white"
                  @history-select="historySelect"
                />
              </view>
              <!--  #endif                -->
              <pay-lawyer-guide-advisory
                v-if="!serverCode"
                v-model="saveAppointParams.serverCode"
                :serviceManageList="serviceManageList"
              />
            </view>
            <div
              :class="{ 'pay-button__top': serverCode }"
              class="pay-button pc-max-w-fixed-center pc-max-w"
            >
              <view
                :class="['btn']"
                @click="payClick"
              >
                发起咨询
              </view>
            </div>
            <div class="pd-bm-16">
              <auto-change-lawyer
                v-model="saveAppointParams.authChangeLawyer"
              />
            </div>
          </my-litigate-container>
        </div>
        <div>
          <!-- 律师评价 -->
          <lawyer-guide-score :lawyerInfo="lawyerInfo" />
          <app-placeholder :height="65" />
          <!-- 重复订单弹窗 -->
          <u-popup
            :round="10"
            :show="orderVisible"
            borderRadius="14"
            mode="center"
          >
            <div class="order">
              <p class="order-title">
                提示
              </p>
              <p class="order-info">
                您有一笔相同服务订单，可前往立即支付
              </p>
              <div class="order-button">
                <view
                  class="btn"
                  @click="orderVisible = false"
                >
                  取消
                </view>
                <view
                  class="btn"
                  @click="toOrder"
                >
                  去支付
                </view>
              </div>
            </div>
          </u-popup>
        </div>
      </login-layout>
    </div>
    <lawyer-service-pay-popup
      :lawyerInfo="lawyerInfo"
      safeAreaInsetBottom
    />
    <app-service-info-popup
      v-model="serviceExplainVisible"
      :serverInfo="lawyerOrderInfo"
    />
  </login-layout>
</template>

<script>
import PayLawyerGuideAdvisory from "@/pages/sub/lawyer-home/pay-lawyer-guide/components/pay-lawyer-guide-advisory/index.vue";
import PayLawyerGuideTextarea from "@/components/pay-lawyer-guide/pay-lawyer-guide-textarea.vue";
import PayLawyerGuideCard from "@/components/pay-lawyer-guide/pay-lawyer-guide-card.vue";
import NoScroll from "@/components/no-scroll/index.vue";
import MyLitigateContainer from "@/components/my-litigate/my-litigate-container.vue";
import ImHistory from "@/components/im-history/im-history.vue";
import {
  caseSourceV2SaveAppoint,
  oneLawyer,
  postGetExistOrderByCurrentUser,
} from "@/api/lawyer.js";
import { shareAppMessage, whetherToLogIn } from "@/libs/tools.js";
import { toConfirmOrder, toImChatPage } from "@/libs/turnPages.js";
import { setPayLawyerGuideStorage } from "@/libs/token.js";
import { paralegalHistory } from "@/libs/paralegalTools.js";
import { saveParalegalData } from "@/libs/paralegalData.js";
import { payLawyerGuideBuriedPoint } from "@/pages/sub/lawyer-home/pay-lawyer-guide/js";
import LoginLayout from "@/components/login/login-layout.vue";
import UPopup from "@/uview-ui/components/u-popup/u-popup.vue";
import LawyerGuideScore from "@/pages/sub/lawyer-home/pay-lawyer-guide/components/lawyer-guide-score/index.vue";
import AppPlaceholder from "@/components/AppComponents/AppPlaceholder/index.vue";
import LawyerInfoCard from "@/pages/sub/lawyer-home/pay-lawyer-guide/components/lawyer-info-card/index.vue";
import AppServiceInfoPopup from "@/components/AppComponents/app-service-info-popup/index.vue";
import { lawyerOrderInfo } from "@/pages/rapid-consultation-confirm-order/assets/serverInfo.js";
import AutoChangeLawyer from "@/components/AutoChangeLawyer/AutoChangeLawyer.vue";
import LawyerServicePayPopup from "@/components/LawyerServicePayPopup/index.vue";
import store from "@/store";

export default {
  name: "PayLawyerGuide",
  components: {
    LawyerServicePayPopup,
    AutoChangeLawyer,
    AppServiceInfoPopup,
    LawyerInfoCard,
    AppPlaceholder,
    LawyerGuideScore,
    UPopup,
    ImHistory,
    MyLitigateContainer,
    PayLawyerGuideCard,
    PayLawyerGuideTextarea,
    PayLawyerGuideAdvisory,
    LoginLayout,
    NoScroll,
  },
  data() {
    return {
      routeQuery: {}, // 路由参数
      /** 步骤条的名称 */
      stepName: ["1.支付提交", "2.律师接单", "3.一对一服务", "4.服务评价"],
      /** 显示已有订单弹窗 */
      orderVisible: false,
      /** 律师信息 */
      lawyerInfo: {},
      /** 点击按钮后需要传递给后端的信息 */
      saveAppointParams: {
        /** 律师id */
        // lawyerId: this.$route.params.id,
        lawyerId: "",
        /** 问题描述 */
        info: "",
        /** 服务编码 */
        serverCode: "",
        /** 问题类型 */
        typeLabel: "",
        /** 问题描述 */
        typeValue: 0,
        /** ! 用户是否授权同意自动换律师（0.否；1.是） */
        authChangeLawyer: 0,
      },
      selectPop: false /** 选择咨询类型弹窗布尔值*/,
      /** 服务说明弹窗 */
      serviceExplainVisible: false,
      /** 文章详情过来的参数 */
      articleParams: {
        /** 案源或者咨询数据来源 0 默认 1问律师 2打官司 3新版本文章详情 */
        createSource: undefined,
        /** 案源或者咨询数据来源业务id 为3时为文章id */
        createSourceBusinessId: undefined,
      },
    };
  },
  onLoad(data) {
    console.log("1v1:", data);
    this.routeQuery = data;
    this.saveAppointParams.lawyerId = data.lawyerId;
    this.saveAppointParams.serverCode = data.serverCode;
    // 新增加的来源
    if (data.createSource) {
      this.articleParams.createSource = data.createSource;
      this.articleParams.createSourceBusinessId = data.createSourceBusinessId;
    }
    this.getLawyerInfo(data.lawyerId);
    this.setSelectInfo(data);
  },
  computed: {
    lawyerOrderInfo() {
      return lawyerOrderInfo;
    },
    /** 少于10个字符则按钮为禁用状态 */
    buttonDisabled() {
      return (
        this.saveAppointParams.info?.trim?.().length < 5 ||
        !this.saveAppointParams.typeValue
      );
    },
    /** 参数中是否有serverCode */
    serverCode() {
      return this.routeQuery?.serverCode;
    },
    /** 默认服务 */
    defaultService() {
      return this.getServerInfo(this.serverCode);
    },
    /** 当前律师对应的服务列表 */
    serviceManageList() {
      return this.lawyerInfo?.serviceManageV2VoList || [];
    },
    /** 当前选择的服务 */
    selectService() {
      return this.getServerInfo(this.saveAppointParams.serverCode);
    },
    /** 当前选中服务的价格 */
    selectPrice() {
      return this.selectService?.servicePrice || 0;
    },
  },
  onReady() {
    uni.setNavigationBarColor({ backgroundColor: "#ffffff" });
  },
  // 分享
  onShareAppMessage() {
    return shareAppMessage(
      "/pages/sub/lawyer-home/pay-lawyer-guide/index?lawyerId=" +
        this.saveAppointParams.lawyerId
    );
  },
  methods: {
    /** 支付失败回调 */
    payFailCallback() {
      console.log("支付失败回调");
    },
    /** 通过serverCode找到对应的服务 */
    getServerInfo(serverCode) {
      return (
        this.serviceManageList?.find(
          (item) => Number(item?.serviceCode) === Number(serverCode)
        ) || {}
      );
    },
    toOrder() {
      this.orderVisible = false;
      setTimeout(() => {
        uni.navigateTo({
          url: "/pages/myorder/index",
        });
      }, 500);
    },
    /** 保存历史记录 */
    async saveHistory() {
      if (!this.saveAppointParams.info) return;

      return await saveParalegalData({
        info: this.saveAppointParams.info,
        type: 2,
      });
    },
    /** 选择历史记录后触发的事件 */
    historySelect(value) {
      this.saveAppointParams.info = value;
    },
    /** 如果是从快速咨询跳转过来，则需要将快速咨询选择的数据代入 */
    setSelectInfo(data) {
      if (data.type !== "1") return;
      this.saveAppointParams.info = paralegalHistory.getConsultMessage();
    },
    /** 获取当前律师信息 */
    getLawyerInfo() {
      oneLawyer({
        id: this.routeQuery.lawyerId,
      }).then((res) => {
        this.lawyerInfo = res.data;
        uni.setNavigationBarTitle({
          title: `咨询 ${res.data?.realName}律师`,
        });
      });
    },
    /** 选择咨询类型触发的回调 */
    selectType(val) {
      this.$set(this.saveAppointParams, "typeValue", val.typeValue);
      this.$set(this.saveAppointParams, "typeLabel", val.typeLabel);
      console.log("选择了咨询类型回调", this.saveAppointParams);
    },
    /** 选择咨询类型弹窗布尔值*/
    selectPopFunVal(val) {
      this.selectPop = val;
    },
    /** 验证参数是否合理 */
    validateParam() {
      const info = this.saveAppointParams.info?.trim?.();

      // 验证问题描述长度
      if (info.length === 0) {
        return uni.showToast({
          title: "请输入问题描述",
          duration: 2000,
          icon: "none",
        });
      } else if (info.length < 10) {
        return uni.showToast({
          title: "最少10个字哦~",
          duration: 2000,
          icon: "none",
        });
      }
      // 验证是否选择了问题类型
      if (!this.saveAppointParams.typeValue)
        return uni.showToast({
          title: "请选择咨询类型",
          duration: 2000,
          icon: "none",
        });

      // 验证是否选择了服务
      if (!this.saveAppointParams.serverCode)
        return uni.showToast({
          title: "请选择咨询方式",
          duration: 2000,
          icon: "none",
        });
      return false;
    },
    /** 点击支付后触发的操作 */
    payClick() {
      // 1.0.0 埋点不校验登录
      payLawyerGuideBuriedPoint();
      /* 先判断是否登录 */
      whetherToLogIn.call(this, async () => {
        if (this.validateParam()) return;

        // 按钮禁用不能点击
        if (this.buttonDisabled) return;

        /** 判断是否有订单 */
        const isThereAnOrder = async () => {
          const res = await postGetExistOrderByCurrentUser({
            serviceCode: this.saveAppointParams.serverCode,
            type: 2,
            lawyerId: this.saveAppointParams.lawyerId,
          });

          // 如果有订单，则弹出提示
          if (res.data) {
            this.orderVisible = true;
            return true;
          }

          return false;
        };

        try {
          if (await isThereAnOrder()) return;

          // 保存咨询信息
          caseSourceV2SaveAppoint({
            ...this.saveAppointParams,
            ...this.articleParams,
            createFrom: 1,
          }).then(async ({ data }) => {
            const { errorCode, errorData } = data;
            // 存储输入记录，并且获得后端返回的id
            const { id: synHistoryId } = await this.saveHistory();

            // 已经有付费且未完成一对一服务 需要提示并跳转到IM聊天窗口 返回格式见这个接口
            if (errorCode === 21101) {
              toImChatPage({
                conversationId: errorData?.imSessionId,
                lawyerId: errorData?.lawyerId,
                caseSourceId: errorData?.caseSourceV2Id,
              });

              return;
            }

            // 已经有未付费一对一服务 跳转到订单列表付费
            if (errorCode === 21102) {
              this.orderVisible = true;
              return;
            }

            // 储存数据到缓存中
            setPayLawyerGuideStorage(this.saveAppointParams);

            // 当前用户尚有创建好的服务但是没有创建订单
            if (errorCode === 21103) {
              const payFunc = () => {
                toConfirmOrder({
                  serviceCode: this.saveAppointParams.serverCode,
                  type: "2",
                  businessId: errorData?.id,
                  lawyerId: this.saveAppointParams.lawyerId,
                  synHistoryId,
                });
              };

              payFunc();

              // 将付费方法存入vuex
              store.commit("payState/SET_PAY_FAIL_POPUP_PAY", payFunc);

              return;
            }

            const payFunc = () => {
              toConfirmOrder({
                serviceCode: this.saveAppointParams.serverCode,
                type: "2",
                businessId: data.caseSourceServerV2Id,
                lawyerId: this.saveAppointParams.lawyerId,
                synHistoryId,
              });
            };

            payFunc();

            // 将付费方法存入vuex
            store.commit("payState/SET_PAY_FAIL_POPUP_PAY", payFunc);
          });
        } catch (e) {
          console.log(e);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.disabled-bc {
  background: rgba(0, 0, 0, 0.08);
  z-index: 999999;
  overflow: hidden;
  position: fixed;
  height: 100vh;
  width: 100%;
}

.description {
  padding: 16px 0;
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.im-history {
  margin-left: -16px;
  padding-top: 4px;
  margin-bottom: 4px;
  //padding-bottom: 8px;
}

.wrap {
  background: linear-gradient(180deg, #e1eafa 0%, rgba(229, 237, 255, 0) 100%)
    noc;
}

.lawyer-info {
  margin-bottom: 12px;
  overflow: hidden;
}

.pay-button {
  padding-top: 20px;
  padding-bottom: 12px;

  &__top {
    padding-top: 0;
  }

  .btn {
    width: 319px;
    height: 44px;
    background: #3887f5;
    border-radius: 22px 22px 22px 22px;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    line-height: 44px;
    text-align: center;

    &-disabled {
      background: #a0cffb;
    }
  }
}

.order {
  width: 311px;
  text-align: center;
  padding-bottom: 16px;

  &-title {
    font-size: 16px;
    font-weight: bold;
    margin-top: 24px;
    color: #222222;
  }

  &-info {
    font-size: 14px;
    font-weight: 400;
    color: #222222;
    margin-top: 12px;
    margin-bottom: 24px;
  }

  &-button {
    display: flex;
    padding: 0 24px;

    .btn {
      border: 1px solid #3887f5;
      background: #ffffff;
      color: #3887f5;
      font-size: 14px;
      width: 120px;
      height: 28px;
      text-align: center;
      line-height: 28px;
      border-radius: 68px 68px 68px 68px;

      &:nth-child(2) {
        background: #3887f5;
        color: #ffffff;
        margin-left: 18px;
      }
    }
  }
}
</style>
