<template>
  <div>
    <u-form-item
      :borderBottom="borderBottom"
      :label="label"
      :labelWidth="labelWidth"
      :leftIcon="leftIcon"
      :leftIconStyle="leftIconStyle"
      :prop="prop"
      :required="required"
      :rightIcon="rightIcon"
      @click="$emit('click')"
    >
      <slot name="error">
        <template #error />
      </slot>
    </u-form-item>
  </div>
</template>

<script>
import UFormItem from "@/uview-ui/components/u-form-item/u-form-item.vue";
import props from "@/uview-ui/components/u-form-item/props";

export default {
  name: "AppFormItem",
  components: { UFormItem },
  mixins: [props],
};
</script>

<style lang="scss" scoped></style>
