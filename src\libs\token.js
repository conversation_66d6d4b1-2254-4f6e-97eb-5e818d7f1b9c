export const userToken = "lawMSeoToken";
export const newUser = "lawMNewUser";
/** 支付极端情况下，需要返回到之前的界面 */
export const returnRouter = "returnRouter";


export const getNewUserStateStorage = () => {
  const item = uni.getStorageSync(newUser) || "";
  console.log(item, "getNewUserStateStorage");
  return item ? item === "true" : "";
};

export const clearNewUserStateStorage = () => {
  return uni.removeStorageSync(newUser) || "";
};

export const setUserTokenStorage = (value) => {
  return uni.setStorageSync(userToken, value) || "";
};

/**
 * 支付极端情况下，需要返回到之前的界面路由
 * @param {string} router
 */
export const setReturnRouterStorage = (router) => {
  uni.setStorageSync(returnRouter, router);
};

export const getUserTokenStorage = () => {
  return uni.getStorageSync(userToken) || "";
};

export const clearUserTokenStorage = () => {
  return uni.removeStorageSync(userToken) || "";
};

export const checkAppLoginAndGetCode = () => {
  return new Promise(async (resolve) => {
    let { provider } = await uni.getProvider({
      service: "oauth",
    });

    uni.login({
      provider: provider[0],
      success: (loginRes) => {
        console.log(loginRes, "loginRes");
        uni.setStorageSync("loginCode", loginRes.code);

        resolve(loginRes.code);
      },
    });
  });
};



export function getImToken() {
  return uni.getStorageSync("imToken") || "";
}

export function setImToken(token) {
  return uni.setStorageSync("imToken", token);
}

export function removeImToken() {
  return uni.removeStorageSync("imToken") || "";
}


/* im 缓存配置*/
export function getImCacheConfig() {
  return uni.getStorageSync("ImCacheConfig") || {};
}
/* 通过id去查找*/
export function getImCacheConfigToId(id) {
  const config = getImCacheConfig();
  return config[id] || {};
}

export function setImCacheConfig(id, config = {}) {
  const configToId = getImCacheConfigToId(id);
  return uni.setStorageSync("ImCacheConfig", {
    ...getImCacheConfig(),
    [id]: {
      ...configToId,
      ...config
    }
  });
}

export function removeImCacheConfig() {
  return uni.removeStorageSync("ImCacheConfig");
}

/* tab页面得参数 */
export function getTabParams(key) {
  return uni.getStorageSync("tabParams" + key) || {};
}

export function setTabParams(key, params) {
  return uni.setStorageSync("tabParams" + key, params);
}