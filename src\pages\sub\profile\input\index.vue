<template>
  <login-layout>
    <div class="app-form">
      <img
        alt=""
        class="bg-image"
        src="img/bg.png"
      >
      <u-form
        ref="uForm"
        :model="params"
        :rules="rules"
        labelPosition="left"
        labelWidth="78"
      >
        <div class="top-content content !pt-0">
          <u-form-item
            borderBottom
            label=" "
            labelWidth="0"
            prop="imgUrl"
          >
            <profile-input-header v-model="params.imgUrl" />
          </u-form-item>
          <u-form-item
            borderBottom
            label="姓名"
            prop="realName"
          >
            <u-input
              v-model="params.realName"
              border="none"
              clearable
              maxlength="10"
              placeholder="请输入真实姓名"
            />
          </u-form-item>
          <u-form-item
            borderBottom
            label="就职单位"
            prop="lawyerOffice"
          >
            <u-input
              v-model="params.lawyerOffice"
              border="none"
              clearable
              maxlength="20"
              placeholder="请输入律所名称"
            />
          </u-form-item>
          <u-form-item
            borderBottom
            label="所在地区"
            prop="workCity"
          >
            <app-area
              :value="params.workCity"
              @confirm="areaChange"
            />
          </u-form-item>
          <u-form-item
            borderBottom
            label="发证日期"
            prop="workStartTime"
          >
            <app-select-time v-model="params.workStartTime" />
          </u-form-item>
          <u-form-item
            borderBottom
            label="执业证号"
            prop="certificateId"
          >
            <u-input
              v-model="params.certificateId"
              border="none"
              clearable
              maxlength="17"
              placeholder="请输入执业证号"
            />
          </u-form-item>
          <!-- 擅长领域 -->
          <div>
            <u-form-item
              label="擅长领域"
              prop="workFields"
            >
              <app-areas v-model="params.workFields" />
            </u-form-item>
            <!-- 显示选择的擅长领域的内容 -->
            <ul
              v-if="params.workFields.length > 0"
              class="workFields"
            >
              <li
                v-for="item in params.workFields"
                :key="item.workFieldCode"
                class="workFields-item"
              >
                <div>{{ item.workFieldName }}</div>
                <i
                  class="iconfont icon-a-cuowuguanbi"
                  @click="deleteWorkFieldsItem(item)"
                />
              </li>
            </ul>
          </div>
        </div>
        <div class="bottom-content content">
          <div class="title">
            执业信息
          </div>
          <u-form-item
            borderBottom
            label="执业证照片"
            labelWidth="100"
            prop="certificatePic"
          >
            <div @click="toPracticeLicense">
              <app-field
                v-if="params.certificatePic"
                arrow
                color="#3887F5"
                value="已上传，点击查看"
              />
              <div
                v-else
                class="flex"
              >
                <div class="text-c8c8c8 font14">
                  上传律师执业证照片，要求照片清晰可辨认
                </div>
                <img
                  alt=""
                  class="bottom-content__icon"
                  src="img/10.png"
                >
              </div>
            </div>
          </u-form-item>
          <u-form-item
            label="身份认证"
            labelWidth="100"
            prop="idCard"
          >
            <div @click="toOcrResult">
              <app-field
                v-if="params.idCard"
                arrow
                color="#3887F5"
                value="已上传，点击查看"
              />
              <div
                v-else
                class="flex"
              >
                <div class="text-c8c8c8 font14">
                  进行实名认证，确保本人操作账户安全
                </div>
                <img
                  alt=""
                  class="bottom-content__icon"
                  src="img/10.png"
                >
              </div>
            </div>
          </u-form-item>
        </div>
      </u-form>
      <!-- 底部按钮 -->
      <app-bottom>
        <div>
          <profile-swiper-tips />
        </div>
        <div class="flex justify-between px-[16px]">
          <div
            class="w-[128px] h-[44px] rounded-[46px] border-[1px] border-solid border-[#CCCCCC] text-[16px] text-[#666666] flex items-center justify-center"
            @click="examplePopup = true"
          >
            查看填写示例
          </div>
          <div
            class="w-[203px] h-[44px] bg-[#3887F5] rounded-[68px] font-bold text-[16px] text-[#FFFFFF] flex items-center justify-center"
            @click="submit"
          >
            申请为认证律师
          </div>
        </div>
      </app-bottom>
      <profile-input-example
        :show="examplePopup"
        @cancel="examplePopup = false"
      />
    </div>
  </login-layout>
</template>

<script>
import UForm from "@/uview-ui/components/u-form/u-form.vue";
import UFormItem from "@/uview-ui/components/u-form-item/u-form-item.vue";
import AppBottom from "@/components/AppComponents/AppBottom/index.vue";
import ProfileInputHeader from "@/pages/sub/profile/input/components/ProfileInputHeader.vue";
import {
  turnToIdentifyResultPage,
  turnToLawyerAuthingPage,
  turnToLawyerAuthResultPage,
  turnToPracticePage,
} from "@/libs/turnPages";
import ProfileInputExample from "@/pages/sub/profile/input/components/ProfileInputExample.vue";
import store from "@/store";
import AppArea from "@/components/AppComponents/AppArea/index.vue";
import AppSelectTime from "@/components/AppComponents/AppSelectTime/index.vue";
import AppAreas from "@/components/AppComponents/AppAreas/index.vue";
import AppField from "@/components/AppComponents/AppField/index.vue";
import { lawyerCertCommit } from "@/api/lawyerProfile";
import { shareAppMessage, shareTimeline, whetherToLogIn } from "@/libs/tools";
import { mapGetters } from "vuex";
import LoginLayout from "@/components/login/LoginLayout.vue";
import { appValidator } from "@/libs/validator";
import { lawyerGetTaskByRuleTypeCode } from "@/api/lawLogin";
import { amountFilter, amountIsOmittedFilter } from "@/libs/filter";
import ProfileSwiperTips from "@/pages/sub/profile/input/components/ProfileSwiperTips.vue";

export default {
  name: "ProfileInput",
  components: {
    ProfileSwiperTips,
    LoginLayout,
    AppField,
    AppAreas,
    AppSelectTime,
    AppArea,
    ProfileInputExample,
    ProfileInputHeader,
    AppBottom,
    UFormItem,
    UForm,
  },
  computed: {
    params() {
      return store.state.lawyerVerify.lawyerVerify;
    },
    ...mapGetters({
      userInfo: "user/getUserInfo",
    }),
  },
  watch: {
    userInfo: {
      // 这里使用watch的原因是可能会直接到当前页面，所以律师信息是异步获取的
      handler(val) {
        const role = val.role;

        // 如果不为-1，直接用默认值
        if (role && Number(role) !== -1) this.params.role = val.role;
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    // 强制登陆
    whetherToLogIn(() => {
      console.log("登陆成功");

      turnToLawyerAuthResultPage({
        isAuth: true,
      });

      this.getRewardCoin();
    });
  },
  onShareAppMessage() {
    return shareAppMessage({
      title:
        "邀请您入驻法临律师端，认证通过可得35法临币免费抢案源，点击完成认证",
      imageUrl: require("@/img/share/<EMAIL>"),
    });
  },
  onShareTimeline() {
    return shareTimeline({
      title:
        "邀请您入驻法临律师端，认证通过可得35法临币免费抢案源，点击完成认证",
      imageUrl: require("@/img/share/<EMAIL>"),
    });
  },
  onReady() {
    // 如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
    this.$refs.uForm.setRules(this.rules);
  },
  data() {
    return {
      /** 示例弹窗 */
      examplePopup: false,
      rules: {
        imgUrl: [
          {
            required: true,
            message: "请上传真实头像",
            trigger: "change",
          },
        ],
        realName: [
          {
            required: true,
            message: "请填写真实姓名",
            trigger: "change",
          },
        ],
        lawyerOffice: [
          {
            required: true,
            message: "请填写就职单位",
            trigger: "change",
          },
        ],
        workCity: [
          {
            validator: () => {
              return !!this.params.workCity && !!this.params.cityName;
            },
            message: "请选择所在地区",
            trigger: "blur",
          },
        ],
        workStartTime: [
          {
            validator: () => {
              return !!this.params.workStartTime;
            },
            message: "请选择发证日期",
            trigger: "blur",
          },
        ],
        certificateId: [
          {
            required: true,
            message: "请填写执业证号",
            trigger: "blur",
          },
          {
            type: "number",
            min: 17,
            max: 17,
            message: "执业证号请输入17位整数",
            trigger: "blur",
          },
        ],
        workFields: [
          {
            validator: () => {
              return this.params.workFields?.length > 0;
            },
            message: "请选择擅长领域",
            trigger: "blur",
          },
        ],
        certificatePic: [
          {
            required: true,
            message: "请上传执业证照片",
            trigger: "blur",
          },
        ],
        idCard: [
          {
            validator: () => {
              return (
                !!this.params.idCard ||
                !!this.params.idCardBackPic ||
                !!this.params.idCardPosPic
              );
            },
            message: "请进行身份认证",
            trigger: "blur",
          },
        ],
      },
      /** 法临币奖励 */
      awardAmount: 0,
    };
  },
  methods: {
    amountFilter,
    amountIsOmittedFilter,
    /** 数据提交 */
    submit() {
      appValidator(this.params, this.rules)
        .then(() => {
          lawyerCertCommit(this.params).then(() => {
            this.$toast("提交成功").then(() => {
              this.$store.commit("lawyerVerify/RESET_STORE");
              turnToLawyerAuthingPage({
                isAuth: true,
              });
            });
          });
        })
        .catch((errors) => {
          console.log(errors);
        });
    },
    /** 跳转到识别结果页面 */
    toOcrResult() {
      turnToIdentifyResultPage();
    },
    /** 跳转到执业照界面 */
    toPracticeLicense() {
      turnToPracticePage();
    },
    /** 地区选择 */
    areaChange(data) {
      this.params.workCity = data.workCity;
      this.params.cityName = data.cityName;
    },
    /** 点击擅长领域item删除按钮 */
    deleteWorkFieldsItem(item) {
      this.params.workFields = this.params.workFields.filter(
        (i) => i.workFieldCode !== item.workFieldCode
      );
    },
    /** 获取奖励的法临币数量 */
    getRewardCoin() {
      lawyerGetTaskByRuleTypeCode({
        ruleTypeCode: "TASK_WC_RZ",
      }).then(({ data = {} }) => {
        this.awardAmount = data.awardAmount || 0;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/formStyles";

.bg-image {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 375px;
  height: 145px;
}

.top-content {
  margin-top: 76px;
}

.bottom-content {
  margin-top: 12px;
  margin-bottom: 24px;

  &__icon {
    flex-shrink: 0;
    display: block;
    width: 30px;
    height: 30px;
    margin-left: 15px;
  }
}

.content {
  width: 351px;
  background: #ffffff;
  border-radius: 12px;
  opacity: 1;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
  padding: 12px 16px 0 16px;
}

.title {
  font-size: 16px;
  font-weight: bold;
  color: #111111;
  margin-bottom: 12px;
}

.workFields {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 12px 12px;
  padding-bottom: 12px;

  &-item {
    height: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: 400;
    color: #3887f5;
    background: #edf2fe;
    border-radius: 12px;
    opacity: 1;

    .iconfont {
      font-size: 12px;
      margin-left: 4px;
    }
  }
}
</style>
