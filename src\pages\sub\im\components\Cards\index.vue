<template>
  <div class="cards">
    <text-card
      v-if="componentName==='textCard'"
      :theme="theme"
      :data="data"
    />
    <system-card
      v-if="componentName==='systemCard'"
      :theme="theme"
      :data="data"
    />
    <image-card
      v-if="componentName==='imageCard'"
      :theme="theme"
      :data="data"
    />
    <service-card
      v-if="componentName===imCardMap[imMessageType.SERVER_ONE_TO_ONE]"
      :theme="theme"
      :data="data"
    />
    <sensitive-word-tip-card v-if="componentName===imCardMap[imMessageType.SERVER_QA_AUTO_SEND_CARD]" />
    <contact-info-lawyer-card
      v-if="componentName===imCardMap[imMessageType.CONTACT_INFO_LAWYER]"
      :theme="theme"
      :data="data"
    />
    <lawyer-auto-reply-card
      v-if="componentName===imCardMap.lawyer_auto_reply"
      :theme="theme"
      :data="data"
    />
    <user-phone-card
      v-if="componentName===imCardMap[imMessageType.SERVER_CONTACT_PHONE]"
      :theme="theme"
      :data="data"
    />
    <case-source-evaluation-card
      v-if="componentName===imCardMap.server_lawyer_comment"
      :data="data"
    />
    <submissions-card
      v-if="componentName===imCardMap.server_lawyer_idea"
      :theme="theme"
      :data="data"
    />
    <refund-tip-card
      v-if="(componentName===imCardMap[imMessageType.REFUND_TIP])||(componentName===imCardMap.server_change_lawyer_tips)"
      :data="data"
    />
    <user-questions-card
      v-if="componentName===imCardMap.server_QA_asked"
      :theme="theme"
      :data="data"
    />
    <user-reviews
      v-if="componentName===imCardMap[imMessageType.SERVER_EVALUATE]"
      :data="data"
    />
    <request-exchange-contacts-card
      v-if="componentName===imCardMap.EXCHANGE_MESSAGE"
      :theme="theme"
      :data="data"
    />
    <exchange-contacts-card
      v-if="componentName===imCardMap.EXCHANGE_MESSAGE_INFO"
      :theme="theme"
      :data="data"
    />
  </div>
</template>

<script>
import UserPhoneCard from "@/pages/sub/im/components/Cards/UserPhoneCard/index.vue";
import CaseSourceEvaluationCard from "@/pages/sub/im/components/Cards/CaseSourceEvaluationCard/index.vue";
import SubmissionsCard from "@/pages/sub/im/components/Cards/SubmissionsCard/index.vue";
import TextCard from "@/pages/sub/im/components/Cards/TextCard/index.vue";
import cardProps from "@/pages/sub/im/mixins/card-props.js";
import SystemCard from "@/pages/sub/im/components/Cards/SystemCard/index.vue";
import ImageCard from "@/pages/sub/im/components/Cards/ImageCard/index.vue";
import ServiceCard from "@/pages/sub/im/components/Cards/ServiceCard/index.vue";
import { imCardMap, imMessageType } from "@/pages/sub/im/enum/imEnum.js";
import SensitiveWordTipCard from "@/pages/sub/im/components/Cards/SensitiveWordTipCard/index.vue";
import ContactInfoLawyerCard from "@/pages/sub/im/components/Cards/ContactInfoLawyerCard/index.vue";
import LawyerAutoReplyCard from "@/pages/sub/im/components/Cards/LawyerAutoReplyCard/index.vue";
import RefundTipCard from "@/pages/sub/im/components/Cards/RefundTipCard/index.vue";
import UserQuestionsCard from "@/pages/sub/im/components/Cards/UserQuestionsCard/index.vue";
import UserReviews from "@/pages/sub/im/components/Cards/UserReviews/index.vue";
import ExchangeContactsCard from "@/pages/sub/im/components/Cards/ExchangeContactsCard/index.vue";
import RequestExchangeContactsCard from "@/pages/sub/im/components/Cards/RequestExchangeContactsCard/index.vue";

export default {
  name: "Cards",
  components: {
    RequestExchangeContactsCard,
    ExchangeContactsCard,
    UserReviews,
    UserQuestionsCard,
    RefundTipCard,
    LawyerAutoReplyCard,
    ContactInfoLawyerCard,
    SensitiveWordTipCard,
    ServiceCard, ImageCard, SystemCard, SubmissionsCard, CaseSourceEvaluationCard, UserPhoneCard, TextCard },
  mixins: [cardProps],
  props: {
    componentName: {
      type: String,
      default: ""
    }
  },
  computed: {
    imMessageType() {
      return imMessageType;
    },
    imCardMap() {
      return imCardMap;
    }
  }
};
</script>

<style scoped lang="scss">
.cards{
  padding-bottom: 16px;

}
</style>
