<template>
  <app-popup
    :safeAreaInsetBottom="false"
    :show="getShow"
    mode="center"
  >
    <div class="w-[311px] bg-[#FFFFFF] rounded-[16px]">
      <p class="font-bold text-[16px] text-[#333333] pt-[24px] text-align-center">
        发布规则
      </p>
      <div class="px-[16px] text-[14px] text-[#333333] pt-[12px]  leading-[24px]  pb-[24px]">
        <p>1. 完成认证：完成认证后，可创建个人社群 </p>
        <p>2. 每日上限：每日最多上传<span class="text-[#3887F5]">2个</span>社群二维码 </p>
        <p>3. 群码有效期：群二维码<span class="text-[#3887F5]">7天</span>有效，过期需重新上传审核，否则将下架 </p>
        <p>4. 审核机制：提交建群申请-审核通过-外部展示社群二维码</p>
      </div>
      <div class="px-[24px] pb-[16px]">
        <div
          class="h-[36px] bg-[#3887F5] rounded-[68px] font-bold text-[14px] text-[#FFFFFF] flex items-center justify-center"
          @click="getShow=false"
        >
          知道了
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";

export default {
  name: "PublishingRulesPopup",
  components: { AppPopup },
  props: {
    value: {
      type: Boolean,
      default: false,
    },

  },
  computed: {
    getShow: {
      get(){
        return this.value;
      },
      set(val){
        this.$emit("input", val);
      }
    }
  }
};
</script>

<style scoped lang="scss">

</style>