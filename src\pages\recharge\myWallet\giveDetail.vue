<template>
  <div class="giveDetail">
    <div class="pd-16">
      <div class="card-wr flex flex-space-center flex-align-center">
        <img
          mode="widthFix"
          class="bg"
          src="@/pages/recharge/img/bg@2x(3).png"
          alt=""
        >
        <div class="item">
          <div class="title">
            赠送总额
          </div>
          <div class="num">
            {{ getShowPrice(wallet.giftAccountBalance) }}
          </div>
        </div>
        <div class="item">
          <div class="title">
            即将失效
          </div>
          <div class="num color-text">
            {{ getShowPrice(wallet.temporaryAccountBalance) }}
          </div>
        </div>
      </div>
    </div>
    <div class="title-line flex-align-center flex-space-center flex">
      <div class="line" />
      <div class="text">
        法临币有效期明细
      </div>
      <div class="line" />
    </div>
    <div class="pd-16">
      <div class="list-wr">
        <div
          v-for="item in list"
          :key="item.id"
          class="list-item"
        >
          <div class="line flex flex-space-between">
            <div class="label">
              {{ item.title }}
            </div>
            <div class="num up">
              +{{ priceNumber(item.giveTotal) }}
            </div>
          </div>
          <div class="date flex flex-space-between">
            <span>有效期至：{{ item.giveValidTime }}</span>
            <span>余额：{{ priceNumber(item.surplusTotal) }}</span>
          </div>
        </div>
      </div>
    </div>
    <u-safe-bottom />
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { temporaryAccount, walletBalance } from "@/api/recharge";
import { priceNumber } from "@/libs/tool";

export default {
  components: { USafeBottom },
  data() {
    return {
      wallet: {},
      page: {
        currentPage: 1,
        total: 0,
      },
      list: [],
    };
  },
  onLoad() {
    walletBalance().then(({ data }) => {
      this.wallet = data;
    });
    this.getList();
  },
  onReachBottom() {
    this.scrollToLower();
  },
  methods: {
    priceNumber,
    getList() {
      temporaryAccount({
        ...this.page,
      }).then(({ data = {} }) => {
        const records = data.records || [];
        this.list = [...this.list, ...records];
        this.page.total = data.total;
      });
    },
    scrollToLower() {
      if (this.list.length < this.page.total) {
        this.page.currentPage++;
        this.getList();
      }
    },
    getShowPrice(price) {
      // 分转万
      const res = price / (10000 * 100);
      console.log(res, 111111);
      return res > 1 ? res.toFixed(2) + "万" : priceNumber(price);
    }
  },
};
</script>

<style lang="scss" scoped>
.giveDetail {
  min-height: 100vh;
  background: #fff;
  .pd-16{
    padding: 0 16px;
  }
  .card-wr{
    position: relative;
    height: 106px;
    border-radius: 12px;
    //padding: 0 61px;
    .bg{
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 1;
    }
    &:after{
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 40px;
      opacity: 0.2;
      transform: translate(-50%,-50%);
      border-left: 1px dashed #fff;
      z-index: 3;

    }
    .item{
      flex: 1;
      position: relative;
      z-index: 1;
      text-align: center;
      .title{
        font-size: 12px;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.65);
      }
      .num{
        font-size: 28px;
        font-weight: bold;
        color: #FFFFFF;
        line-height: 36px;
        margin-top: 4px;
        &.color-text{
          color: #FFC352;
        }
      }
    }
  }
  .title-line{
    margin-top: 24px;
    .text{
      flex:0 0 112px;
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      padding: 0 12px;
      line-height: 32px;
      text-align: center;
    }
    .line{
      flex: 1;
      border-bottom: 0.5px solid #EEEEEE;
      border-radius: 0px 0px 0px 0px;
    }
  }
  .list-wr{
    background: #FFFFFF;
    padding: 0 16px;
    .list-item{
      padding: 16px 0 19px;
      border-bottom: 0.5px solid #eee;
      .label{
        font-size: 14px;
        font-weight: 500;
        color: #333333;
      }
      .num{
        font-size: 20px;
        font-weight: bold;
        color: #22BF7E;
        &.up{
          color: #22BF7E;
        }
        &.down{
          color: #EB4738;
        }
      }
      .date{
        font-size: 12px;
        font-weight: 400;
        color: #999999;
        margin-top: 8px;
      }
    }
  }
}
</style>
