<template>
  <div class="relative bg-[linear-gradient(_179deg,_#DAEAFF_0%,_#FFFFFF_100%)] min-h-[100vh] pb-[80px]">
    <img
      class="absolute z-[0] right-0 top-0 w-full"
      mode="widthFix"
      src="@/pages/sub/community/imgs/Frame1556238546.png"
      alt=""
    >
    
    <u-sticky>
      <app-header>
        <p class="flex items-center justify-center font-bold text-[17px] h-full text-[#333333]">
          详情
        </p>
      </app-header>
    </u-sticky>
    <div class="relative z-[2] m-[12px]  p-[16px]  bg-[#FFFFFF] [box-shadow:0px_0px_4px_0px_rgba(0,0,0,0.04)] rounded-[12px]">
      <div class=" flex">
        <img
          mode="aspectFill"
          class="w-[48px] h-[48px] rounded-[4px] overflow-hidden"
          src=""
          alt=""
        >
        <div class="pl-[8px]">
          <p class="font-bold text-[16px] text-[#333333]">
            上海地区律师交流区
          </p>
          <div class="flex pt-[8px]">
            <p class="px-[8px] py-[3px] bg-[#F7F6F6] rounded-[4px] text-[12px] text-[#666666] ml-[4px] first-of-type:ml-[0] flex items-center">
              <i class="iconfont icon-dingwei !text-[12px] !text-[#666666]" />
              成都
            </p>
            <p class="px-[8px] py-[3px] bg-[#F7F6F6] rounded-[4px] text-[12px] text-[#666666] ml-[4px] first-of-type:ml-[0] flex items-center">
              12000人
            </p>
          </div>
          <div class="flex pt-[12px]">
            <p class="px-[4px] py-[3px] bg-[#F7F6F6] rounded-[4px] text-[12px] text-[#3887F5] ml-[4px] first-of-type:ml-[0]">
              #经济纠纷群
            </p>
            <p class="px-[4px] py-[3px] bg-[#F7F6F6] rounded-[4px] text-[12px] text-[#3887F5] ml-[4px] first-of-type:ml-[0]">
              #经济纠纷群
            </p>
          </div>
        </div>
      </div>
      <div
        class="mt-[12px] bg-[#F7F6F6] overflow-hidden rounded-[8px] p-[12px] text-[12px] text-[#999999]  leading-[20px]"
      >
        <img
          class="w-[44px] h-[16px] pr-[8px] mb-[-3px]"
          src="@/pages/sub/community/imgs/<EMAIL>"
          alt=""
        >
        这是群公告群公告这是群公告群公告这是群公告群公告这 是这是群公告这是群公告这是群公告
      </div>
    </div>
    <certification-progress :placeholderStyle="placeholderStyle">
      <div class="relative z-[2]">
        <div class="mx-[12px] flex flex-col items-center bg-[#FFFFFF]  pb-[60px] rounded-[12px] overflow-hidden">
          <div class="relative">
            <img
              class="w-[351px] h-[470px] block filter-blur-[10px]"
              src="https://oss.imlaw.cn/test/image/2025/08/04/f642b35b6ff7405fb9efde4c3908ea24.jpeg"
              alt=""
            >
            <div
              class="absolute absolute-center center w-[123px] h-[28px] bg-[linear-gradient(_116deg,_#71B5FF_0%,_#2676E4_100%)] rounded-[50px] text-[13px] text-[#FFFFFF]"
            >
              查看二维码进群
            </div>
          </div>
          <p class="text-[12px] text-[#999999] pt-[16px]">
            谨防诈骗，平台不承担通过群聊产生的任何责任
          </p>
        </div>
        <div
          class="fixed bottom-0 left-0 right-0 px-[8px] py-[16px] bg-[#FFFFFF] [box-shadow:0px_-3px_10px_0px_rgba(0,0,0,0.08)] rounded-tl-[16px] rounded-br-none rounded-tr-[16px] rounded-bl-none"
        >
          <div
            class="font-bold text-[16px] text-[#FFFFFF] flex justify-center items-center h-[46px] bg-[#3887F5] rounded-[68px]"
          >
            <i class="iconfont icon-a-fenxiang1 pr-[4px] !text-[16px] !text-[#FFFFFF] " />
            分享到群聊
          </div>
          <u-safe-bottom />
        </div>
      </div>
    </certification-progress>
    <the-number-is-insufficient v-model="theNumberIsInsufficientState" />
  </div>
</template>

<script>
import AppHeader from "@/components/AppHeader/index.vue";
import TheNumberIsInsufficient from "@/pages/sub/community/components/TheNumberIsInsufficient/index.vue";
import CertificationProgress from "@/components/CertificationProgress/index.vue";
import { pxToRpx } from "@/libs/tools";

export default {
  name: "CommunityDetails",
  components: { CertificationProgress, TheNumberIsInsufficient, AppHeader },
  data() {
    return {
      theNumberIsInsufficientState: false,
      placeholderStyle: {
        paddingTop: pxToRpx(36)
      }
    };
  },
};
</script>

<style scoped lang="scss">

</style>