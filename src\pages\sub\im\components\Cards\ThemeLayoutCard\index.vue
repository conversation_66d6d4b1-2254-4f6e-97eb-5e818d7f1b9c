<template>
  <div
    class="theme-layout-card"
    :class="'theme-layout-card-'+theme"
  >
    <slot />
  </div>
</template>

<script>
import cardProps from "@/pages/sub/im/mixins/card-props.js";

export default {
  name: "ThemeLayoutCard",
  mixins: [cardProps],
};
</script>

<style scoped lang="scss">
.theme-layout-card{
  max-width: 255px;
  padding: 12px;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  box-sizing: border-box;
  &-blue{
    background: #E1EAFA;
    border-radius: 8px 0px 8px 8px;
  }
  &-white{
    background: #FFFFFF;
    border-radius: 0px 8px 8px 8px;
  }
}
</style>
