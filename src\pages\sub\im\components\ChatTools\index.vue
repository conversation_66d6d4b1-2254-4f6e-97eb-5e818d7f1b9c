<template>
  <div
    class="chat-tools flex"
    :class="{active:show}"
  >
    <div
      v-if="!getConversationInfo.isLawyerHelp&&!isCaseInfoItTheSourceOfTheCase"
      class="chat-tool-item"
      @click.stop="$emit('handleServicePopUp')"
    >
      <img
        class="tool-img"
        src="../../imgs/tool-1.png"
        alt=""
      >
      <p class="tool-name">
        付费服务
      </p>
    </div>

    <div
      class="chat-tool-item"
      @click.stop="$emit('sendPrivateImg')"
    >
      <img
        class="tool-img"
        src="../../imgs/tool-2.png"
        alt=""
      >
      <p class="tool-name">
        图片
      </p>
    </div>

    <div
      v-if="!getConversationInfo.isLawyerHelp&&!isCaseInfoQuestionClosely"
      class="chat-tool-item"
      @click.stop="$emit('handlePhonePopUp')"
    >
      <img
        class="tool-img"
        src="../../imgs/tool-3.png"
        alt=""
      >
      <p class="tool-name">
        电话号码
      </p>
    </div>
    <div class="chat-tool-item" />
  </div>
</template>

<script>

import { caseInfoStateProps, conversationInfoStateProps } from "@/pages/sub/im/mixins/case-info-state.js";

export default {
  name: "ChatTools",
  mixins: [caseInfoStateProps, conversationInfoStateProps],
  props: {
    show: {
      type: Boolean,
      default: false
    },
  },
};
</script>

<style scoped lang="scss">

.chat-tools {
  height: 0;
  overflow: hidden;
  transition: height 0.2s;

  &.active {
    height: 107px;
  }

  .chat-tool-item {
    padding-left: 31px;
    &:first-child {
      padding-left: 24px;
    }
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .tool-img {
      width: 58px;
      height: 58px;
    }

    .tool-name {
      padding-top: 8px;
      font-size: 12px;
      font-weight: 400;
      color: #666666;
    }
  }
}
</style>
