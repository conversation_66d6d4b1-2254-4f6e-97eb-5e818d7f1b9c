<template>
  <login-layout>
    <div class="chat-room-box ">
      <!--悬浮顶部    -->
      <div class="header-container">
        <chat-room-header />
      </div>
      <!--悬浮顶部    -->

      <!--聊天内容    -->
      <div class="chat-room-container flex-1">
        <chat-room-top />
        <div
          v-for="i in messageHistoryList"
          :id="'msg_'+i.msgId"
          :key="i.msgId"
        >
          <p
            v-if="i.intervalTime"
            class="date"
          >
            {{ i.intervalTime }}
          </p>
          <message-card
            v-if="i.noAvatar"
            :data="i"
            position="center"
          />
          <message-card
            v-else-if="i.from&&i.from===getCurrentToken"
            :data="i"
            position="right"
            :avatar="currentUserInfo.img"
            @loadComponent="loadComponent"
          />
          <message-card
            v-else-if="i.from&&i.from!==getCurrentToken"
            :data="i"
            position="left"
            :avatar="conversationInfo.userInfos[i.from].img"
          />
          <p v-else>
            {{ i }}
          </p>
        </div>
        <u-safe-bottom />
      </div>
      <!--        聊天内容-->

      <!--底部输入框    -->
      <div
        v-if="inputContainerState"
        class="operate-container"
      >
        <tools-for-lawyers
          v-if="!isLawyerHelp"
          :submissionsSendState="submissionsSendState"
          @handleServeLater="handleServeLater"
          @handlePhonePopUp="handlePhonePopUp"
          @handleServicePopUp="servicePopUpState=true"
        />
        <div class="input-box  flex flex-align-center">
          <div
            class="btn-box"
            @click.stop="handlePhrasesClick"
          >
            常用语
          </div>
          <div class="textarea flex-1">
            <u--input
              v-model="msg"
              :showConfirmBar="false"
              placeholder="请输入内容"
              autoHeight
              :cursorSpacing="cursorSpacing"
            />
          </div>

          <div class="icon-box flex flex-align-center">
            <i
              class="iconfont icon-tianjia"
              @click.stop="handleChatToolClick"
            />
            <p
              class="px-[16px] py-[6px] ml-[12px] bg-[#3887F5] rounded-[68px] text-[14px] text-[#FFFFFF] text-center"
              @click.stop="sendPrivateText"
            >
              发送
            </p>
          </div>
        </div>
        <chat-tools
          :show.sync="chatToolsState"
          @handlePhonePopUp="handlePhonePopUp"
          @sendPrivateImg="sendPrivateImg"
          @handleServicePopUp="servicePopUpState=true"
        />
        <phrases-tools
          :show.sync="phrasesToolsState"
          @sendPhrases="sendPhrases"
        />
        <u-safe-bottom />
      </div>
      <u-safe-bottom />
      <!--底部输入框    结束-->
      <service-pop-up
        :show.sync="servicePopUpState"
        @sendServe="sendServerSend"
      />
      <!--发送手机号 提示框    -->
      <div
        v-if="!isCaseInfoQuestionClosely"
        class="phone-modal"
      >
        <u-modal
          cancelColor="#000000"
          confirmColor="#3887F5"
          width="622rpx"
          confirmText="同意"
          cancelText="拒绝"
          showCancelButton
          showConfirmButton
          lineBorderColor="#eeeeee"
          :show="phoneModalState"
          @confirm="sendPhone"
          @cancel="phoneModalState=false"
        >
          <div class="phone-modal-content">
            <p class="phone-modal-title">
              主动发送您的电话号码
            </p>
            <p class="phone-modal-text">
              同意后平台将把您的电话展示给用户, 双方可电话联系; 若拒绝则双方在线图文沟通
            </p>
          </div>
        </u-modal>
      </div>
    </div>
  </login-layout>
</template>

<script>
import UInput from "@/uview-ui/components/u-input/u-input.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import ChatTools from "@/pages/sub/im/components/ChatTools/index.vue";
import PhrasesTools from "@/pages/sub/im/components/PhrasesTools/index.vue";
import ToolsForLawyers from "@/pages/sub/im/components/ToolsForLawyers/index.vue";
import MessageCard from "@/pages/sub/im/components/MessageCard/index.vue";
import ChatRoomTop from "@/pages/sub/im/components/ChatRoomTop/index.vue";
import ChatRoomHeader from "@/pages/sub/im/components/ChatRoomHeader/index.vue";
import ServicePopUp from "@/pages/sub/im/components/ServicePopUp/index.vue";
import { WS_MESSAGE_TYPE } from "yoc-im-web";
import { setImCacheConfig } from "@/libs/token.js";
import { imCaseInfoStateProps } from "@/pages/sub/im/mixins/case-info-state.js";
import { isNull } from "@/libs/basics-tools.js";
import { messageProcessing } from "@/pages/sub/im/mixins/message-processing.js";
import { imCardMap, imMessageType } from "@/pages/sub/im/enum/imEnum.js";
import UModal from "@/uview-ui/components/u-modal/u-modal.vue";
import { lawyerOfferPhone } from "@/api/im.js";
import { CaseSourceStatus } from "@/enum";
import { isIOSClient } from "@/libs/tools.js";
import LoginLayout from "@/components/login/LoginLayout.vue";

export default {
  name: "Index",
  components: {
    LoginLayout,
    UModal,
    ServicePopUp,
    ChatRoomHeader,
    ChatRoomTop,
    MessageCard,
    ToolsForLawyers,
    PhrasesTools,
    ChatTools,
    USafeBottom,
    "u--input": UInput
  },
  mixins: [imCaseInfoStateProps, messageProcessing],
  provide() {
    return {
    };
  },
  data() {
    return {
      /* 聊天工具显示状态*/
      chatToolsState: false,
      /* 快捷回复显示状态*/
      phrasesToolsState: false,
      msg: "",
      /* 律师服务弹窗*/
      servicePopUpState: false,
      /* 发送手机号 提示框*/
      phoneModalState: false,
      /* 意见书发送状态*/
      submissionsSendState: false
    };
  },
  computed: {
    // /* 输入区域显示状态 true显示 false不显示*/
    inputContainerState() {
      if(this.isLawyerHelp) return  true;
      if (isNull(this.serverStatus)) return false;
      // V2接口 服务结束 为3 退款 91  以前为4
      if (this.getExt.businessType === 10) {
        return (this.serverStatus !== CaseSourceStatus.CASE_SOURCE_STATUS_FINISHED && this.serverStatus !== CaseSourceStatus.CASE_SOURCE_STATUS_REFUND && this.serverStatus !== CaseSourceStatus.CASE_SOURCE_STATUS_FEEDBACK_INVALID && this.serverStatus !== CaseSourceStatus.CASE_SOURCE_STATUS_CANCEL_LOCK);
      }
      return this.serverStatus !== CaseSourceStatus.CASE_SOURCE_STATUS_CLOSED;
    },
    /* 键盘弹起高度 兼容ios*/
    cursorSpacing(){
      if(isIOSClient()){
        return 80;
      }else{
        return 30;
      }
    }
  },
  methods: {
    isNull,
    /* 常用语点击*/
    handlePhrasesClick(){
      this.chatToolsState = false;
      this.phrasesToolsState = !this.phrasesToolsState;
    },
    /* 更多工具点击*/
    handleChatToolClick(){
      this.phrasesToolsState = false;
      this.chatToolsState = !this.chatToolsState;
    },
    /* 发送卡片*/
    sendServerSend(data) {
      const cardData = {
        customEvent: "server",
        customExts: {
          showType: "0",
          style: imMessageType.SERVER_ONE_TO_ONE,
          subType: imMessageType.SERVER_ONE_TO_ONE,
          content: data.serviceNum + data.unitLabel,
          title: data.serviceName,
          ext: {
            icon: data.icon,
            serverCode: data.serviceCode,
            originalPrice: data.originalPrice,
            serverDesc: data.info,
            serverPrice: data.servicePrice
          }
        }
      };
      this.send({
        msgType: WS_MESSAGE_TYPE.CUSTOM,
        msg: cardData,
        ...this.commonSendState()
      });
    },
    /* 按钮点击发送文字*/
    sendPrivateText() {
      this.sendText(this.msg);
      this.phrasesToolsState = false;
      this.chatToolsState = false;
      this.msg = "";
    },
    /* 发送文字*/
    sendText(msg = this.msg) {
      if (isNull(msg.trim())) {
        uni.showToast({
          title: "请输入内容",
          icon: "none"
        });
        return false;
      }
      this.send({
        msgType: WS_MESSAGE_TYPE.TEXT,
        msg: msg.trim(),
        ...this.commonSendState()
      });
    },
    sendPhrases(text){
      this.msg = text;
    },
    /* 发送图片*/
    sendPrivateImg(){
      this.chatToolsState = false;
      uni.chooseImage({
        count: 1, // 默认9
        success: (res) => {
          let imgSrc = res.tempFilePaths[0];
          /* 图片发送*/
          this.send({
            msgType: WS_MESSAGE_TYPE.IMG,
            files: res.tempFiles,
            msg: imgSrc,
            allowType: ["png", "jpg", "gif", "bmp", "jpeg"],
            ...this.commonSendState(),
          });
        },
      });
    },
    /* 发送手机号确认弹窗*/
    handlePhonePopUp(){
      this.phoneModalState = true;
    },
    /* 发送手机*/
    sendPhone(){
      console.log(this.caseInfo);
      lawyerOfferPhone({
        caseSourceServerV2Id: this.caseSourceServerV2Id,
        /* 律师是否提供电话（1同意，9拒绝）*/
        type: 1
      });
      this.scrollToBottom();
      this.chatToolsState = false;

      this.phoneModalState = false;
    },
    /* 发送稍后提示*/
    handleServeLater(){
      this.sendText("您好，我现在在忙，稍后来解答您的问题");
      setImCacheConfig(this.conversationId, {
        isSendLater: true
      });
    },
    /* 加载的im卡片*/
    loadComponent(name){
      if(name === imCardMap.server_lawyer_idea){
        this.submissionsSendState = true;
      }
    }
  }
};
</script>

<style scoped lang="scss">
.chat-room-box {
  min-height: 100vh;
  background: #F0F0F2;
/*  .header-container{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
  }*/
  .operate-container {
    position: fixed;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 2;
    background: white;

    .input-box {
      padding: 8px 12px;

      .btn-box {
        padding: 8px 7px;
        background: #3887F5;
        border-radius: 8px 8px 8px 8px;
        font-size: 14px;
        font-weight: 400;
        color: #FFFFFF;
      }

      .textarea {
        padding-left: 8px;
        padding-right: 12px;

        ::v-deep .u-input {
          background: #F5F5F5;
          border: none;
        }
      }

      .icon-box {
        .iconfont {
          font-size: 24px;
        }
      }
    }
  }

  .chat-room-container {
    padding: 0 16px 150px;
    .date{

      font-size: 12px;
      font-weight: 400;
      color: #999999;
      text-align: center;
      padding-bottom: 16px;
    }
  }
}

.phone-modal{

  .phone-modal-content{
    .phone-modal-title{
      font-size: 15px;
      font-weight: 500;
      text-align: center;
      color: #333333;
      padding-bottom: 12px;
    }
    .phone-modal-text{
      font-size: 14px;
      font-weight: 400;
      text-align: center;
      color: #999999;
    }
  }
  ::v-deep{
    .u-modal__content{
      padding: 24px !important;
    }
    .u-popup__content{
      border-radius: 16px !important;
    }
  }
}
</style>
