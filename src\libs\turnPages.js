/**
 * 跳转到律师引导付费界面
 * @param {string} lawyerId 律师id
 * @param {string} [type] 1:快速咨询 2:其它咨询，其它咨询可不传
 * @param serverCode 选择的服务码
 */
import qs from "qs";
import store from "@/store";
import { whetherToLogIn } from "@/libs/tools";
import { setTabParams } from "@/libs/token";
import { isHttp } from "@/libs/basics-tools";
import { isFunction } from "lodash-es";


/* 跳转链接 判断是小程序链接还是htt链接 跳转对应的页面 */
export function turnToWebViewOrMiniProgramsPage({ src, query = {} }) {
  const { isToken = true } = query;
  if(isHttp(src)) return turnToWebViewPage({ src, isToken });
  if(isFunction(src)) return src();
  turnPages({
    path: src,
    query: query,
  });
}
/**
 * 将 query对象转换为字符串
 * @param query
 * @returns {string}
 */
export function queryToString(query) {
  let str = "?";
  for (let key in query) {
    str += `${key}=${query[key]}&`;
  }
  return str.substring(0, str.length - 1);
}

export function formatQuery({ path, query = {} }) {
  // 过滤 query 中的空值
  const queryStr = Object.keys(query)
    .filter(key => {
      return query[key] !== undefined && query[key] !== null;
    })
    .map(key => {
      return `${key}=${query[key]}`;
    })
    .join("&");

  return `${path}${queryStr ? "?" + queryStr : ""}`;
}

/**
 * 将 uni 的页面跳转方法转化为类Vue的跳转方法
 */
export function turnPages(options, isReplace = false) {
  return new Promise((resolve, reject) => {
    const { path, query, animationType, animationDuration, isTab = false } = options;
    // 过滤 query 中的空值
    for (let key in query) {
      if (query[key] === undefined || query[key] === null) {
        delete query[key];
      }
    }

    const queryStr = queryToString(query);
    let navigateTo = uni.navigateTo;
    if (isReplace) {
      navigateTo = uni.redirectTo;
    }
    if (isTab) {
      navigateTo = uni.switchTab;
    }
    navigateTo({
      url: path + queryStr,
      success() {
        resolve();
      },
      fail() {
        reject();
      },
      animationType,
      animationDuration,
    });
  });
}


/* 开启其他小程序*/
export function turnOpenApp({ path, extraData = {} }) {
  uni.navigateToMiniProgram({
    /* 只是微信的appid*/
    appId: "wxf2af270eb52577d7",
    path,
    extraData
  });
}

/** 获取当前页面路由 */
export function getCurrentPageRoute() {
  // 获取加载的页面
  const pages = getCurrentPages();
  // 获取当前页面的对象
  const currentPage = pages[pages.length - 1] || {};
  // 当前页面url
  return {
    fullPath: currentPage.$page?.fullPath,
    query: qs.parse(currentPage.$page?.fullPath?.split("?")?.[1]),
  };
}

/** 跳转到退单说明界面 */
export function turnToRefundInfoPage() {
  return turnPages({
    path: "/pages/pay/refund/intro/index",
  });
}

/**
 * 跳转到退单界面
 * @param id 案源服务id 通常是 caseSourceServerId 字段
 */
export function turnToRefundPage({ id }) {
  if (!id) return;

  return turnPages({
    path: "/pages/pay/refund/index",
    query: {
      id,
    },
  });
}

/**
 * 跳转到退单详情界面
 * @param id 案源服务id 通常是 caseSourceServerId 字段
 * @param isReplace
 */
export function turnToRefundDetailPage({ id, isReplace = false }) {
  if (!id) return;

  return turnPages({
    path: "/pages/pay/refund/detail/index",
    query: {
      id,
    },
  }, isReplace);
}

/** 跳转到权益详情 */
export function turnToRightsDetailPage() {
  return turnPages({
    path: "/pages/pay/clue/rights/index",
  });
}

/**
 * 跳转到消耗明细界面
 * @param id
 */
export function turnToConsumeDetailPage({ id }) {
  if (!id) return;

  return turnPages({
    path: "/pages/pay/clue/consume/index",
    query: {
      id,
    },
  });
}

/** 跳转到个人简介界面 */
export function turnToProfilePage() {
  return turnPages({
    path: "/pages/sub/profile/lawyer/intro/index",
  });
}

/** 跳转到教育背景界面 */
export function turnToEducationPage() {
  return turnPages({
    path: "/pages/sub/profile/lawyer/education/index",
  });
}

/** 跳转到荣誉奖项界面 */
export function turnToHonorPage() {
  return turnPages({
    path: "/pages/sub/profile/lawyer/awards/index",
  });
}

export function turnToWebViewPage({ src, isToken = false }) {
  if (!src) return;

  return turnPages({
    path: "/pages/sub/other/web-view/index",
    query: {
      src,
      isToken
    },
  });
}

/** 跳转到个人资料页 */
export function turnToProfileInfoPage() {
  return turnPages({
    path: "/pages/sub/profile/lawyer/index",
  });
}

/** 跳转到识别结果页面 */
export function turnToIdentifyResultPage() {
  return turnPages({
    path: "/pages/sub/profile/input/card/index",
  });
}

/** 跳转到执业证界面 */
export function turnToPracticePage() {
  return turnPages({
    path: "/pages/sub/profile/input/profession/index",
  });
}

/* 认证页面*/
export function turnToQASquare() {
  return turnPages({
    path: "/pages/sub/profile/lawyer/index",
  });
}

/* 首页*/
export function turnToHomePage(data) {
  return turnToCaseSourceSquare(data);
}

/* 去投诉详情界面*/
export function turnToDetailsOfTheComplaint({ id }) {
  return turnPages({
    path: "/pages/sub/im/details-of-the-complaint/index",
    query: {
      id,
    },
  });
}

/* im页面*/
export function turnToImPage({ id }) {
  return turnPages({
    path: "/pages/sub/im/index",
    query: {
      id,
    },
  });
}

/* 案源广场*/
export function turnToCaseSourceSquare({ type = "" } = {}) {
  setTabParams("index", { type });
  return turnPages(
    {
      path: "/pages/index/index",
      isTab: true
    }
  );
}

/* 跳转充值页面*/
export function turnToRechargePage(query) {
  return turnPages({
    path: "/pages/recharge/toUpCenter/index",
    query
  });
}


/**
 * 案源详情
 * @param {Object}  query:{ id: 案源服务id, type: 案源类型 1、2 为案源广场案源； 3 为已锁定案源}
 * @param isReplace 是否替换当前页面
 */
export function turnToCaseSourceDetails(query, isReplace = false) {
  return turnPages({
    path: "/pages/caseSource/detail/index",
    query
  }, isReplace);
}

/* 我得钱包*/
export function turnToMyWallet() {
  return turnPages({
    path: "/pages/recharge/myWallet/index",
  });
}

/* 系统消息页面*/
export function turnToSystemMessagesPage({ type }) {
  return turnPages({
    path: "/pages/sub/im/system-notice/index",
    query: {
      type,
    },
  });
}

/** 跳转到裁剪图片页面 */
export function turnToCropImagePage({
  destWidth = 300,
  rectWidth = 300,
  fileType = "jpg",
} = {}) {
  return turnPages({
    path: "/pages/sub/other/cropper/index",
    query: {
      // 输出图片宽度，高等于宽，单位px
      destWidth,
      // 裁剪框宽度，高等于宽，单位px
      rectWidth,
      // 输出的图片类型，如果'png'类型发现裁剪的图片太大，改成"jpg"即可
      fileType,
    },
  });
}

/**
 * * 跳转到律师认证结果界面，除了下面两种情况，其它界面直接调用即可
 * @param [isAuth] 是否是认证界面调用
 * @param [isCaseDetail] 是否是案源详情调用
 * @param [callback] 回调函数，已认证调用
 */
export function turnToLawyerAuthResultPage({
  isAuth = false,
  isCaseDetail = false,
  callback
} = {}) {
  const certStatus = store.getters["user/getCertStatus"];
  if(!callback){
    callback = () => {
      return turnPages(
        {
          path: "/pages/sub/profile/lawyer/verify-result/index",
        },
        isAuth
      );
    };
  }

  // 如果已经认证过了，跳转到认证结果界面
  if (certStatus === 2) {
    callback();
    return;
  }

  // 如果认证中，跳转到认证中界面
  if (certStatus === 1)
    return turnToLawyerAuthingPage({
      isAuth,
    });

  if (isAuth || isCaseDetail) {
    return;
  }

  // 如果未认证，跳转到认证界面
  return turnToLawyerAuthPage();
}

/**
 * * 跳转到律师认证结果界面，除了下面两种情况，其它界面直接调用即可
 * @param [callback] 回调函数，已认证调用
 */
export const turnToLawyerAuthResultPageToLogin = (callback) => {
  whetherToLogIn(() => {
    turnToLawyerAuthResultPage({ callback });
  });
};

/** 跳转到认证中界面 */
export function turnToLawyerAuthingPage({ isAuth = false } = {}) {
  return turnPages(
    {
      path: "/pages/sub/profile/input/verify/index",
    },
    isAuth
  );
}

export const LAWYER_AUTH_PAGE_PATH = "/pages/sub/profile/input/index";

/** 跳转到律师认证界面 */
export function turnToLawyerAuthPage() {
  return turnPages({
    path: LAWYER_AUTH_PAGE_PATH,
  });
}

/** 跳转到客服中心界面 */
export function turnToServiceCenterPage() {
  return turnPages({
    path: "/pages/sub/callCenter/index",
  });
}

/* 跳转新手指南*/
export function turnToNewGuidePage() {
  return turnPages({
    path: "/pages/sub/other/beginnerSGuide/index",
  });
}

/** 跳转到线索包支付界面 */
export function turnToCluePackConfirmOrderPage(query = {}) {
  return turnPages({
    path: "/pages/pay/clue/cluePackConfirmOrder/index",
    query
  });
}

/** 跳转到案源支付界面 */
export function turnToCaseSourcePayPage(query = {}) {
  return turnPages({
    path: "/pages/caseSource/pay/index",
    query
  });
}

/** 跳转到钱包明细详情 */
export function turnToTradeDetailPage(query = {}) {
  return turnPages({
    path: "/pages/recharge/myWallet/tradeDetail",
    query
  });
}

/** 跳转到赠送法临币明细详情 */
export function turnToGiveDetailPage(query = {}) {
  return turnPages({
    path: "/pages/recharge/myWallet/giveDetail",
    query
  });
}

/**
 * 跳转到律师引导付费界面
 * @param {string} lawyerId 律师id
 * @param {string | number} [type] 1:快速咨询 2:其它咨询，其它咨询可不传
 * @param serverCode 选择的服务码
 * @param {string | number} [createSource] 案源或者咨询数据来源 0 默认 1问律师 2打官司 3新版本文章详情
 * @param {string | number} [createSourceBusinessId] 案源或者咨询数据来源业务id 为3时为文章id
 */
export function toPayLawyerGuide({
  lawyerId,
  type,
  serverCode,
  createSource,
  createSourceBusinessId
}) {
  if (!lawyerId) {
    return;
  }

  turnPages({
    path: "/pages/sub/lawyer-home/pay-lawyer-guide/index",
    query: {
      lawyerId,
      type,
      serverCode,
      createSource,
      createSourceBusinessId
    }
  });
}


/* 跳转文章详情*/
export function toArticleDetail({ id }) {
  turnPages({
    path: "/pages/sub/lawyer-home/essay-detail/index",
    query: {
      id
    }
  });
}

/**
 * 跳转到律师主页
 * @param {string} id 律师id 不传则跳转到自身律师主页
 */
export function toLawyerHome({ id } = {}) {
  const toPage = () => {
    return whetherToLogIn(() => {
      turnPages({
        path: "/pages/sub/lawyer-home/index",
        query: {
          id
        }
      });
    });
  };

  if(!id) {
    // 如果没有 id 则会查自身的数据，判断是否有认证，没有认证则先进行认证
    turnToLawyerAuthResultPage({
      callback: () => {
        toPage();
      }
    });

    return;
  }

  toPage();
}

/**
 * 跳转到律师主页-裁决文书
 */
export function toLawyerHomeJudgment({ id }) {
  turnPages({
    path: "/pages/sub/lawyer-home/document/index",
    query: {
      id
    }
  });
}

/**
 * 跳转到律师主页-案例详情
 */
export function toLawyerHomeJuDocumentDetail({ id, lawyerId, state }) {
  /* 是不是个人上传案例 详情页*/
  const path = state
    ? "/pages/sub/lawyer-home/document-detail-of-me/index"
    : "/pages/sub/lawyer-home/document-detail/index";
  turnPages({
    path: path,
    query: {
      id,
      ...(lawyerId ? { lawyerId } : {})
    }
  });
}


/**
 * 跳转到律师主页-评价
 */
export function toLawyerHomeComment({ id }) {
  turnPages({
    path: "/pages/sub/lawyer-home/comment/index",
    query: {
      id
    }
  });
}

/**
 * 跳转到律师主页-案件统计
 */
export function toLawyerHomeCaseStatistics({ id }) {
  turnPages({
    path: "/pages/sub/lawyer-home/case/index",
    query: {
      id
    }
  });
}

/**
 * 跳转文章列表
 * @param [lawyerId]
 * @param [typeValue]
 */
export function toArticleList({ lawyerId, typeValue, isSearch } = {}) {
  if (lawyerId) {
    turnPages({
      path: "/pages/sub/lawyer-home/essay-list/index",
      query: {
        lawyerId,
        typeValue
      }
    });

    return;
  }

  turnPages({
    path: "/pages/sub/lawyer-home/essay-list/index",
    query: {
      typeValue
    }
  });
}

/**
 * 发布协作
 * @param {string} id 协作id
 * @param {string} showHistoryPopup 进入页面是否显示历史发布弹窗
 */
export function toPublishCollaboration({ id, showHistoryPopup } = {}) {
  turnPages({
    path: "/pages/sub/collaboration-details/publishCollaboration/index",
    query: {
      id,
      showHistoryPopup
    }
  });
}

/* 协作详情*/
export function toCollaborationDetails({ id }) {
  turnPages({
    path: "/pages/sub/collaboration-details/index",
    query: {
      id
    }
  });
}

/* 协作详情*/
export function toCollaborationDetailsPath({ id }) {
  return formatQuery({
    path: "/pages/sub/collaboration-details/index",
    query: {
      id
    }
  });
}


/* 合同上传*/
export function toContractUpload() {
  turnPages({
    path: "/pages/sub/other/contractUpload/index",
  });
}

/* 我的协作列表*/
export function toMyCollaborationList() {
  turnPages({
    path: "/pages/sub/collaboration-details/collaborationLists/index",
  });
}

/* 消息通知 */
export function toMessageNotice() {
  turnPages({
    path: "/pages/sub/im/messageNotice/index",
  });
}


/** 跳转到成单喜报 */
export function toOrderSuccessful() {
  turnPages({
    path: "/pages/sub/order-successful/index",
  });
}

/** 跳转到成单喜报详情 */
export function toOrderSuccessfulDetails({ id, lawyerId }) {
  turnPages({
    path: "/pages/sub/order-successful/details/index",
    query: {
      id,
      lawyerId
    }
  });
}

/* 问答广场 */
export function toAnswerSquare(isReplace = false) {
  turnPages({
    path: "/pages/sub/qa-square/index",
  }, isReplace);
}

/* 我的问答 */
export function toMyAnswer() {
  turnPages({
    path: "/pages/sub/my-qa/index",
  });
}

/* 问答详情 */
export function toAnswerDetails({ id }) {
  turnPages({
    path: "/pages/sub/qa-details/index",
    query: {
      id
    }
  });
}

/* 问答解答规范 */
export function toAnswerSpecification() {
  turnPages({
    path: "/pages/sub/example-of-qa/index",
  });
}

/* 找律师页面 */
export function toFindLawyer() {
  turnPages({
    path: "/pages/find-lawyer/index",
    isTab: true
  });
}

/** 跳转到协作页面 */
export function toCollaboration() {
  turnPages({
    path: "/pages/collaborative/index",
    isTab: true
  });
}

/* 问答回复 */
export function toAnswerReply({ id }) {
  turnPages({
    path: "/pages/sub/qa-answer/index",
    query: {
      id
    }
  });
}

/* 办案工具跳转 */
export function toHandleTool() {
  turnPages({
    path: "/pages/collaboration-tools/index"
  });
}


/* 跳转协作设置 */
export function toCollaborationSettings() {
  turnPages({
    path: "/pages/sub/collaboration-settings/index",
  });
}

/* 跳转律师费计算器 */
export function toLawyerFeeCalculator() {
  whetherToLogIn(() => {
    turnToWebViewOrMiniProgramsPage({ src: process.env.VUE_APP_CHILD_URL + "/#/caculator/civil" });
  });
}
/* 跳转仲裁费计算 */
export function toArbitrationFeeCalculator() {
  whetherToLogIn(() => {
    turnToWebViewOrMiniProgramsPage({ src: process.env.VUE_APP_CHILD_URL + "/#/caculator/labor-arbitration" });
  });
}

/* 跳转同行群详情 */
export function toCompanionGroupDetails({ id }) {
  turnPages({
    path: "/pages/sub/community/community-details/index",
    query: {
      id
    }
  });
}

/* 跳转发布的群记录 */
export function toPublishCompanionGroupRecord() {
  turnPages({
    path: "/pages/sub/community/publish-community-record/index",
  });
}

/* 跳转发布群 */
export function toPublishCompanionGroup({ id }) {

  turnPages({
    path: "/pages/sub/community/publish-community/index",
    query: {
      id
    }
  });
}
