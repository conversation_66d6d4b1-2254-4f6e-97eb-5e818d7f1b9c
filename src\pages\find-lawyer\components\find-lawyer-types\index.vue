<template>
  <div>
    <div class="find-lawyer-types-lx">
      <div
        v-for="(item, i) in contactLists"
        :key="i"
        class="type-lists"
        @click="selectType(item, i)"
      >
        <div class="type-name">
          <img
            :src="item.ext.icon"
            alt="图标"
            class="type-name-img"
          >
          <span class="type-name-text">{{ item.label }}</span>
        </div>
        <div class="type-desc text-ellipsis-2">
          {{ item.ext.remark }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import { isNull } from "@/libs/basics-tools.js";

export default {
  name: "FindLawyerTypes",
  props: {
    list: {
      type: Array,
      default: () => {
        return [];
      },
      desc: "展示的列表"
    }
  },
  data(){
    return {
      selectedData: {}
    };
  },
  computed: {
    contactLists() {
      const arr = JSON.parse(JSON.stringify(this.list));

      arr.unshift( {
        label: "全部类型",
        value: "",
        ext: {
          icon: require("@/pages/find-lawyer/components/find-lawyer-types/img/<EMAIL>"),
          remark: "涵盖所有问题类型"
        }
      });

      return arr;
    }
  },
  methods: {
    selectType(item, index) {
      this.selectedData = item;
      const data = {
        ...item,
        ...(isNull(item.name) ? { name: item.label } : {  }),
      };
      this.$emit("getCaseType", { ...data, index });
    }
  }
};
</script>

<style lang="scss" scoped>
.find-lawyer-types-lx {
  padding: 0 16px;
  display: grid;
  grid-template-columns: repeat(2, 164px);
  gap: 12px 16px;
  justify-content: center;
  max-height: 375px;
  overflow-y: auto;
  background: #f5f5f7;
  border-radius: 0 0 16px 16px;

  .type-lists {
    padding: 12px;
    background: #ffffff;
    font-size: 12px;
    color: #666666;
    width: 164px;
    height: 96px;
    border-radius: 8px 8px 8px 8px;
    box-sizing: border-box;
    // margin: 8px auto;
    .type-name {
      color: #333333;
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 5px;

      &-img {
        width: 24px;
        height: 24px;
        margin-right: 8px;
      }

      &-text {
        vertical-align: top;
      }
    }

    .type-desc {
      line-height: 20px;
    }
  }
}
</style>
