<template>
  <div>
    <div class="card-box">
      <div
        v-for="(item, index) in lawyerConsultationList"
        :key="item.serviceCode"
        :class="[cardBoxItemInfoClass]"
        class="card-box-item z-10"
        @click="toNewPaidGuidance(item)"
      >
        <img
          v-if="index === 0"
          alt=""
          class="background-image"
          src="../img/<EMAIL>"
        >
        <img
          v-if="index === 1"
          alt=""
          class="background-image"
          src="../img/<EMAIL>"
        >
        <div class="flex flex-align-center">
          <div
            class="card-box-item-info flex flex-column flex-space-between w-full"
          >
            <div class="card-box-item-info-name font16 flex flex-align-center">
              <div class="text-ellipsis">
                {{ item.serviceName }}
              </div>
              <img
                alt=""
                class="card-box-item-info-name__icon"
              >
            </div>
            <div class="text-[13px] text-[#FFFFFF] flex items-center mt-[6px]">
              <p>
                ¥{{
                  (item.servicePrice ? item.servicePrice : 0) | amountFilter
                }}
              </p>
              /
              <p>
                {{ item.serviceNum + item.unitLabel }}
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- item 数量少于2个的时候凑齐2个 -->
      <div
        v-for="item in countNum(2, lawyerConsultationList.length)"
        :key="item"
        class="card-box-item-placeholder"
      >
        <p>暂无更多服务</p>
      </div>
    </div>
    <div
      v-if="!$basicsTools.isArrNull(lawyerCaseList)"
      class="trust"
    >
      <div
        v-for="item in lawyerCaseList"
        :key="item.id"
        class="trust-item"
        @click="toPaidGuidance(item)"
      >
        <!-- 图标 -->
        <img
          :src="item.icon"
          alt=""
          class="trust-item-icon"
        >
        <p class="trust-item-name">
          {{ item.serviceName }}
        </p>
        <p class="trust-item-price">
          ¥{{ (item.servicePrice ? item.servicePrice : 0) | amountFilter }}
        </p>
        <p class="trust-item-time">
          {{ item.serviceNum + item.unitLabel }}
        </p>
      </div>
      <!-- item 数量少于4个的时候凑齐4个 -->
      <div
        v-for="item in countNum(4, lawyerCaseList.length)"
        :key="item"
        class="trust-item-placeholder"
      >
        <p>暂无更多服务</p>
      </div>
    </div>
  </div>
</template>

<script>
import { toPayLawyerGuide } from "@/libs/turnPages.js";
import { serviceManegeGetLawyerAllServiceList } from "@/api/im";

export default {
  name: "LawyerHomeServersItem",
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    lawyerInfo: {
      type: Object,
      default: () => ({}),
    },
    lawyerConsultationList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      /** ! 律师服务 这里主要是用来判断是否有对应的电话或图文服务 */
      lawyerService: {
        1: [],
        2: [],
      },
    };
  },
  computed: {
    /** 律师服务数量 */
    lawyerAppointDetailsLength() {
      return this.serviceManageV2VoList?.length || 0;
    },
    /** 根据item数量展示不同的样式 */
    cardBoxItemInfoClass() {
      if (this.lawyerAppointDetailsLength === 2) return "card-box-item--tow";

      return "";
    },
    /** 案件委托服务 */
    lawyerCaseList() {
      return this.data.filter((item) => Number(item.serviceType) === 1002);
    },
  },
  watch: {
    lawyerInfo: {
      handler() {
        this.getLawyerService();
      },
      deep: true,
    },
  },
  methods: {
    getLawyerService() {
      const requestArr = [
        serviceManegeGetLawyerAllServiceList({
          lawyerId: this.lawyerInfo.id,
          itemClassType: 1,
        }),
        serviceManegeGetLawyerAllServiceList({
          lawyerId: this.lawyerInfo.id,
          itemClassType: 2,
        }),
      ];

      Promise.all(requestArr).then((res) => {
        console.log(res, "res");
        this.lawyerService = {
          1: res[0]?.data || [],
          2: res[1]?.data || [],
        };
      });
    },
    /** 计算个数 */
    countNum(a, b) {
      const num = Number(a || 0) - Number(b || 0);

      return num > 0 ? num : 0;
    },
    /** 新版电话咨询去支付 */
    toNewPaidGuidance(item) {
      this.$emit("click", item);
    },
    toPaidGuidance(item) {
      toPayLawyerGuide({
        lawyerId: this.lawyerInfo.id,
        type: 2,
        serverCode: item.serviceCode,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.card-box {
  width: 311px;
  border-radius: 8px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 12px;

  &-item {
    height: 80px;
    border-radius: 8px;
    padding-top: 12px;
    padding-left: 12px;
    box-sizing: border-box;
    position: relative;

    &-info {
      &-width {
        width: 80px;
      }

      &-name {
        font-size: 18px;
        font-weight: bold;
        color: #ffffff;
        max-width: 120px;

        &__icon {
          flex-shrink: 0;
          display: block;
          width: 16px;
          height: 16px;
        }
      }

      &-price {
        margin-top: 6px;
        font-size: 12px;
        font-weight: 400;
        color: #ffffff;
      }
    }
  }
}

.consult-button {
  height: 25px;
  background: #ffffff;
  border-radius: 68px;
  opacity: 1;
  text-align: center;
  line-height: 25px;
  margin-top: 8px;
  font-size: 12px;
  font-weight: 400;

  &--0 {
    color: #49bf8e;
  }

  &--1 {
    color: #3887f5;
  }
}

.trust {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 8px;
  margin-top: 16px;

  &-item {
    width: 72px;
    height: 105px;
    background: #f5f5f7;
    border-radius: 8px;
    box-sizing: border-box;
    text-align: center;
    opacity: 1;

    &-icon {
      display: block;
      width: 28px;
      height: 28px;
      border-radius: 7px;
      margin: 10px auto 0;
    }

    &-name {
      margin-top: 8px;
      font-size: 13px;
      font-weight: 400;
      color: #333333;
    }

    &-price {
      margin-top: 2px;
      font-size: 11px;
      font-weight: 400;
      color: #999999;
    }

    &-time {
      font-size: 11px;
      font-weight: 400;
      color: #999999;
    }
  }
}

.trust-item-placeholder {
  height: 105px;
  font-size: 11px;
  font-weight: 400;
  color: #999999;
  padding: 0 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f7;
  text-align: center;
  border-radius: 8px;
  width: 44px;
}

.card-box-item-placeholder {
  font-size: 11px;
  font-weight: 400;
  color: #999999;
  padding: 0 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80px;
  background: #f5f5f7;
  text-align: center;
  border-radius: 8px;
}
</style>
