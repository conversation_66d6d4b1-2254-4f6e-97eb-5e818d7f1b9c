<template>
  <div class="p-[16px] bg-white flex items-center">
    <img
      class="w-[44px] flex-shrink-0 rounded-full h-[44px] border-[1px] border-solid border-[#E8EFEF]"
      :src="lawyerInfo.imgUrl"
      mode="aspectFill"
      alt=""
    >
    <div class="flex flex-1 items-center justify-between pl-[8px]">
      <div>
        <p
          class="font-[600] text-[16px] flex items-center text-[#222229]"
          @click.stop="toLawyerHome"
        >
          {{ lawyerInfo.realName }}律师
          <i class="iconfont ml-[4px] icon-erjiyoujiantou text-[#666666] !text-[12px]" />
        </p>
        <div class="flex pt-[4px]">
          <p class="max-w-[60px] line-clamp-1 px-[4px]  box-border mr-[4px] py-[3px] bg-[#F7F6F6] rounded-[4px] text-[12px] text-[#666666]">
            执业{{ lawyerInfo.workTime }}年
          </p>
          <p class="max-w-[92px] box-border line-clamp-1 px-[4px] py-[3px] bg-[#F7F6F6] rounded-[4px] text-[12px] text-[#666666]">
            {{ lawyerInfo.workCityName }}
          </p>
        </div>
      </div>
      <div class="center">
        <div
          class="text-center w-[62px]"
          @click="changeThePhone"
        >
          <i class="iconfont icon-a-Component1 !text-[24px] text-[#222229]" />
          <p class="pt-[4px] text-[12px] text-[#666666]">
            换电话
          </p>
        </div>

        <div
          class="text-center w-[62px]"
          @click="changeWeChat"
        >
          <i class="iconfont icon-btn_icon_weixin !text-[24px] text-[#222229]" />
          <p class="pt-[4px] text-[12px] text-[#666666]">
            换微信
          </p>
        </div>
      </div>
    </div>

    <!-- 交换手机号或者微信提示框   -->
    <div v-if="exchangePhoneOrWechatInfo.show">
      <exchange-phone-or-wechat-popup
        v-model="exchangePhoneOrWechatInfo.show"
        :lawyerInfo="lawyerInfo"
        :data="exchangePhoneOrWechatInfo"
      />
    </div>
  </div>
</template>

<script>
import { conversationInfoStateProps, sessionIMThisSymbolProps } from "@/pages/sub/im/mixins/case-info-state";
import { imOneLawyer } from "@/api/lawyer";
import { toLawyerHome } from "@/libs/turnPages";
import ExchangePhoneOrWechatPopup from "@/pages/sub/im/components/ExchangePhoneOrWechatPopup/index.vue";
import { whetherToBindWechat } from "@/libs/tools";

export default {
  name: "CollaborateOnOpposingCounselInfoCards",
  components: { ExchangePhoneOrWechatPopup },
  mixins: [sessionIMThisSymbolProps, conversationInfoStateProps],
  data() {
    return {
    //   律师信息
      lawyerInfo: {},
      /* 交换微信或者手机号弹窗 */
      exchangePhoneOrWechatInfo: {
        show: false,
      }
    };
  },
  methods: {
    toLawyerHome(){
      toLawyerHome({
        id: this.lawyerInfo.id
      });
    },
    conversationInfoInit(){
      imOneLawyer({
        imToken: this.getConversationInfo.getOtherToken
      }).then(({ data = {} }) => {
        this.lawyerInfo = data;
      });
    },
    // 点击换电话
    changeThePhone(){
      // 交换信息
      this.clickHeadExchangeOfInfo({
        type: "phone",
        text: "电话号"
      });
    },
    // 点击换微信
    changeWeChat(){
      whetherToBindWechat(() => {
        this.clickHeadExchangeOfInfo({
          type: "wechat",
          text: "微信号"
        });
      });
    },
    //   顶部交换手机号点击
    clickHeadExchangeOfInfo(data){
      console.log(data);
      this.exchangePhoneOrWechatInfo = {
        show: true,
        ...data
      };
    }
  }
};
</script>

<style scoped lang="scss">

</style>