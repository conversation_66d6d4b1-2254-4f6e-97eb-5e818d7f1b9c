import ImWebSdk, { RUN_ENVIRONMENT, SIGNAL_TYPE } from "yoc-im-web";
import Vue from "vue";
import store from "../store";
import { BURY_POINT_CHANNEL_TYPE, buryPointChannelBasics, IM_STATUS } from "@/libs/burypoint";

export const EventBus = new Vue();
let ons = [];
let routeName =  "";

/* 这里 做一次代理 每次注册事件都会把重复的事件给销毁保持事件的唯一性
* 一个页面可以存在多个 相同事件 切换页面的时候会销毁 上一个页面
* */
function $onRewrite(event, callback){
  const currentPages = getCurrentPages();
  const currentRouter = currentPages[currentPages.length - 1];
  /* 判断当前路由切换没有 删除已经调用过得事件*/
  if(routeName !== currentRouter){
    console.log("上一页im监听的回调", ons.join(","));
    /* 这里不能摧毁全部监听 只能摧毁当前页面的监听*/
    $offRewrite();
  }
  routeName = currentRouter;
  ons.push(event);
  return  EventBus.$on(event, callback);
}

/* 清除当前注册的事件*/
function $offRewrite(){
  ons.forEach((onKey) => EventBus.$off(onKey));
  ons = [];
}

EventBus.$offRewrite = $offRewrite;
EventBus.$onRewrite = $onRewrite;
Vue.prototype.$ImEventBus = EventBus;

const WebIM = new ImWebSdk({
  httpEnvironment: process.env.VUE_APP_ENV_IM_HTTP_ENVIRONMENT,
  autoReconnectNumMax: 50,
  https: true,
  runEnvironment: RUN_ENVIRONMENT.UNI_APP,
  // #ifdef MP-TOUTIAO
  signalType: SIGNAL_TYPE.JSON
  // #endif
});
Vue.prototype.$WebIM = WebIM;
WebIM.listen({
  // 连接成功回调
  // eslint-disable-next-line no-unused-vars
  onOpened: (message) => {
    buryPointChannelBasics({
      code: "IM_CONNECT_MSG",
      type: 1,
      behavior: BURY_POINT_CHANNEL_TYPE.VI,
      extra: {
        im_status: IM_STATUS.AUTHENTICATION_SUCCESS
      }
    });
    console.log("连接成功回调");
    EventBus.$emit("onOpened");
  },
  // 连接关闭回调
  onClosed: function (message) {
    console.log("连接关闭回调");
    EventBus.$emit("onClosed", message);
  },
  // 错误回调
  onError: function (message) {
    console.log("错误回调=======", message);
    buryPointChannelBasics({
      code: "IM_CONNECT_MSG",
      type: 1,
      behavior: BURY_POINT_CHANNEL_TYPE.VI,
      extra: {
        im_status: IM_STATUS.ERROR_CATCH,
        ...message
      }
    });
    EventBus.$emit("onError", message);
  },
  // 收到文本消息
  onTextMessage: (message) => {
    console.log("收到文本消息");
    console.log(message);
    EventBus.$emit("onTextMessage", message);

  },
  // 收到图片消息
  onPictureMessage: function (message) {
    console.log("收到图片消息");
    EventBus.$emit("onPictureMessage", message);
  },
  // 收到文件消息
  onFileMessage: function (message) {
    console.log("收到文件消息");
    EventBus.$emit("onFileMessage", message);
  },
  // 收到自定义消息
  onCustomMessage: function (message) {
    console.log("收到自定义消息");
    EventBus.$emit("onCustomMessage", message);
  },
  // 收到自定义消息
  onSystemMessage: function (message) {
    console.log("收到系统消息");
    EventBus.$emit("onSystemMessage", message);
  },
  //
  onReceivedMessage: function (message) {
    EventBus.$emit("onReceivedMessage", message);
  },
  // 收到所有消息
  onMessage: function (message) {
    console.log("收到所有消息");
    EventBus.$emit("onMessage", message);
  },
  // 收到消息送达客户端回执
  // eslint-disable-next-line no-unused-vars
  onDeliveredMessage: function(message){
  },
  onOtherCommandMessage: function(message){
    console.log("收到指令消息", message);
    EventBus.$emit("onOtherCommandMessage", message);
  },
  onAuthenticationFailed: function(message){
    console.log("收到认证失败指令", message);
    buryPointChannelBasics({
      code: "IM_CONNECT_MSG",
      type: 1,
      behavior: BURY_POINT_CHANNEL_TYPE.VI,
      extra: {
        im_status: IM_STATUS.AUTHENTICATION_FAILED
      }
    });
    EventBus.$emit("onAuthenticationFailed", message);
  }
});


EventBus.$on("onOpened", () => {
  // 设置为打开状态
  store.commit("im/SET_OPEN", true);
});
EventBus.$on("onClosed", () => {
  // 设置为打开状态
  store.commit("im/SET_OPEN", false);
});

EventBus.$on("onAuthenticationFailed", () => {
  store.commit("im/SET_OPEN", false);
});

WebIM.conn = WebIM;
export  {
  WebIM
};
