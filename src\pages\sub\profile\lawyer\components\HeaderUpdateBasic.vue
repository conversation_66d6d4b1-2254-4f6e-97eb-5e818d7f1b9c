<template>
  <div>
    <app-upload
      v-model="fileList"
      :maxCount="999"
      :previewImage="false"
    >
      <slot :header="header" />
    </app-upload>
  </div>
</template>

<script>
import AppUpload from "@/components/AppComponents/AppUpload/index.vue";

export default {
  name: "HeaderUpdateBasic",
  components: { AppUpload },
  props: {
    value: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      fileList: [],
    };
  },
  computed: {
    /** 头像 */
    header() {
      return this.value;
    },
  },
  watch: {
    fileList: {
      handler(val) {
        // 如果图片为空 则不传递
        if (!val[val.length - 1]?.url) return;

        console.log(val[val.length - 1]?.url, "图片");
        // 传递最后一个图片
        this.$emit("input", val[val.length - 1]?.url);
      },
      immediate: true,
      deep: true,
    },
  },
};
</script>

<style lang="scss" scoped></style>
