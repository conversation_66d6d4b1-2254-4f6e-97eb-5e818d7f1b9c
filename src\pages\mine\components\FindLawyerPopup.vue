<template>
  <div>
    <app-popup
      :safeAreaInsetBottom="false"
      :show="show"
      mode="center"
    >
      <div class="w-[311px] pt-[24px] pb-[16px]">
        <div class="font-bold text-[16px] text-[#333333] text-center">
          法临温馨提示
        </div>
        <div class="px-[16px]">
          <div class="text-[14px] text-[#333333] mt-[12px]">
            1.法临仅为双方提供信息展示和沟通平台，请自行确认对方协作信息的真实性后再进行合作，避免给您带来不必要的损失
          </div>
          <div class="text-[14px] text-[#333333] mt-[12px]">
            2.协作费用由发单、接单双方律师自主协商并自行完成支付， 法临不收取也不担保任何费用
          </div>
        </div>
        <div class="w-[311px] h-[80px] bg-[#F5F5F5] flex justify-between items-center mt-[16px]">
          <div class="flex-1">
            <div class="flex justify-center items-end">
              <div class="font-bold text-[22px] text-[#3887F5]">
                {{ getTotalContactLawyer }}
              </div>
              <div class="text-[14px] text-[#666666] ml-[4px]">
                次
              </div>
            </div>
            <div class="text-[12px] text-[#666666] mt-[4px] text-center">
              今天可打招呼数
            </div>
          </div>
          <div class="w-[1px] h-[32px] bg-[#EEEEEE] shrink-0" />
          <div class="flex-1">
            <div class="flex justify-center items-end">
              <div class="font-bold text-[22px] text-[#3887F5]">
                {{ getRemainContactLawyer }}
              </div>
              <div class="text-[14px] text-[#666666] ml-[4px]">
                次
              </div>
            </div>
            <div class="text-[12px] text-[#666666] mt-[4px] text-center">
              剩余次数
            </div>
          </div>
        </div>
        <div class="flex items-center justify-between px-[24px] mt-[24px]">
          <div
            class="w-[84px] h-[44px] rounded-[68px] border-[1px] border-solid border-[#CCCCCC] flex items-center justify-center"
            @click="show=false"
          >
            <div class="text-[16px] text-[#333333]">
              取消
            </div>
          </div>
          <div
            class="w-[167px] h-[44px] bg-[#3887F5] rounded-[68px] flex items-center justify-center"
            @click="callPhone"
          >
            <div class="text-[16px] text-[#FFFFFF]">
              继续打招呼
            </div>
          </div>
        </div>
      </div>
    </app-popup>

    <contact-a-lawyer-reaches-the-upper-limit
      v-model="contactALawyerReachesTheUpperLimit"
    />
  </div>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import ContactALawyerReachesTheUpperLimit from "@/pages/index/components/contactALawyerReachesTheUpperLimit.vue";
import { imCreate } from "@/api/im";
import { turnToImPage } from "@/libs/turnPages";
import { mapGetters } from "vuex";

export default {
  name: "FindLawyerPopup",
  components: { ContactALawyerReachesTheUpperLimit, AppPopup },
  props: {
    data: {
      type: Object,
      default: () => ({}),
      required: true
    },
    value: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  data() {
    return {
      /* 联系律师上限弹窗 */
      contactALawyerReachesTheUpperLimit: false
    };
  },
  computed: {

    ...mapGetters(
      "user",
      ["getTotalContactLawyer", "getRemainContactLawyer"]
    ),
    show: {
      get(){
        return this.value;
      },
      set(val){
        this.$emit("input", val);
      }
    }
  },
  methods: {
    /* sayHello */
    sayHello(){
      const params = {
        type: 12,
        lawyerId: this.data.id
      };
      return imCreate(params);
      
    },
    
    /** 拨打电话 */
    callPhone() {
      this.sayHello().then(({ data = {} }) => {
        if(!data.imSessionId) return false;
        this.$store.dispatch("user/updateRemainContactLawyer");
        turnToImPage({
          id: data.imSessionId
        });
        this.show = false;
      });
    }
  }
};
</script>
