<template>
  <div class="myWallet">
    <div class="float-bg" />
    <div class="content-wrapper">
      <div class="main flex flex-align-center flex-column flex-space-center">
        <div class="balance-title">
          当前账户余额(元)
        </div>
        <div class="balance">
          {{ priceNumber(wallet.totalBalance) }}
        </div>
        <div class="line flex flex-space-between">
          <div class="item">
            <div class="title">
              充值(元)
            </div>
            <div class="num">
              {{ priceNumber(wallet.rechargeAccountBalance) }}
            </div>
          </div>
          <div class="item">
            <div
              class="title color-text"
              @click="turnToGiveDetailPage()"
            >
              赠送金额 <span class="iconfont icon-erjiyoujiantou" />
            </div>
            <div class="num">
              {{ priceNumber(wallet.giftAccountBalance) }}
            </div>
          </div>
          <div class="item">
            <div class="title">
              佣金(元)
            </div>
            <div class="num">
              {{ priceNumber(wallet.commissionAccountBalance) }}
            </div>
          </div>
        </div>
        <div
          class="btn"
          @click="turnToRechargePage"
        >
          充值
        </div>
        <div
          class="tip"
          @click="illustrateShow=true"
        >
          法临币说明
        </div>
      </div>
      <div class="title-bar flex flex-space-between flex-align-center">
        <div class="left">
          交易记录
        </div>
        <div class="search">
          <span
            class="search-item"
            @click="typeClick"
          >{{ currentType.label }} <span class="iconfont icon-xiala" /></span>
          <span
            class="search-item"
            @click="dateClick"
          >{{ getMonth() }} <span class="iconfont icon-xiala" /></span>
        </div>
      </div>
      <div class="list-wr">
        <div
          v-for="(item) in list"
          :key="item.id"
          class="list-item"
          @click="turnToTradeDetailPage({id:item.id})"
        >
          <div class="line flex flex-space-between">
            <div class="label">
              {{ item.title }}
            </div>
            <div
              v-if="item.type===1"
              class="num up"
            >
              +{{ priceNumber(item.amount) }}
            </div>
            <div
              v-else
              class="num down"
            >
              -{{ priceNumber(item.amount) }}
            </div>
          </div>
          <div class="date">
            {{ item.updateTime }}
          </div>
        </div>
      </div>
      <div
        v-if="!list.length"
        class="no-data"
      >
        <img
          alt=""
          class="img"
          src="@/pages/recharge/img/no-data.png"
        >
        <div>当前条件没有钱包记录哦</div>
      </div>
    </div>
    <app-popup
      :closeOnClickOverlay="true"
      :show="typeShow"
      mode="bottom"
      round="16"
      @close="typeClose"
    >
      <div class="type-wr">
        <div class="title">
          选择交易类型
        </div>
        <div class="con">
          <div
            v-for="item in typeList"
            :key="item.value"
            :class="{active:clickType.value === item.value}"
            class="con-item"
            @click="clickType=item"
          >
            {{ item.label }}
          </div>
        </div>
        <div
          class="btn"
          @click="typeConfirm(item)"
        >
          确认
        </div>
      </div>
    </app-popup>
    <datetime-picker
      ref="datetimePicker"
      v-model="dateValue"
      :show="dateShow"
      mode="year-month"
      round="16"
      title="选择交易时间"
      @cancel="dateShow = false"
      @confirm="confirm"
    />
    <app-popup
      :show="illustrateShow"
      closeOnClickOverlay
      mode="bottom"
      round="16"
      @close="illustrateShow=false"
    >
      <div class="illustrate-wr">
        <div class="title">
          什么是法临币？
        </div>
        <div class="con">
          法临币是法临推出的虚拟货币，1法临币可当作人民币1元，法临币可用于购买案源线索
        </div>
        <div class="title">
          法临币领取说明
        </div>
        <div class="con">
          通过活动获得的法临币奖励均需手动领取
        </div>
        <div class="title">
          如何获取法临币？
        </div>
        <div class="con">
          进入任务中心，完成相关任务后，手动领取奖励
        </div>
        <div class="title">
          其他说明
        </div>
        <div class="con">
          <div>1. 为了进一步提升律师对平台服务的满意度及体验,我们将不断更新获取法临币的任务种类</div>
          <div class="mg-tp-16">
            2. 若律师存在欺诈、作弊及其他不正当的手段获取法临币，一经发现平台将停止发放，取消账户中所有法临币
          </div>
        </div>
        <div class="title">
          赠送法临币说明
        </div>
        <div class="con">
          <div>1. 通过平台任务及充值法临币后额外赠送的法临币,将进入赠送法临币余额</div>
          <div class="mg-tp-16">
            2.赠送法临币自获取开始，将于30个自然日后到期失效
          </div>
        </div>
      </div>
    </app-popup>
    <u-safe-bottom />
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import DatetimePicker from "@/uview-ui/components/u-datetime-picker/u-datetime-picker.vue";
import { lawyerBillPage, walletBalance } from "@/api/recharge";
import { priceNumber } from "@/libs/tool";
import { turnToGiveDetailPage, turnToRechargePage, turnToTradeDetailPage } from "@/libs/turnPages";
import { dataDetailList } from "@/api";

export default {
  components: { DatetimePicker, AppPopup, USafeBottom },
  data() {
    return {
      typeShow: false,
      dateShow: false,
      dateValue: new Date(),
      illustrateShow: false,
      wallet: {},
      page: {
        currentPage: 1,
        total: 0,
      },
      typeList: [],
      list: [],
      clickType: { label: "全部", value: "" },
      currentType: { label: "全部", value: "" },
    };

  },
  onReady() {
    // 微信小程序需要用此写法
    this.$refs.datetimePicker.setFormatter(this.formatter);
  },
  onLoad() {
    walletBalance().then(({ data }) => {
      this.wallet = data;
    });
    this.getList();

    dataDetailList({ groupCode: "LAWYER_BILL_ACTION_TYPE" }).then(({ data = [] }) => {
      this.typeList = [{ label: "全部", value: "" }, ...data];
    });
  },
  onReachBottom() {
    this.scrollToLower();
  },
  computed: {

  },
  methods: {
    turnToGiveDetailPage,
    turnToTradeDetailPage,
    turnToRechargePage,
    priceNumber,
    getMonth(flag) {
      const date = new Date(this.dateValue);
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const year = date.getFullYear();
      if (flag) return `${year}${month}`;
      return `${year}年${month}月`;
    },
    typeClick(){
      this.typeShow = true;
    },
    dateClick(){
      this.dateShow = true;
    },
    confirm(){
      this.page.currentPage = 1;
      this.list = [];
      this.getList();
      this.dateShow = false;
    },
    typeConfirm(){
      this.page.currentPage = 1;
      this.list = [];
      this.currentType = this.clickType;
      this.getList();
      this.typeShow = false;
    },
    formatter(type, value) {
      if (type === "year") {
        return `${value}年`;
      }
      if (type === "month") {
        return `${value}月`;
      }
      if (type === "day") {
        return `${value}日`;
      }
      return value;
    },
    getList() {
      lawyerBillPage({
        ...this.page,
        month: this.getMonth(true),
        action: this.currentType.value,
      }).then(({ data = {} }) => {
        const records = data.records || [];
        this.list = [...this.list, ...records];
        this.page.total = data.total;
      });
    },
    scrollToLower() {
      if (this.list.length < this.page.total) {
        this.page.currentPage++;
        this.getList();
      }
    },
    typeClose(){
      this.typeShow = false;
      this.clickType = this.currentType;
    },
  },
};
</script>

<style lang="scss" scoped>
.myWallet{
  position: relative;
  .float-bg {
    position: absolute;
    width: 100%;
    height: 163px;
    background: linear-gradient(180deg, #3887F5 0%, rgba(56,135,245,0) 100%);
    z-index: 1;
  }
  .content-wrapper{
    position: relative;
    z-index: 2;
    padding: 16px 18px 0;
    .main{
      height: 247px;
      background: #FFFFFF;
      border-radius: 8px 8px 8px 8px;
      text-align: center;
      padding: 0 28px;
      position: relative;
      .balance-title{
        font-size: 12px;
        font-weight: 400;
        color: #999999;
      }
      .balance{
        font-size: 32px;
        font-weight: bold;
        color: #3887F5;
        margin-top: 8px;
        margin-bottom: 20px;
      }
      .line{
        width: 100%;
      }
      .item{
        font-size: 12px;
        font-weight: 400;
        color: #999999;
        .iconfont {
          vertical-align: bottom;
        }
      }
      .num{
        font-size: 20px;
        font-weight: bold;
        color: #333333;
        line-height: 28px;
        margin-top: 8px;
      }
      .btn{
        width: 200px;
        line-height: 40px;
        border-radius: 20px 20px 20px 20px;
        margin-top: 20px;
        text-align: center;
        font-size: 15px;
        font-weight: 600;
        color: #FFFFFF;
        background: #3887F5;
      }
      .tip{
        width: 86px;
        line-height: 24px;
        background: #EBF1FF;
        border-radius: 0px 8px 0px 8px;
        font-size: 14px;
        font-weight: 400;
        color: #3887F5;
        text-align: center;
        position: absolute;
        right: 0;
        top: 0;
      }
    }
    .title-bar{
      margin: 27px 0 11px;
      .left{
        font-size: 16px;
        font-weight: 500;
        color: #000000;
      }
      .search-item{
        font-size: 14px;
        font-weight: 400;
        color: #999999;
        & + .search-item{
          margin-left: 24px;
        }
      }
    }
    .list-wr{
      background: #FFFFFF;
      border-radius: 8px 8px 8px 8px;
      padding: 0 16px;
      .list-item{
        padding: 16px 0;
        border-bottom: 0.5px solid #eee;
        &:last-child{
          border-bottom: none;
        }
        .label{
          font-size: 14px;
          font-weight: 400;
          color: #333333;
        }
        .num{
          font-size: 16px;
          font-weight: 500;
          color: #22BF7E;
          &.up{
            color: #22BF7E;
          }
          &.down{
            color: #EB4738;
          }
        }
        .date{
          font-size: 12px;
          font-weight: 400;
          color: #999999;
          margin-top: 5px;
        }
      }
    }
    .no-data{
      padding-top: 25px;
      text-align: center;
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      .img{
        width: 240px;
        height: 180px;
        margin-bottom: 16px;
      }
    }
  }
  .type-wr{
    border-radius: 16px 16px 0px 0px;
    background: #FFFFFF;
    padding: 0 16px;
    .title{
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      line-height: 46px;
      text-align: center;
      margin-bottom: 16px;
      border-bottom: 1px solid #eee;
    }
    .con{
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-gap: 12px;
      .con-item{
        border-radius: 68px 68px 68px 68px;
        border: 1px solid #EEEEEE;
        box-sizing: border-box;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: 36px;
        text-align: center;
        &.active{
          color: #3887F5;
          border-color: #3887F5;
        }
      }
    }
    .btn{
      line-height: 44px;
      background: #3887F5;
      border-radius: 68px 68px 68px 68px;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      color: #FFFFFF;
      margin-top: 24px;
    }
  }
  .illustrate-wr{
    padding: 24px 16px;
    .title{
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      margin-bottom: 12px;
    }
    .con{
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      margin-bottom: 24px;
    }
  }
}
</style>
