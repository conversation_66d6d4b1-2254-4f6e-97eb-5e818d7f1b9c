<template>
  <div class="tab-container">
    <div
      id="search-bar"
      :style="[tabsStyle]"
      class="search-bar flex"
    >
      <div
        :class="{active: show3}"
        class="search-item"
        @click="handleClick('show3')"
      >
        {{ cityInfo.name || '全国' }}
        <span
          :class="{active: show3 }"
          class="icon2 iconfont icon-xiala"
        />
      </div>
      <div
        :class="{active: show1}"
        class="search-item"
        @click="handleClick('show1')"
      >
        {{ caseCollaborationInfo.label }}
        <span
          :class="{active: show1 }"
          class="icon2 iconfont icon-xiala"
        />
      </div>
      <div
        :class="{active: show2}"
        class="search-item"
        @click="handleClick('show2')"
      >
        {{ caseAssistanceInfo.label }}
        <span
          :class="{active: show2 }"
          class="icon2 iconfont icon-xiala"
        />
      </div>
    </div>
    <!--   案件协作 -->
    <popup-select
      :contentWrapperStyle="contentWStyle"
      :list="caseCollaborationInfoList"
      :show.sync="show1"
      :value="caseCollaborationInfo.value"
      activeStyle="color:#3887F5"
      @handleSelect="changeCaseCollaborationInfo"
    />
    <!-- 协助状态   -->
    <popup-select
      :contentWrapperStyle="contentWStyle"
      :list="caseAssistanceList"
      :show.sync="show2"
      :value="caseAssistanceInfo.value"
      activeStyle="color:#3887F5"
      @handleSelect="changeCaseTypeInfo"
    />
    <popup-location
      :contentWrapperStyle="contentWStyle"
      :show.sync="show3"
      nationwide
      @setCity="setCity"
    />
  </div>
</template>

<script>
import PopupLocation from "@/pages/index/components/popup-location/index.vue";
import PopupSelect from "@/pages/index/components/popupSelect.vue";
import { COOPERATION_LIST, COOPERATION_STATUS } from "@/enum";
import { pxToRpx } from "@/libs/tools";

export default {
  components: { PopupSelect, PopupLocation },
  props: {
    localStatus: {
      type: Number,
      default: 0
    },
  },
  data() {
    return {
      show1: false,
      show2: false,
      show3: false,
      /* 案件协作 选中信息*/
      caseCollaborationInfo: COOPERATION_LIST[0],
      /* 协作状态信息*/
      caseAssistanceInfo: COOPERATION_STATUS[0],
      /* 城市信息*/
      cityInfo: {
        name: "全国"
      },
      /* 协作状态*/
      caseAssistanceList: COOPERATION_STATUS,
      /* 案件协作*/
      caseCollaborationInfoList: COOPERATION_LIST,
      contentWStyle: {
        top: pxToRpx(41)
      },
    };
  },
  computed: {
    /** 是否显示弹窗 */
    showPopup() {
      return (
        this.show1 ||
          this.show2 ||
          this.show3
      );
    },
    /** 顶部导航的样式 */
    tabsStyle() {
      if (!this.showPopup) return {};

      return uni.$u.addStyle({
        top: 0,
        right: 0,
        left: 0,
        position: "fixed"
      });
    }
  },
  created() {},
  methods: {
    // 案源线索子tab切换
    handleClick(type){
      if(this[type]) return this[type] = false;
      this.show1 = false;
      this.show2 = false;
      this.show3 = false;
      this[type] = true;
    },
    // 城市选择
    setCity(city) {
      console.log(city);
      this.cityInfo = city;
      this.dataChange();
    },
    // 数据变化时重新请求数据
    dataChange(){
      const data =  {
        status: this.caseAssistanceInfo.value,
        type: this.caseCollaborationInfo.value,
        /* {name: "石家庄", code: 130100, provinceName: "河北省", province: 130000*/
        regionCode: this.cityInfo.code || "",
        provinceCode: this.cityInfo.province || ""
      };
      this.$emit("searchChange", data);
    },
    // 协作状态选择
    changeCaseTypeInfo(item){
      this.caseAssistanceInfo = item;
      this.dataChange();
    },
    // 案件协作选择
    changeCaseCollaborationInfo(item){
      this.caseCollaborationInfo = item;
      this.dataChange();
    }
  }
};
</script>

<style lang="scss" scoped>
.tab-container{
  background: #F5F5F7;
  .tab-bar{
    padding: 0 24px;
    display: flex;
    .tab-item{
      flex: 1;
      height: 44px;
      line-height: 44px;
      text-align: center;
      font-size: 14px;
      font-weight: 400;
      color: #999;
      &.active{
        color: #000000;
        font-weight: 500;
        position: relative;
        &:after{
          content: '';
          position: absolute;
          left: 50%;
          bottom: 0;
          transform: translateX(-50%);
          width: 14px;
          height: 3px;
          background: #3887F5;
          border-radius: 70px;
        }
      }
    }
  }
  .search-bar{
    padding: 0 12px;
    position: relative;
    z-index: 2;
    background: #F5F5F7;
    .search-item{
      flex: 1;
      height: 41px;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      display: flex;
      align-items: center;
      justify-content: center;
      &.active{
        color: #3887F5;
      }
      .icon2{
        transition: all 0.3s;
        margin-left: 3px;
        font-size: 12px;
        &.active {
          transform: rotate(180deg);
        }
      }
      .icon3{
        font-size: 16px;
      }
    }
  }
  .info-bar{
    font-size: 12px;
    font-weight: 400;
    color: #333333;
    padding: 0 20px;
    height: 36px;
    .tag{
      line-height: 14px;
      background: linear-gradient(109deg, #FFB759 0%, #FF7721 100%);
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      text-align: center;
      color: #fff;
      font-size: 10px;
      font-weight: 600;
      margin-right: 4px;
      padding: 0 4px;
    }
    .sw-con{
      .text{
        font-size: 12px;
        font-weight: 400;
        color: #999999;
        margin-right: 10px;
      }
    }
  }
  .sub-wrapper{
    padding: 10px 0;
  }
  .sub-bar{
    width: 343px;
    height: 36px;
    background: #EEEEEE;
    border-radius: 8px 8px 8px 8px;
    margin:0 auto;
    box-sizing: border-box;
    padding: 2px;
    .sub-item{
      flex: 1;
      font-size: 12px;
      font-weight: 400;
      color: #666666;
      text-align: center;
      line-height: 32px;
      border-radius: 6px 6px 6px 6px;
      &.active{
        background: #FFFFFF;
      }
    }
  }
}
</style>
