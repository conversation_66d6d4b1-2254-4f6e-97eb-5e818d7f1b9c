<template>
  <div>
    <u-sticky>
      <slot />
      <div class="p-[12px] pb-[10px] relative overflow-hidden bg-[#F5F5F7]">
        <img
          class="block w-full h-[96px]"
          src="@/pages/find-lawyer/imgs/<EMAIL>"
          alt=""
        >
        <div
          class="absolute text-[12px] text-[#999999] flex items-center bottom-[12px] left-[46px] w-[210px] h-[21px] bg-[linear-gradient(_90deg,_#DAEAFF_0%,_rgba(255,255,255,0)_100%)] rounded-[12px]"
          @click="TheNumberOfGroupsIsExplainedPopupState=true"
        >
          <img
            class="w-[11px] pl-[6px] h-[11px] pr-[4px]"
            src="@/pages/find-lawyer/imgs/<EMAIL>"
            alt=""
          >
          进群次数<span
            class="text-[#3887F5] pl-[4px]"
          >{{ getLawyerGroupConfig.remainingBenefitCount }}次</span>
        </div>
      </div>
      <group-search-tab-bar @searchChange="handleSearch" />
    </u-sticky>
    <div>
      <div
        v-if="isArrNull(list)&&isRequest"
        class="flex items-center justify-center pt-[24px]"
      >
        <img
          alt=""
          class="w-[240px] h-[216px]"
          src="../../sub/imgs/<EMAIL>"
        >
      </div>
      <div>
        <div
          v-for="i in list"
          :key="i.id"
          class="px-[12px] pt-[12px]"
        >
          <group-lawyer-item :data="i" />
        </div>
      </div>
    </div>
    <img
      class="w-[66px] h-[66px] fixed bottom-[24px] right-[24px]"
      src="@/pages/find-lawyer/imgs/<EMAIL>"
      alt=""
      @click.stop="toPublishCompanionGroupRecord"
    >
    <the-number-of-groups-is-explained-popup v-model="TheNumberOfGroupsIsExplainedPopupState" />
  </div>
</template>

<script>
import GroupSearchTabBar from "@/pages/find-lawyer/components/GroupSearchTabBar.vue";
import TheNumberOfGroupsIsExplainedPopup from "@/pages/find-lawyer/components/TheNumberOfGroupsIsExplainedPopup.vue";
import GroupLawyerItem from "@/pages/find-lawyer/components/GroupLawyerItem.vue";
import { toPublishCompanionGroupRecord } from "@/libs/turnPages";
import { bindOnHook } from "@/libs/hooks";
import { isArrNull } from "@/libs/basics-tools";
import { communityGroupPage } from "@/api/communityGroup";

export default {
  name: "GroupList",
  components: { GroupLawyerItem, TheNumberOfGroupsIsExplainedPopup, GroupSearchTabBar },
  data() {
    return {
      /* 进群次数说明 */
      TheNumberOfGroupsIsExplainedPopupState: false,
      query: {
        currentPage: 1,
        pageSize: 10,
      },
      isRequest: false,
      isLastPage: false,
      list: []
    };
  },
  computed: {
    /* 获取进群权益 */
    getLawyerGroupConfig(){
      return this.$store.getters["user/getLawyerGroupConfig"];
    }
  },
  mounted() {
    const parent = bindOnHook.call(this, "onReachBottom", this.onReachBottomCallback);
    const parentPullDownRefresh = bindOnHook.call(this, "onPullDownRefresh", this.onPullDownRefreshCallback);
    this.$on("hook:destroyed", () => {
      parent.$off("hook:onReachBottom", this.onReachBottomCallback);
      parentPullDownRefresh.$off("hook:onPullDownRefresh", this.onPullDownRefreshCallback);
    });
  },
  methods: {
    isArrNull,
    toPublishCompanionGroupRecord,
    onReachBottomCallback(){
      this.query.currentPage++;
      this.getList();
    },
    onPullDownRefreshCallback(){
      console.log("refresh");
      this.resetData();
      this.getList().finally(() => {
        uni.stopPullDownRefresh();
      });
    },
    /* 搜索 */
    handleSearch(data) {
      this.query = {
        ...this.query,
        ...data
      };
      this.resetData();
      this.getList();
    },
    /** 重置数据 */
    resetData() {
      this.list = [];
      this.query.currentPage = 1;
      this.isLastPage = false;
      this.isRequest = false;
    },
    getList() {
      if (this.isLastPage) {
        return Promise.resolve();
      }
      return communityGroupPage(this.query).then(res => {
        this.list = [...this.list, ...res.data.records];
        if (this.list.length >= res.data.total) {
          this.isLastPage = true;
        }
        return this.list;
      })
        .finally(() => {
          this.isRequest = true;
        });
    }
  }
};
</script>

<style scoped lang="scss">

</style>