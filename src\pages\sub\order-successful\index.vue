<template>
  <login-layout>
    <div class="position-relative">
      <img
        alt=""
        class="block w-[375px] h-[158px]"
        src="@/pages/sub/order-successful/img/iq6ipj.png"
      >
      <div class="flex items-center absolute top-[50px] left-[30px]">
        <div class="text-[14px] text-[#FFFFFF] mr-[4px] shrink-0">
          请前往法临律师端APP上传成单案例及获取奖励
        </div>
        <i
          class="iconfont icon-yiwen w-[15px] h-[15px] text-[#FFFFFF] shrink-0 block"
          @click="popup=true"
        />
      </div>
      <div class="bg-[#F5F5F7] rounded-tl-[16px] rounded-br-none rounded-tr-[16px] rounded-bl-none absolute top-[94px] left-0 w-full box-border px-[16px] pt-[29px]">
        <div
          v-for="item in list"
          :key="item.id"
          class="flex"
        >
          <img
            alt=""
            class="block w-[14px] h-[168px] mr-[12px]"
            src="@/pages/sub/order-successful/img/<EMAIL>"
          >
          <order-info-card
            :data="item"
            @click="toOrderSuccessfulDetails(item)"
          />
        </div>
      </div>
      <u-safe-bottom />
      <order-successful-popup v-model="popup" />
    </div>
  </login-layout>
</template>

<script>
import OrderInfoCard from "@/pages/sub/order-successful/components/OrderInfoCard.vue";
import { goodNewsPage } from "@/api";
import OrderSuccessfulPopup from "@/pages/sub/order-successful/components/OrderSuccessfulPopup.vue";
import { toOrderSuccessfulDetails } from "@/libs/turnPages";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { whetherToLogIn } from "@/libs/tools";
import LoginLayout from "@/components/login/LoginLayout.vue";

export default {
  name: "OrderSuccessful",
  components: { LoginLayout, USafeBottom, OrderSuccessfulPopup, OrderInfoCard },
  data(){
    return {
      popup: false,
      list: [],
      pageParams: {
        /** 页码 */
        currentPage: 1,
        /** 每页条数 */
        pageSize: 20,
      },
    };
  },
  mounted() {
    this.getList();
  },
  onReachBottom() {
    this.scrollToLower();
  },
  methods: {
    toOrderSuccessfulDetails(data){
      whetherToLogIn(() => {
        toOrderSuccessfulDetails(data);
      });
    },
    scrollToLower() {
      if (
        this.pageParams.currentPage * this.pageParams.pageSize >=
          this.pageTotal
      )
        return;

      this.pageParams.currentPage++;
      this.getList();
    },
    getList(){
      goodNewsPage({
        ...this.pageParams
      }).then(({ data = {} }) => {
        this.list = [...this.list, ...data.records];

        this.pageTotal = data.total || 0;
      });
    }
  }
};
</script>
