<template>
  <div class="date-select">
    <float-popup
      :contentWrapperStyle="contentWrapperStyle"
      :show="show"
      @maskClick="$emit('update:show', false)"
    >
      <div
        v-for="item in list"
        :key="item.value"
        class="slide-item flex flex-space-between flex-align-center"
        @click="handleClick(item)"
      >
        <span :style="[item.value===value?getActiveStyle:{}]">{{ item.label }}</span> <span
          v-show="item.value===value"
          class="iconfont icon-a-zhengquewancheng"
        />
      </div>
    </float-popup>
  </div>
</template>

<script>

import FloatPopup from "@/pages/index/components/floatPopup.vue";

export default {
  name: "PopupSelect",
  components: { FloatPopup },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => ([])
    },
    value: {
      type: [Number, String],
      default: ""
    },
    contentWrapperStyle: {
      type: Object,
      default: () => ({})
    },
    activeStyle: {
      type: [Object, String],
      default: () => ({})
    }
  },
  computed: {
    getActiveStyle() {
      return uni.$u.addStyle(this.activeStyle);
    }
  },
  methods: {
    handleClick(item){
      this.$emit("input", item.value);
      this.$emit("handleSelect", item);
      this.$emit("update:show", false);
    },
  },
};
</script>

<style lang="scss" scoped>
.date-select{
  .slide-item{
    padding: 0 16px;
    line-height: 44px;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    .iconfont{
      font-size: 20px;
      color: #3887F5;
    }
  }
}

</style>
