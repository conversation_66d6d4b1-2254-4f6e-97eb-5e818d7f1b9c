<template>
  <login-layout>
    <div class="h-[144px] relative">
      <img
        alt=""
        class="block w-full h-full absolute left-0 top-0 z-[-1]"
        src="@/pages/sub/imgs/<EMAIL>"
      >
      <div
        class="flex items-center justify-center  text-[#FFFFFF]"
        @click.stop="changeInstructionsState"
      >
        <p class="font-bold text-[24px]">
          问答广场
        </p>
        <i class="iconfont icon-shuoming1 text-[16px] ml-[4px]" />
      </div>
      <div class="pt-[5px] flex items-center justify-between px-[28px] opacity-[0.6]">
        <div class="flex items-center justify-center">
          <i class="iconfont icon-yanxuan !text-[16px] text-[#FFFFFF]" />
          <p class="text-[12px] text-[#FFFFFF] ml-[2px]">
            真实免费问题
          </p>
        </div>
        <div class="flex items-center justify-center">
          <i class="iconfont icon-a-yinsi3x1 !text-[16px] text-[#FFFFFF]" />
          <p class="text-[12px] text-[#FFFFFF] ml-[2px]">
            隐私保护
          </p>
        </div>
        <div class="flex items-center justify-center">
          <i class="iconfont icon-a-xiadan3x !text-[16px] text-[#FFFFFF]" />
          <p class="text-[12px] text-[#FFFFFF] ml-[2px]">
            追问后可引导付费下单
          </p>
        </div>
      </div>
    </div>
    <div class="bg-[#F5F5F7] min-h-[20vh] overflow-hidden mt-[-71px] rounded-tl-[16px] rounded-br-none rounded-tr-[16px] rounded-bl-none">
      <search-tab-bar @searchChange="handleSearch" />
      <div class="px-[12px]">
        <!--  没有数据的时候     -->
        <div
          v-if="isArrNull(list)&&isRequest"
          class="flex items-center justify-center pt-[24px]"
        >
          <img
            alt=""
            class="w-[240px] h-[216px]"
            src="@/pages/sub/imgs/<EMAIL>"
          >
        </div>
        <div
          v-for="i in list"
          :key="i.id"
          class="mb-[12px] bg-[#FFFFFF] rounded-[12px] px-[16px] pt-[16px] pb-[8px]"
          @click.stop="toDetail(i)"
        >
          <div class="flex justify-between pb-[12px]">
            <div class="bg-[#FCF1ED] rounded-[4px] text-[12px] text-[#D36D64] px-[4px] py-[2px]">
              {{ i.typeLabel }}
            </div>
            <div class="text-[12px] text-[#999999]">
              <p v-if="isNull(i.rushAnswerCount)||i.rushAnswerCount===0">
                暂无回复，试试帮助Ta？
              </p>
              <div
                v-else
                class=" flex items-center"
              >
                <p>
                  文字回复
                </p><p class="text-[#3887F5]">
                  ({{ i.rushAnswerCount }}/{{ i.rushAnswerMaxCount }})
                </p>
              </div>
            </div>
          </div>
          <div class=" pb-[12px]">
            <div class="text-[15px] text-[#333333] text-ellipsis-2">
              {{ i.detail }}
            </div>
          </div>
          <div class="pb-[16px] text-[12px] text-[#8A8D94]">
            {{ i.createTimeDesc }} ｜ {{ i.regionName }}
          </div>
          <div class="flex justify-end  pt-[8px] border-0 border-solid border-t border-[#EEEEEE]">
            <div
              class="w-[66px] flex items-center justify-center h-[28px] bg-[#3887F5] rounded-[18px] text-[14px] text-[#FFFFFF]"
            >
              去回复
            </div>
          </div>
        </div>
      </div>
    </div>
    <sub-tab-bar
      :list="QA_TAB_BAR_LIST"
      activeValue="square"
    />
    <qa-instructions :show.sync="instructionsState" />
  </login-layout>
</template>

<script>
import SearchTabBar from "@/pages/sub/qa-square/components/searchTabBar.vue";
import SubTabBar from "@/pages/sub/components/TabBar/index.vue";
import { QA_TAB_BAR_LIST } from "@/enum/qa";
import { qaMessagePageList } from "@/api";
import { isArrNull, isNull } from "@/libs/basics-tools";
import { toAnswerDetails } from "@/libs/turnPages";
import LoginLayout from "@/components/login/LoginLayout.vue";
import QaInstructions from "@/pages/sub/components/QaInstructions/index.vue";

export default {
  name: "QaSquare",
  components: { QaInstructions, LoginLayout, SubTabBar, SearchTabBar },
  data() {
    return {
      QA_TAB_BAR_LIST,
      query: {
        qaMessageRequestType: 3,
        payStatus: 0,
        currentPage: 1,
        pageSize: 10
      },
      isRequest: false,
      isLastPage: false,
      instructionsState: false,
      list: []
    };
  },
  onReachBottom() {
    this.query.currentPage++;
    this.getList();
  },
  onPullDownRefresh() {
    console.log("refresh");
    this.resetData();
    this.getList().finally(() => {
      uni.stopPullDownRefresh();
    });
  },
  methods: {
    isArrNull,
    isNull,
    changeInstructionsState(){
      this.instructionsState = !this.instructionsState;
    },
    /* 搜索 */
    handleSearch(data) {
      this.query  = {
        ...this.query,
        ...data
      };
      this.resetData();
      this.getList();
    },
    /** 重置数据 */
    resetData() {
      this.list = [];
      this.query.currentPage = 1;
      this.isLastPage = false;
      this.isRequest = false;
    },
    getList(){
      if (this.isLastPage) {
        return Promise.resolve();
      }

      uni.showLoading({
        title: "请稍等 正在加载"
      });

      return qaMessagePageList(this.query).then(res => {
        this.list = [...this.list, ...res.data.records];
        if (this.list.length >= res.data.total) {
          this.isLastPage = true;
        }
        return this.list;
      })
        .finally(() => {
          this.isRequest = true;

          uni.hideLoading();
        });
    },
    toDetail(data) {
      toAnswerDetails({
        id: data.id
      });
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
