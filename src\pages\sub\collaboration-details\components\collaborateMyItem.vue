<template>
  <div class="p-[16px] bg-[#FFFFFF] rounded-[8px]">
    <p
      v-if="data.status!==9"
      class="font-bold text-[16px] text-[#333333]"
    >
      接单律师
    </p>
    <div v-if="data.status!==9">
      <!-- 无律师接单       -->
      <div
        v-if="isArrNull(jdLawyer)"
        class="flex items-center pt-[16px]"
      >
        <img
          alt=""
          class="w-[48px] h-[48px] rounded-[31px] shrink-0"
          src="../imgs/iSpt.gif"
        >

        <p class="pl-[8px] text-[14px] text-[#333333]">
          {{ urgencyTip }}
        </p>
      </div>
      <!--  有律师接单       -->
      <div v-else>
        <div
          v-for="i in jdLawyer"
          :key="i.jdLawyerId"
          class="flex pt-[16px] items-center"
          @click="toLawyerHome(i)"
        >
          <img
            :src="i.jdLawyerAvatar"
            alt=""
            class="w-[48px] shrink-0 h-[48px] rounded-[37px]"
          >
          <div class="flex-1 pl-[8px]">
            <p class="font-bold text-[16px] text-[#333333]">
              {{ i.jdLawyerName }}
            </p>
            <div class="flex items-center pt-[8px]">
              <img
                alt=""
                class="w-[12px] h-[12px]"
                src="../imgs/icon.png"
              >
              <p class="pl-[2px] text-[12px] text-[#9F6310]">
                已通过法临官方认证
              </p>
            </div>
          </div>
          <div
            class="flex-shrink-0 flex items-center"
          >
            <i
              class="iconfont icon-a-Component1 text-[#22BF7E] !text-[24px]"
              @click.stop="call(i)"
            />
            <i
              class="iconfont ml-[20px] icon-btn_icon_weixin text-[#22BF7E] !text-[24px]"
              @click.stop="copyWeiChat(i)"
            />
          </div>
        </div>
      </div>
      <div
        class="text-[14px] text-[#3887F5] mt-[14px] flex items-center justify-center text-center"
        @click="toFindLawyer"
      >
        {{ tipText }} >
      </div>
    </div>
    <!-- 被关闭 -->
    <div
      v-if="data.status===9"
      class="flex items-center"
    >
      <img
        alt=""
        class="w-[48px] h-[48px]"
        src="../imgs/<EMAIL>"
      >
      <p class="text-[14px] text-[#333333] pl-[8px]">
        您的诉求已被关闭，可在协作广场重新发布
      </p>
    </div>
  </div>
</template>

<script>
import { isArrNull } from "@/libs/basics-tools";
import { toFindLawyer, toLawyerHome } from "@/libs/turnPages";
import { lawyerCollaborationGetPhoneByJdRecordId } from "@/api/collaboration";
import { URGENCY_TYPE } from "@/enum";

export default {
  name: "CollaborateMyItem",
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    /* 接单律师*/
    jdLawyer(){
      return this.data.jdLawyerList || [];
    },
    urgencyTip(){
      return URGENCY_TYPE.find(
        (item) => item.value === Number(this.data.urgency)
      ).showTip;
    },
    /** 提示文案 */
    tipText(){
      // 1.待接单、2.已匹配（已接单）、9.已关闭
      if(this.data.status === 2)
        return "诉求已完成匹配，若问题未解决，可主动找律师";

      if(isArrNull(this.jdLawyer)) {
        return "不想等待了，直接主动找律师";
      }

      return `已有${this.jdLawyer.length}个律师响应，若问题未解决，可主动找律师`;
    }
  },
  methods: {
    toFindLawyer,
    isArrNull,
    toLawyerHome(data){
      toLawyerHome({
        id: data.jdLawyerId
      });
    },
    call(data){
      lawyerCollaborationGetPhoneByJdRecordId({
        jdRecordId: data.id
      }).then(res => {
        const data = res.data;
        console.log(data);
        uni.makePhoneCall({
          phoneNumber: data.lawyerPhone
        });
      });
    },
    copyWeiChat(data){
      this.$emit("copyWeiChat", data);
    }
  },

};
</script>

<style lang="scss" scoped>

</style>
