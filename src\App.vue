<script>
import { getUserTokenStorage } from "@/libs/token.js";
import store from "@/store";
import { getCurrentPageRoute } from "@/libs/turnPages";
import { setUserTokenStorage } from "./libs/token.js";
import { lastOnlineTime } from "@/api";
import { isNotLogin } from "@/libs/tools";
import { WebIM } from "@/plugins/im";
import { WS_READY_STATE } from "yoc-im-web";

/** 监听网络 */
function onNetworkStatusChange() {
  /** 错误时的提示 */
  function err() {
    uni.showToast({
      title: "请检查网络",
      icon: "none",
      duration: 2000,
    });
  }

  /** 判断网络状态 */
  function getNetworkType() {
    uni.getNetworkType({
      success: function (res) {
        if (res.networkType === "none") {
          err();
        }
      },
    });
  }

  // 监听网络状态变化
  uni.onNetworkStatusChange((res) => {
    // 如果网络断开，弹出提示
    if (!res.isConnected) {
      console.log("网络断开");
      err();
    } else {
      console.log("网络已连接");
      // 当网络恢复时，刷新页面
      uni.reLaunch({
        url: getCurrentPageRoute().fullPath,
      });
    }
  });

  // 监听跳转
  uni.addInterceptor("navigateTo", {
    success(e) {
      getNetworkType();
      store.dispatch("user/setUserInfo");
    },
  });
  // 监听关闭本页面跳转
  uni.addInterceptor("redirectTo", {
    success(e) {
      getNetworkType();
      store.dispatch("user/setUserInfo");
    },
  });
  // 监听tabBar跳转
  uni.addInterceptor("switchTab", {
    success(e) {
      getNetworkType();
      store.dispatch("user/setUserInfo");
    },
  });

  // 监听返回
  uni.addInterceptor("navigateBack", {
    success(e) {
      getNetworkType();
    },
  });
}

/** 用户最后在线时间 */
function setLastOnlineTime(){
  if (!isNotLogin()) {
    lastOnlineTime();
  }

  setTimeout(() => {
    setLastOnlineTime();
  }, 5000);
}

export default {
  data() {
    return {
      shigeto: 0,
      /* 隐藏显示页面标签的时间搓*/
      timeRubs: 0,
    };
  },
  onLaunch: function (option) {
    onNetworkStatusChange();
    setLastOnlineTime();

    const query = option.query || {};
    if(query.token){
      setUserTokenStorage(query.token);
    }

    let token = query.token || getUserTokenStorage();
    if (token) {
      store.dispatch("user/setToken", token);
      store.dispatch("user/setUserInfo");
    }
    const openid = uni.getStorageSync("openid");
    if (openid) store.dispatch("user/setOpenid", openid);
    /* 认证失败重新登录*/
    this.$ImEventBus.$on("onAuthenticationFailed", () => {
      if (this.shigeto < 5) {
        console.log("认证失败重新登录");
        this.$store.dispatch("im/userLogin");
      }
      this.shigeto++;
    });
  },
  onShow: function () {
    const handleTimeRubs = this.handleTimeRubs();
    console.log("是否隐藏页面状态过长", handleTimeRubs);
    if(handleTimeRubs || WebIM.conn.getWsReadystate() === WS_READY_STATE.CLOSED){
      let token = getUserTokenStorage();
      if (token) {
        // 登录才连接IM
        store.dispatch("im/userLogin");
      }
    }
    
    // 处理分享完成回调
    store.dispatch("share/handleShareComplete");
    
    console.log("App Show");
  },
  onHide: function () {
    console.log("App Hide");
    this.timeRubs = Date.now();
  },
  methods: {
    /* 页面隐藏时间 30s后刷新*/
    handleTimeRubs() {
      console.log((Date.now() - this.timeRubs) / 1000, this.timeRubs, "this.timeRubs");
      /* 超过30s才执行*/
      return this.timeRubs === 0 || (Date.now() - this.timeRubs) / 1000 >= 30;

    },
  }
};
</script>

<style lang="scss">
/*每个页面公共css */
/*每个页面公共css */
@import "~@/uview-ui/index.scss";

/* 设置全局背景色 */
page {
  background-color: #f5f5f7;
}
</style>
