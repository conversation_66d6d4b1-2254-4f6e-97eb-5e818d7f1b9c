<template>
  <!-- <div> -->
  <app-popup
    :closeOnClickOverlay="false"
    :safeAreaInsetBottom="false"
    :show="getShow"
    mode="center"
    round="16"
    @close="close"
  >
    <view class="file-wrap">
      <view class="text-box">
        <view class="success-tit">
          <img
            alt=""
            class="checked"
            src="./img/checked.png"
          >
          合同模板文件上传成功
        </view>
        <view class="text-desc">
          你可前往法临律师端APP查看
        </view>
      </view>
      <div class="flex">
        <div
          class="reset flex flex-align-center flex-space-center flex-1"
          @click="resetFun"
        >
          继续上传
        </div>
        <button
          app-parameter="wechatUploadContract"
          binderror="launchAppError"
          class="btn flex-1 flex flex-align-center flex-space-center "
          open-type="launchApp"
        >
          返回法临律师端
        </button>
      </div>
    </view>
  </app-popup>
  <!-- </div> -->
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";

export default {
  name: "BackAppPopup",
  components: { AppPopup },
  props: {
    show: {
      type: Boolean,
      default: false
    },
  },
  data () {
    return {
      indicatorDots: true,
      autoplay: true,
      interval: 3000,
      duration: 500
    };
  },
  computed: {
    getShow: {
      get () {
        return this.show;
      },
      set (value) {
        this.$emit("update:show", value);
      }
    }
  },
  methods: {
    close(){
      this.getShow = false;
    },
    /* 重置*/
    resetFun(){
      this.close();
      this.$emit("reset");
    },
    launchAppError (e) {
      console.log(e.detail.errMsg);
    }
  }

};
</script>

<style lang="scss" scoped>
.file-wrap{
  overflow: hidden;
  width: 311px;
}
.text-box{
  padding: 24px;
  text-align: center;
  border-bottom: 0.5px solid #f5f5f5;
}
.success-tit{
  font-weight: 500;
  color: #333333;
  font-size: 16px;
  margin-bottom: 13px;
}
  .checked{
    width: 24px;
    height: 24px;
    vertical-align: middle;
    margin-right: 4px;
  }
.text-desc{
  font-size: 14px;
  font-weight: 400;
  color: #666666;
}
.btn{
  width: 100%;
  height: 46px;
  border: none;
  padding: 0;
  font-size: 16px;
  font-weight: 400;
  color: #3887F5;
  outline: none;
  background-color: #FFFFFF;
  border-radius: 16px;
  &::after{
    border: none !important;
  }
}
.reset{
  border-right: 0.5px solid #EEEEEE;
  font-size: 16px;
  font-weight: 400;
  color: #333333;
}
</style>
