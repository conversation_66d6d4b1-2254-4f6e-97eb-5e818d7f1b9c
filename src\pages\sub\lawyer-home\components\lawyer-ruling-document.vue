<template>
  <div class="w-full">
    <div class="flex flex-1 flex-align-center">
      <div class="flex flex-1 flex-space-between">
        <div class="content-item-time">
          {{ data.judgeDate || data.refereeTime }}
        </div>
        <div class="content-item-name flex flex-align-center">
          <img
            v-if="data.courtName || data.lawCourt"
            alt=""
            class="content-item-name-icon"
            src="../img/right-home-icon.png"
          >{{ data.courtName || data.lawCourt || '' }}
        </div>
      </div>
    </div>
    <div class="content-item-text text-ellipsis-2">
      {{ data.title }}
    </div>
  </div>
</template>

<script>
export default {
  name: "LawyerRulingDocument",
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="scss" scoped>
.content-item {
  &-time {
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding: 2px 4px;
    background: #EBF3FE;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    font-size: 10px;
    font-weight: 400;
    color: #3887F5;
    height: 18px;
  }

  &-name {
    font-size: 12px;
    font-weight: 400;
    color: #999999;

    &-icon {
      width: 14px;
      height: 14px;
      margin-right: 5px;
    }
  }

  &-text {
    margin-top: 8px;
    font-size: 13px;
    font-weight: 400;
    color: #333333;
    line-height: 15px;
  }
}
</style>
