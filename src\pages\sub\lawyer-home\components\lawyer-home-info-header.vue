<template>
  <div class="header-text flex flex-align-center flex-space-between">
    <div class="flex flex-align-center">
      <p class="left-icon" />
      <p class="left-text">
        {{ title }}
      </p>
    </div>
    <p
      v-if="more"
      class="right-text flex flex-align-center"
      @click="handleMore"
    >
      全部
      <img
        alt=""
        class="header-text-img"
        src="../img/arrow.png"
      >
    </p>
  </div>
</template>

<script>
export default {
  name: "LawyerHomeInfoHeader",
  props: {
    title: {
      type: String,
      default: "",
    },
    more: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    handleMore(){
      this.$emit("more");
    },
  }
};
</script>

<style lang="scss" scoped>
.header-text {
  width: 100%;
  padding-top: 24px;
  padding-bottom: 12px;

  &-img {
    width: 16px;
    height: 16px;
  }
  .left-icon {
    width: 3px;
    height: 14px;
    background: #3887F5;
    margin-right: 8px;
    border-radius: 20px;
    opacity: 1;
  }

  .left-text {
    font-size: 16px;
    font-weight: bold;
    color: #333333;
  }

  .right-text {
    font-size: 13px;
    font-weight: 400;
    color: #666666;
  }
}
</style>
