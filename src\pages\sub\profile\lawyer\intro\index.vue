<template>
  <div class="page">
    <template v-if="isEmpty">
      <div class="title flex flex-align-end">
        <div class="title__left">
          系统默认
        </div>
        <div class="title__right">
          （若需修改相关信息，请联系客服）
        </div>
      </div>
      <div class="info">
        {{ tipsText }}
      </div>
      <div class="mg-tp-16 textarea">
        <u-textarea
          v-model="inputValue"
          :maxlength="200"
          border="none"
          count
          placeholder="请输入您的补充个人简介，将会补充在系统默认生成的个人简介后"
          placeholderStyle="color: #fff;font-size: 14px;"
        />
      </div>
      <div
        class="button"
        @click="confirm"
      >
        确认
      </div>
      <div
        class="service"
        @click="turnToServiceCenterPage"
      >
        <i class="iconfont icon-kefu2" />
        <div>联系客服</div>
      </div>
    </template>
    <template v-else>
      <div class="textarea">
        <u-textarea
          v-model="inputValue"
          :maxlength="200"
          border="none"
          count
          placeholder="请输入您的补充个人简介，将会补充在系统默认生成的个人简介后"
          placeholderStyle="color: #fff;font-size: 14px;"
        />
      </div>
      <div
        class="button"
        @click="confirm"
      >
        确认
      </div>
      <div
        class="service"
        @click="turnToServiceCenterPage"
      >
        <i class="iconfont icon-kefu2" />
        <div>联系客服</div>
      </div>
    </template>
  </div>
</template>

<script>
import UTextarea from "@/uview-ui/components/u--textarea/u--textarea.vue";
import store from "@/store";
import { turnToServiceCenterPage } from "@/libs/turnPages";

export default {
  name: "ProfileLawyerIntro",
  components: { UTextarea },
  data() {
    return {
      inputValue: "",
    };
  },
  computed: {
    params() {
      return store.state.lawyerProfile.lawyerProfile;
    },
    /** 律师信息 */
    profileInfo() {
      return store.getters["user/getUserInfo"];
    },
    /** 职业年限 */
    workTime() {
      return this.profileInfo.workTime;
    },
    /** 律所 */
    lawyerOffice() {
      return this.profileInfo.lawyerOffice;
    },
    workField() {
      return this.profileInfo.workFields
        ?.map((item) => item.workFieldName)
        .join("、");
    },
    /** 提示文案 */
    tipsText() {
      const workTime = `执业年限${this.workTime || 0}年，`;
      const lawyerOffice = this.lawyerOffice
        ? `执业律所为${this.lawyerOffice}，`
        : "";
      const workField = this.workField ? `擅长类型为${this.workField}，` : "";

      return `${workTime}${lawyerOffice}${workField}有相关的法律咨询可通过一对一咨询服务向我提问`;
    },
    /**
     * value是否为空
     * ! 这里如果值为空，则说明没有填过个人简介，那么就需要显示默认文案，并且在点击确定的时候将默认文案和输入的文案拼接起来
     * @return {boolean}
     */
    isEmpty() {
      return !this.params.lawyerProfile;
    },
  },
  mounted() {
    this.inputValue = this.params.lawyerProfile;
  },
  methods: {
    turnToServiceCenterPage,
    confirm() {
      if (this.isEmpty) {
        this.params.lawyerProfile =
          this.tipsText + (this.inputValue ? this.inputValue : "");
      } else {
        this.params.lawyerProfile = this.inputValue ? this.inputValue : "";
      }

      uni.navigateBack();
    },
  },
};
</script>

<style>
page {
  background-color: #fff;
}
</style>

<style lang="scss" scoped>
@import "../style/input";

.page {
  padding: 12px 16px;
}

.title {
  &__left {
    font-size: 16px;
    font-weight: bold;
    color: #333333;
  }

  &__right {
    font-size: 12px;
    font-weight: 400;
    color: #f78c3e;
  }
}

.info {
  margin-top: 12px;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
}

.service {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  font-weight: 400;
  color: #3887f5;
  margin-top: 22px;

  .iconfont {
    font-size: 24px;
    margin-right: 12px;
  }
}
</style>
