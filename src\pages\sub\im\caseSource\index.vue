<template>
  <div>
    <u-divider
      v-if="isEnd"
      text="已经是最后一条了"
    />
    <u-safe-bottom />
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import UDivider from "@/uview-ui/components/u-divider/u-divider.vue";
import { onReachBottomPage } from "@/libs/tools.js";
import { orderCenterCaseSourceV2Clues } from "@/api/im.js";
import { requestUserInfoCallback } from "@/store/modules/user.js";
import { turnToCaseSourceDetails } from "@/libs/turnPages.js";

export default {
  name: "Index",
  components: { UDivider, USafeBottom },
  data() {
    return {
      list: [],
      onReachBottomPage: {},
      isEnd: false,
      aboutPriceShow: false,
      caseSource: {}
    };
  },
  computed: {
    getUserInfo(){
      return this.$store.getters["user/getUserInfo"];
    },
  },
  onLoad(){
    requestUserInfoCallback(() => {
      this.onReachBottomPage = onReachBottomPage((data) => {
        return  orderCenterCaseSourceV2Clues({
          provinceCode: this.getUserInfo.workProvinceCode,
          ...data
        });
      });
      this.requestList();
    });
  },
  methods: {
    requestList(){
      this.onReachBottomPage().then(({ list, paginationState }) => {
        this.isEnd = paginationState.isEnd;
        this.list = [...this.list, ...list];
      });
    },
    cardClick(data, type) {
      turnToCaseSourceDetails({ id: data.caseSourceV2Id, type });
    },
    questionClick(data){
      this.aboutPriceShow = true;
      this.caseSource = data;
    }
  },
  onReachBottom(){
    this.requestList();
  }
};
</script>

<style scoped lang="scss">
.card{
  padding: 16px 12px 0;
}
</style>
