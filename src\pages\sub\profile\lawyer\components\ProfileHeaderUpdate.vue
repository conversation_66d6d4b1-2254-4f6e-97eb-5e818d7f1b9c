<template>
  <div>
    <app-avatar-cropper
      :value="value"
      @input="(e) => $emit('input', e)"
    >
      <template #default="{ header }">
        <div class="card flex flex-space-between flex-align-center">
          <div>
            <div class="title">
              头像 <span class="mg-l-4 text-eb4738">*</span>
            </div>
            <div class="tip">
              头像会用于平台展示哦，请上传形象照
            </div>
          </div>
          <div class="flex flex-align-center">
            <img
              v-if="header"
              :src="header"
              alt=""
              class="header"
            >
            <img
              v-else
              alt=""
              class="header"
              src="../img/1.png"
            >
            <i class="iconfont icon-erjiyoujiantou" />
          </div>
        </div>
        <u-line />
      </template>
    </app-avatar-cropper>
  </div>
</template>

<script>
import AppAvatarCropper from "@/components/AppComponents/AppAvatarCropper/index.vue";
import ULine from "@/uview-ui/components/u-line/u-line.vue";

export default {
  name: "ProfileHeaderUpdate",
  components: { ULine, AppAvatarCropper },
  props: {
    value: {
      type: String,
      default: "",
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  width: 311px;
  box-sizing: border-box;
  padding: 12px 0;
}

.title {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
}

.tip {
  margin-top: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
}

.header {
  width: 40px;
  height: 40px;
  display: block;
  border-radius: 50%;
}

.iconfont {
  margin-left: 4px;
  font-size: 16px;
  color: #999999;
}
</style>
