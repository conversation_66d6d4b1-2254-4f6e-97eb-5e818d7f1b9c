import { requestCommon } from "@/libs/axios";

export const createOrder = (data) => requestCommon.post("/order/v2/orderLawyer/createOrder", data);
export const orderPay = (data) => requestCommon.post("/order/v2/orderLawyer/pay", data);
export const pageCoupon = (data) => requestCommon.post("/paralegal/lawyer/pageCoupon", data);
export const caseSourceGrabOrder = (data) => requestCommon.post("/paralegal/caseSourceServerV2/v2/grabOrder", data);

