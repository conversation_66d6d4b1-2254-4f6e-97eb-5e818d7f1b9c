<template>
  <scroll-view
    class="w-screen h-screen"
    scrollY
    @scrolltolower="scrollToLower"
  >
    <div class="mg-tp-12">
      <div
        v-for="item in caseList"
        :key="item.id"
        class="document-item"
        @click="goDetail(item)"
      >
        <div>
          <div class="document-item__content text-ellipsis-2">
            {{ item.caseBasicInfo || "" }}
          </div>
          <div class="mg-tp-8 flex flex-space-between flex-align-center">
            <div class="document-item__time">
              {{ item.refereeTime || "" }}
            </div>
            <div class="document-item__office flex flex-align-center">
              <img
                alt=""
                class="document-item__office__img"
                src="../img/right-home-icon.png"
              >
              {{ item.lawCourt || "" }}
            </div>
          </div>
        </div>
      </div>
      <app-divider />
    </div>
  </scroll-view>
</template>

<script>
import LawyerRulingDocument from "@/pages/sub/lawyer-home/components/lawyer-ruling-document.vue";
import AppDivider from "@/components/AppComponents/AppDivider/index.vue";
import { toLawyerCaseDetail } from "@/libs/turnPages.js";
import { lawyerLawCasePageAll } from "@/api/findlawyer.js";

export default {
  name: "LawyerHomeWonderfulCase",
  components: { AppDivider, LawyerRulingDocument },
  data() {
    return {
      /** 案件列表 */
      caseList: [],
      /** 案件总数 */
      caseTotal: 0,
      /** 案件页码 */
      casePage: 1,
      /** 案件每页条数 */
      caseSize: 20,
    };
  },
  onLoad() {
    this.getCaseList();
  },
  methods: {
    /** 获取案件列表 */
    getCaseList() {
      const params = {
        currentPage: this.casePage,
        pageSize: this.caseSize,
      };

      lawyerLawCasePageAll(params).then((res) => {
        this.caseList = this.caseList.concat(res.data.records || []);
        this.caseTotal = res.data.total || 0;
      });
    },
    /** 滚动到底部 */
    scrollToLower() {
      if (this.casePage * this.caseSize >= this.caseTotal) return;
      this.casePage++;
      this.getCaseList();
    },
    goDetail(item) {
      toLawyerCaseDetail(item);
    },
  },
};
</script>

<style lang="scss" scoped>
.document-item {
  width: 351px;
  margin: 0 auto 12px auto;
  padding: 12px;
  background: #ffffff;
  border-radius: 8px;
  opacity: 1;
  box-sizing: border-box;

  &__content {
    font-size: 14px;
    font-weight: 400;
    color: #333333;
  }

  &__time {
    background: #ebf3fe;
    border-radius: 4px;
    opacity: 1;
    font-size: 10px;
    font-weight: 400;
    color: #3887f5;
    padding: 2px 4px;
  }

  &__office {
    font-size: 12px;
    font-weight: 400;
    color: #999999;

    &__img {
      width: 14px;
      height: 14px;
      margin-right: 4px;
    }
  }
}
</style>
