<template>
  <app-popup
    :show="show"
    mode="center"
    :round="16"
    closeable
    :safeAreaInsetBottom="false"
    @cancel="show = false"
  >
    <div class="w-[311px] box-border">
      <div class="px-[24px] pt-[24px] pb-[16px]">
        <div class="text-center mb-[12px]">
          <p class="font-bold text-[16px] text-[#333333]">
            绑定微信账号
          </p>
        </div>

        <div class="mb-[12px]">
          <p class="text-[14px] text-[#666666]">
            为了确保平台的功能完整性及您的使用体验，请填写您的真实微信账号。
          </p>
        </div>

        <div class="mb-[24px]">
          <div
            class="flex items-center bg-[#F5F5F7] rounded-[8px] px-[12px] py-[10px]"
          >
            <i
              class="iconfont icon-a-Frame1321316632 !text-[20px] !text-[#07C160] mr-[8px]"
            />
            <input
              v-model="wechatId"
              class="flex-1 bg-transparent text-[14px] text-[#333333] outline-none"
              maxlength="20"
              placeholder="请填写您的微信号"
              placeholder-style="color: #999999;font-weight: 400;"
            >
            <i
              v-if="wechatId"
              class="iconfont icon-cuowu !text-[14px] !text-[#999999] ml-[8px]"
              @click="clearInput"
            />
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div
            class="w-[84px] h-[44px] rounded-[68px] border-[1px] border-solid border-[#CCCCCC] text-[16px] text-[#333333] flex items-center justify-center"
            @click="handleCancel"
          >
            取消
          </div>
          <div
            class="w-[167px] h-[44px] bg-[#3887F5] rounded-[68px] text-[16px] text-[#FFFFFF] flex items-center justify-center"
            :class="{ 'opacity-60': !wechatId }"
            @click="handleConfirm"
          >
            确定
          </div>
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { lawyerUpdateWechatId } from "@/api/user";
import store from "@/store";

export default {
  name: "WechatBindPopup",
  components: { AppPopup },
  data() {
    return {
      wechatId: "",
    };
  },
  computed: {
    show: {
      get() {
        return this.$store.getters["popupState/getWechatBindPopupState"];
      },
      set(val) {
        this.$store.commit("popupState/SET_WECHAT_BIND_POPUP_STATE", val);
      },
    },
  },
  destroyed() {
    this.show = false;
  },
  methods: {
    clearInput() {
      this.wechatId = "";
    },
    handleClose() {
      this.show = false;
    },
    handleCancel() {
      this.show = false;
    },
    async handleConfirm() {
      const wechatId = this.wechatId.trim();

      if (!wechatId) {
        uni.showToast({
          title: "请填写微信账号",
          icon: "none",
        });
        return;
      }

      try {
        await lawyerUpdateWechatId({ wechatId });

        uni.showToast({
          title: "绑定成功",
          icon: "none",
        });

        this.show = false;
      
        // 执行绑定成功后的回调
        const wechatBindCallback = this.$store.getters["user/getWechatBindCallback"];

        wechatBindCallback?.();

        store.dispatch("user/setUserInfo");
      } 
      catch (error) {
        console.error(error);
      }
    },
  },
};
</script>