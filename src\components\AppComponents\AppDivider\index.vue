<template>
  <div>
    <div class="divider">
      <div class="divider-text">
        {{ text }}
      </div>
    </div>
    <u-safe-bottom />
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "AppDivider",
  components: { USafeBottom },
  props: {
    text: {
      type: String,
      default: "已经是最后一条了",
    },
  },
};
</script>

<style lang="scss" scoped>
.divider {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 351px;
  height: 20px;
  box-sizing: border-box;
  margin: 12px auto 0 auto;
}

.divider-text {
  font-size: 14px;
  font-weight: 400;
  color: #666666;
}
</style>
