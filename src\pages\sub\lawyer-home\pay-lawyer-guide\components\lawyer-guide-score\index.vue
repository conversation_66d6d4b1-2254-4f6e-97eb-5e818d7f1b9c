<template>
  <div
    v-if="total"
    class="law-score"
  >
    <div class="score-container">
      <div class="flex flex-align-end flex-space-between">
        <div class="score-title">
          律师评价 <span class="score-title-num">({{ total }}条)</span>
        </div>
        <div
          class="show-all flex flex-align-center flex-space-center"
          @click="toAllEvaluate"
        >
          全部
          <img
            alt=""
            class="show-all-icon"
            src="../../../img/arrow.png"
          >
        </div>
      </div>
      <div>
        <div
          v-for="item in evaluateList"
          :key="item.id"
          class="score-item"
        >
          <lawyer-home-score-item :data="item" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getEvaluatePage, scoreCountLawyers } from "@/api/lawyer.js";
import LawyerHomeScoreItem from "@/pages/sub/lawyer-home/components/lawyer-home-score/lawyer-home-score-item.vue";
import { toLawyerHomeComment } from "@/libs/turnPages.js";
import { isObjNull } from "@/libs/basics-tools.js";

export default {
  name: "LawyerGuideScore",
  components: { LawyerHomeScoreItem },
  props: {
    lawyerInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      /** 平均分 */
      average: 0,
      /** 服务条数 */
      total: 0,
      /** 服务评价列表 */
      evaluateList: [],
    };
  },
  watch: {
    lawyerInfo: {
      handler(val) {
        if (isObjNull(val)) return;

        this.getScore();
        this.getEvaluatePage();
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    /** 获取得分 */
    getScore() {
      if (!this.lawyerInfo.id) return;

      scoreCountLawyers({ lawyerId: this.lawyerInfo.id }).then(({ data }) => {
        this.average = data || 0;
      });
    },
    /** 获取律师的评价列表 */
    getEvaluatePage() {
      if (!this.lawyerInfo.id) return;

      getEvaluatePage({
        lawyerId: this.lawyerInfo.id,
        pageSize: 5,
      }).then(({ data }) => {
        this.total = data.total;
        this.evaluateList = data.records || [];
      });
    },
    /** 查看全部 */
    toAllEvaluate() {
      toLawyerHomeComment({
        id: this.lawyerInfo.id,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.law-score {
  background-color: #fff;
  border-radius: 16px;
}

.score-title {
  font-size: 16px;
  font-weight: bold;
  padding-top: 12px;

  &-num {
    margin-left: 6px;
    font-weight: 400;
    font-size: 14px;
    color: #999999;
  }
}

.score-container {
  padding: 0 16px;
}

.score-item {
  margin-top: 20px;
  padding-bottom: 12px;

  &:not(:last-child) {
    border-bottom: 1px solid #f5f5f5;
  }
}

.show-all {
  font-size: 14px;
  font-weight: 400;
  color: #333333;

  &-icon {
    width: 16px;
    height: 16px;
  }
}
</style>
