<template>
  <!-- <div> -->
  <app-popup
    :show="getShow"
    bgColor="transparent"
    mode="center"
    round="16"
    @close="close"
  >
    <view class="file-wrap">
      <p class="text-center tit">
        文件上传说明
      </p>
      <view class="uni-margin-wrap">
        <swiper
          :autoplay="autoplay"
          :duration="duration"
          :indicatorDots="indicatorDots"
          :interval="interval"
          circular
          class="swiper"
        >
          <swiper-item>
            <view class="swiper-item ">
              <img
                alt=""
                class="img"
                src="img/<EMAIL>"
              >
            </view>
          </swiper-item>
          <swiper-item>
            <view class="swiper-item ">
              <img
                alt=""
                class="img"
                src="img/<EMAIL>"
              >
            </view>
          </swiper-item>
          <swiper-item>
            <view class="swiper-item ">
              <img
                alt=""
                class="img"
                src="img/<EMAIL>"
              >
            </view>
          </swiper-item>
          <swiper-item>
            <view class="swiper-item ">
              <img
                alt=""
                class="img"
                src="img/<EMAIL>"
              >
            </view>
          </swiper-item>
        </swiper>
      </view>
      <img
        alt=""
        class="close"
        src="img/close.png"
        @click="close"
      >
    </view>
  </app-popup>
  <!-- </div> -->
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";

export default {
  name: "FilePopup",
  components: { AppPopup },
  props: {
    show: {
      type: Boolean,
      default: false
    },
  },
  data () {
    return {
      indicatorDots: true,
      autoplay: true,
      interval: 3000,
      duration: 500
    };
  },
  computed: {
    getShow: {
      get () {
        return this.show;
      },
      set (value) {
        this.$emit("update:show", value);
      }
    }
  },
  methods: {
    close(){
      this.getShow = false;
    }
  }

};
</script>

<style lang="scss" scoped>

.file-wrap{
  font-size: 16px;
  color: #333333;
  width: 311px;
  height: 485px;
  box-sizing: border-box;
  background: transparent;
  border-radius: 16px;
  overflow: hidden;
  .tit{
    font-weight: 500;
    background: #FFFFFF;
    padding-top: 24px;
    padding-bottom: 12px;
  }
  .close{
    width: 24px;
    height: 24px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
  }
}
.swiper {
  height: 365px;
  background: #FFFFFF;
  padding: 0 24px 24px;
  border-radius:0 0 16px 16px;
  .img{
    width: 100%;
    height: 331px;
  }
}


</style>
