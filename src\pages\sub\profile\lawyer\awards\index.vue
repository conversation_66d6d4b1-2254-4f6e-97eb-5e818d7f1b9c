<template>
  <div class="textarea">
    <u-textarea
      v-model="inputValue"
      border="none"
      height="125"
      placeholder="请在此处输入内容"
    />
    <div
      class="button"
      @click="confirm"
    >
      确认
    </div>
  </div>
</template>

<script>
import UTextarea from "@/uview-ui/components/u--textarea/u--textarea.vue";
import store from "@/store";

export default {
  name: "ProfileLawyerAwards",
  components: { UTextarea },
  data() {
    return {
      inputValue: "",
    };
  },
  computed: {
    params() {
      return store.state.lawyerProfile.lawyerProfile;
    },
  },
  mounted() {
    this.inputValue = this.params.honor;
  },
  methods: {
    confirm() {
      this.params.honor = this.inputValue;

      uni.navigateBack();
    },
  },
};
</script>

<style>
page {
  background-color: #fff;
}
</style>

<style lang="scss" scoped>
@import "../style/input";

.textarea {
  padding: 12px 16px;
}
</style>
