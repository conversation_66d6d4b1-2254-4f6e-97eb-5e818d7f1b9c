import { requestCommon } from "@/libs/axios";
import { wrapRequest } from "@/libs/tools";

export const commonLoginRequest = (data) => {
  return requestCommon.post("/paralegal/user/wechat/login", data);
};

/** 根据任务RuleTypeCode获取奖励 */
export const lawyerGetAwardByRuleTypeCode = wrapRequest((data) => {
  return requestCommon.post("/paralegal/lawyer/getAwardByRuleTypeCode", data);
}, {
  isLogin: true,
});


/** 获取任务列表 */
export const lawyerGetTaskByRuleTypeCode = (data) => {
  return requestCommon.post("/paralegal/lawyer/getTaskByRuleTypeCode", data);
};
