<template>
  <div>
    <div
      class="fixed z-[1000] left-0 bottom-0 w-[375px] bg-[#FFFFFF] rounded-tl-[12px] rounded-br-none rounded-tr-[12px] rounded-bl-none opacity-100"
    >
      <div>
        <div class="flex justify-between items-center my-[8px] mx-[16px]">
          <div
            class="w-[164px] h-[39px] bg-[#22BF7E] rounded-[68px] text-[15px] text-[#FFFFFF] flex items-center justify-center font-bold"
            @click="toLawyerIm"
          >
            <div
              v-if="graphicConsultation.servicePrice"
              class="mr-[4px]"
            >
              ¥{{
                (graphicConsultation.servicePrice
                  ? graphicConsultation.servicePrice
                  : 0) | amountFilter
              }}
            </div>
            <div>图文咨询</div>
            <img
              alt=""
              class="w-[16px] h-[16px] block"
              src="../img/1.png"
            >
          </div>
          <div
            class="w-[164px] h-[39px] bg-[#3887F5] rounded-[68px] text-[15px] text-[#FFFFFF] flex items-center justify-center font-bold"
            @click="consultNow"
          >
            <div
              v-if="phoneConsultation.servicePrice"
              class="mr-[4px]"
            >
              ¥{{
                (phoneConsultation.servicePrice
                  ? phoneConsultation.servicePrice
                  : 0) | amountFilter
              }}
            </div>
            <div>电话咨询</div>
            <img
              alt=""
              class="w-[16px] h-[16px] block"
              src="../img/1.png"
            >
          </div>
        </div>
      </div>
      <u-safe-bottom />
    </div>
    <app-placeholder :height="55" />
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { toLawyerFakeImBefore } from "@/libs/tools";
import AppPlaceholder from "@/components/AppComponents/AppPlaceholder/index.vue";
import { mapState } from "vuex";

export default {
  name: "LawyerHomeButton",
  components: { AppPlaceholder, USafeBottom },
  props: {
    lawyerInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    ...mapState({
      phoneAndGraphicServiceInfo: (state) =>
        state.lawyerHome.phoneAndGraphicServiceInfo,
    }),
    /** 图文咨询 */
    graphicConsultation() {
      return this.phoneAndGraphicServiceInfo?.[2]?.[0] || {};
    },
    /** 电话咨询 */
    phoneConsultation() {
      return this.phoneAndGraphicServiceInfo?.[1]?.[0] || {};
    },
  },
  methods: {
    /** 点击立即咨询按钮 */
    consultNow() {
      toLawyerFakeImBefore({
        lawyerInfo: this.lawyerInfo,
        itemClassType: 1,
      });
    },
    /** 点击图文咨询 */
    toLawyerIm() {
      toLawyerFakeImBefore({
        lawyerInfo: this.lawyerInfo,
        itemClassType: 2,
      });
    },
  },
};
</script>
