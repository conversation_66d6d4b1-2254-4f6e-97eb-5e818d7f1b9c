<template>
  <div class="position-relative">
    <div class="w-[351px] bg-[#FFFFFF] rounded-[8px] box-border mx-auto">
      <scroll-view
        class="w-full"
        scrollX
        @scroll="handleScroll"
      >
        <div class="flex items-center">
          <div
            v-for="(item, index) in list"
            :key="index"
            class="w-[76px] box-border shrink-0 my-[16px]"
            @click="handleClick(index)"
          >
            <img
              :src="item.icon"
              alt=""
              class="block w-[36px] h-[36px] mx-auto"
            >
            <div class="text-[13px] text-[#333333] mt-[8px] text-center">
              {{ item.name }}
            </div>
          </div>
        </div>
      </scroll-view>
    </div>
    <div
      class="w-[14px] h-[3px] bg-[#ECECF0] rounded-full absolute bottom-[3px] box-border absolute-x-center"
    >
      <div
        :style="{
          left: scrollLeft + 'px',
        }"
        class="w-[7px] h-[3px] bg-[#3887F5] rounded-full absolute"
      />
    </div>
  </div>
</template>

<script>
import { pxToScreenPx } from "@/libs/tools";
import {
  toCollaboration,
  toFindLawyer,
  toHandleTool,
  toLawyerFeeCalculator,
  turnToServiceCenterPage
} from "@/libs/turnPages";

export default {
  name: "ServiceScroll",
  data() {
    return {
      list: [
        {
          name: "案源广场",
          icon: require("@/pages/index/imgs/ym8z7g.png"),
          click: () => {
            turnToServiceCenterPage();
          },
        },
        {
          name: "异地协作",
          icon: require("@/pages/index/imgs/k9q07x.png"),
          click: () => {
            toCollaboration();
          },
        },
        {
          name: "找律师",
          icon: require("@/pages/index/imgs/zgt1ew.png"),
          click: () => {
            toFindLawyer();
          },
        },
        {
          name: "法律法规",
          icon: require("@/pages/index/imgs/Frame8436.png"),
          click: () => {
            toHandleTool();
          },
        },
        {
          name: "企业查询",
          icon: require("@/pages/index/imgs/Frame2463.png"),
          click: () => {
            toHandleTool();
          },
        },
        {
          name: "律师费计算",
          icon: require("@/pages/index/imgs/Frame8437.png"),
          click: () => {
            toLawyerFeeCalculator();
          },
        },
      ],
      scrollLeft: 0,
    };
  },
  methods: {
    handleScroll(e) {
      const scrollWidth = pxToScreenPx(351);

      const inspect = pxToScreenPx(7);

      const percentage =
        e.target.scrollLeft / (e.target.scrollWidth - scrollWidth);

      this.scrollLeft = inspect * percentage;
    },
    handleClick(index) {
      this.list[index].click();
    },
  },
};
</script>
