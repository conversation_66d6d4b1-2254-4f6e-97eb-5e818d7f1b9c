<template>
  <div
    v-if="isShow"
    class="statistics"
  >
    <div class="statistics-header">
      <div class="header-text flex flex-align-center flex-space-between">
        <p class="left-text">
          案件统计
        </p>
        <p
          class="right-text flex flex-align-center"
          @click="toAll"
        >
          全部
          <img
            alt=""
            class="header-text-img"
            src="../../img/arrow.png"
          >
        </p>
      </div>
      <div class="charts-container">
        <lawyer-home-tab
          :list="tabList"
          @change="changeTabOne"
        />
        <div class="mg-tp-20">
          <!-- 案件标的 -->
          <subject-case
            v-if="current === INDICATOR_NAME.caseAmt"
            :data="all[INDICATOR_NAME.caseAmt]"
          />
          <!-- 近期案件数 -->
          <number-recent-cases
            v-if="current === INDICATOR_NAME.recentCase"
            :data="all[INDICATOR_NAME.recentCase]"
          />
          <!-- 案由分布 -->
          <case-action
            v-if="current === INDICATOR_NAME.caseReason"
            :data="all[INDICATOR_NAME.caseReason]"
          />
          <!-- 常去法院 -->
          <frequent-court
            v-if="current === INDICATOR_NAME.courtName"
            :data="all[INDICATOR_NAME.courtName]"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LawyerHomeTab from "@/pages/sub/lawyer-home/components/lawyer-home-case-statistics/lawyer-home-tab.vue";
import CaseAction from "@/pages/sub/lawyer-home/components/lawyer-home-case-statistics/case-action.vue";
import NumberRecentCases from "@/pages/sub/lawyer-home/components/lawyer-home-case-statistics/number-recent-cases.vue";
import SubjectCase from "@/pages/sub/lawyer-home/components/lawyer-home-case-statistics/subject-case.vue";
import caseStatistics from "@/pages/sub/lawyer-home/mixins/caseStatistics.js";
import FrequentCourt from "@/pages/sub/lawyer-home/components/lawyer-home-case-statistics/frequent-court.vue";
import { toLawyerHomeCaseStatistics } from "@/libs/turnPages.js";
import { isArrNull, isObjNull } from "@/libs/basics-tools.js";
import { INDICATOR_NAME } from "@/libs/config.js";

export default {
  name: "LawyerHomeCaseStatistics",
  components: {
    FrequentCourt,
    SubjectCase,
    NumberRecentCases,
    CaseAction,
    LawyerHomeTab,
  },
  mixins: [caseStatistics],
  props: {
    lawyerInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    isShow() {
      return !isArrNull(this.all[INDICATOR_NAME.caseReason]);
    },
  },
  watch: {
    lawyerInfo: {
      handler(value) {
        if (isObjNull(value)) return;
        this.getCaseStatisticsAll(this.tabList).then((datas) => {
          if (!isObjNull(datas)) {
            this.all = datas;
          }
        });
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    /** 点击全部按钮 */
    toAll() {
      toLawyerHomeCaseStatistics({
        id: this.lawyerInfo.id,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.statistics {
  width: 343px;
  margin: 0 auto;
  border-radius: 8px;
  overflow: hidden;
}

.statistics-header {
  background: linear-gradient(180deg, #fff3e0 0%, #ffffff 93px);
  opacity: 1;
}

.header-text {
  padding: 12px 16px;

  &-img {
    width: 16px;
    height: 16px;
  }

  .left-text {
    font-size: 16px;
    font-weight: bold;
    color: #333333;
  }

  .right-text {
    font-size: 13px;
    font-weight: 400;
    color: #666666;
  }
}

.charts-container {
  padding: 8px 12px 20px 12px;
  min-height: 252px;
}
</style>
