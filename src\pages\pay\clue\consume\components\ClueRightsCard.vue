<template>
  <div class="card position-relative">
    <img
      :src="backgroundImage"
      alt=""
      class="background-image"
    >
    <!-- 右上角的角标 -->
    <img
      :src="src"
      alt=""
      class="card-right-icon"
      mode="heightFix"
    >
    <div class="title">
      {{ data.goodsName }}
    </div>
    <div class="content">
      <div class="content-item">
        <div class="content-item__title">
          剩余抢单条数
        </div>
        <div class="content-item__count">
          {{ data.remainClueNum }}
        </div>
      </div>
      <div class="content-item pd-lt-25">
        <div class="content-item__title">
          已抢单条数
        </div>
        <div class="content-item__count">
          {{ data.usedClueNum }}
        </div>
      </div>
    </div>
    <div class="bottom flex flex-space-between flex-align-center">
      <div class="time">
        {{ isActive ? `有效期至：${data.validTime}` : '激活开始计算有效期' }}
      </div>
      <div
        v-if="showDetail && isActive"
        class="detail"
        @click="dataDetail"
      >
        <div>消耗明细</div>
        <div class="iconfont icon-erjiyoujiantou detail__icon" />
      </div>
    </div>
  </div>
</template>

<script>
import { turnToConsumeDetailPage } from "@/libs/turnPages";
import { saveClueDetail } from "@/pages/pay/clue/consume/js";

export default {
  name: "ClueRightsCard",
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    /** 是否显示消耗明细按钮 */
    showDetail: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  computed: {
    /** 角标图片 */
    src() {
      const src = [
        require("@/pages/pay/clue/consume/img/1(2).png"),
        require("@/pages/pay/clue/consume/img/1.png"),
        require("@/pages/pay/clue/consume/img/1(1).png"),
      ];

      return src[this.cluePackStatus];
    },
    /** 背景图片 */
    backgroundImage() {
      const backgroundImage = [
        require("@/pages/pay/clue/consume/img/bg.png"),
        require("@/pages/pay/clue/consume/img/bg1.png"),
      ];

      if (this.cluePackStatus === 0) {
        return backgroundImage[1];
      }

      return backgroundImage[0];
    },
    /** 线索包状态（0:已过期，1:未过期（正常使用），2.未激活） */
    cluePackStatus() {
      return Number(this.data.cluePackStatus);
    },
    /** 是否激活 */
    isActive() {
      return this.cluePackStatus !== 2;
    },
  },
  methods: {
    dataDetail() {
      saveClueDetail(this.data);

      turnToConsumeDetailPage({
        id: this.data.lawyerCluePackId,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  width: 351px;
  box-sizing: border-box;
  border-radius: 12px;
  opacity: 1;
  margin: 0 auto;
  padding: 16px;

  &-right-icon {
    position: absolute;
    height: 18px;
    top: 0;
    right: 0;
  }
}

.title {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
}

.content {
  margin-top: 12px;
  display: grid;
  grid-template-columns: 1fr 1fr;

  &-item {
    &:first-child {
      border-right: 1px dashed rgba(255, 255, 255, 0.4);
    }

    &__title {
      font-size: 12px;
      font-weight: 400;
      color: #ffffff;
      opacity: 0.6;
    }

    &__count {
      margin-top: 4px;
      font-size: 28px;
      font-weight: bold;
      color: #ffffff;
    }
  }
}

.bottom {
  margin-top: 14px;

  .time {
    padding: 2px 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 23px;
    opacity: 1;
    color: #fff;
    font-size: 10px;
  }

  .detail {
    display: flex;
    align-items: center;
    margin-left: 12px;
    font-size: 12px;
    font-weight: 400;
    color: #ffffff;

    &__icon {
      font-size: 10px;
    }
  }
}
</style>
