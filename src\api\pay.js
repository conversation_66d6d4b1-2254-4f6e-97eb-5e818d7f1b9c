import { requestCommon } from "@/libs/axios";
/* 支付*/
export const orderLawyerPay = (params) => requestCommon.post("/order/v2/orderLawyer/pay", params); // 支付v2

/** 案源反馈无效 */
export const caseSourceServerV2Feedback = (params) => requestCommon.post("/paralegal/caseSourceServerV2Feedback/feedback", params); // 支付v2

/** 案源反馈无效详情 */
export const caseSourceServerV2FeedbackDetail = (params) => requestCommon.post("/paralegal/caseSourceServerV2Feedback/feedbackDetail", params); //
