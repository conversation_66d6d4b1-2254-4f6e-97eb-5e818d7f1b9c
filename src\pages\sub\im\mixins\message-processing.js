import { http, WS_MESSAGE_TYPE, WS_READY_STATE, WS_SEND_INSTRUCTION } from "yoc-im-web";
import { isArrNull, isNull, isObj, isObjNull } from "@/libs/basics-tools.js";
import {
  imDirectivesFlushedCodes,
  imDirectivesFlushedCodesNeedUpdate,
  imMessageType,
  personLessMsgCardMap
} from "@/pages/sub/im/enum/imEnum.js";
import {
  keywordReplace,
  parseJSON,
  parseTime,
  parseTimeDiffToday,
  scrollToTarget,
  setPaginationTool
} from "@/libs/tools.js";
import { dataDetailList } from "@/api";
import { getServerInfo } from "@/api/im.js";
import {
  caseInfoSymbol,
  conversationIdSymbol, conversationInfoSymbol,
  sessionIMThisSymbol,
  updateCaseInfoSymbol
} from "@/pages/sub/im/mixins/case-info-state.js";
import { getImToken, getUserTokenStorage } from "@/libs/token.js";

let sensitiveWordsMap = [];
let timer = null;
export const messageProcessing = {
  provide(){
    return{
      [updateCaseInfoSymbol]: this.getCaseInfo,
      [caseInfoSymbol]: () => {
        return this.caseInfo;
      },
      [conversationIdSymbol]: () => {
        return this.conversationId;
      },
      [sessionIMThisSymbol]: () => {
        return this;
      },
      [conversationInfoSymbol]: () => {
        return {
          info: this.conversationInfo,
          currentUserInfo: this.currentUserInfo,
          getCurrentToken: this.getCurrentToken,
          getOtherUserInfo: this.getOtherUserInfo,
          getOtherToken: this.getOtherToken,
          businessType: this.businessType,
          getExt: this.getExt,
          isLawyerHelp: this.isLawyerHelp
        };
      }
    };
  },
  data() {
    return {
      /* 分页工具*/
      paginationTool: {
        next: () => {}
      },
      /* im工具*/
      imTools: {},
      /* 所有的历史消息*/
      messageHistoryList: [],
      /* 会话信息*/
      conversationInfo: {
        userInfos: {}
      },
      /* 案源信息*/
      caseInfo: {},
      /* 会话id*/
      conversationId: "",
      /* 更改消息列表 */
      updateMessageList: {}
    };
  },
  computed: {
    getExt() {
      return this.conversationInfo.ext || { businessType: "" };
    },
    /* ! 这个id是案源id 因为mixins里面有个案源id 是获取案源后的 所以这个文件里面都要用这个id*/
    businessId() {
      return this.getExt.businessId;
    },
    /* 这个是服务列表 代表当前im是什么类型的 */
    businessType(){
      return this.getExt.businessType;
    },
    /* 判断是不是律师互助 12 */
    isLawyerHelp() {
      return this.getExt.businessType === 12;
    },
    /* 当前用户信息*/
    currentUserInfo() {
      return this.conversationInfo.userInfos[getImToken()] || {};
    },
    /* 当前用户token*/
    getCurrentToken() {
      return getImToken();
    },
    //   对方token
    getOtherToken() {
      return Object.keys(this.conversationInfo.userInfos).find(item => item !== this.getCurrentToken);
    },
    getOtherUserInfo() {
      return this.conversationInfo.userInfos[this.getOtherToken] || {};
    }
  },
  mounted() {
    console.log("message-processing mounted");
    if (isArrNull(sensitiveWordsMap)) {
      dataDetailList({ groupCode: "IM_WORDS_SENSITIVE" }).then(({ data = [] }) => {
        console.log(data, "KEY_IM_WORDS_SENSITIVE");
        sensitiveWordsMap = data.map(item => item.label);
      });
    }
    // 收到文本消息
    this.$ImEventBus.$onRewrite("onTextMessage", (message) => {
      console.log("this.$ImEventBus?.onTextMessage 收到文本消息");
      if (message.toC === this.conversationId) {
        console.log(message, "this.$ImEventBus?.onTextMessage 收到文本消息");
        this.onMessagePushList(message);
        this.$emit("hadnleMessage", message);
        this.$emit("handleTextMessage", message);
      }
    });

    // 收到图片消息
    this.$ImEventBus.$onRewrite("onPictureMessage", (message) => {
      if (message.toC === this.conversationId) {
        console.log(message, "this.$ImEventBus?.onPictureMessage 收到图片消息");
        this.onMessagePushList(message);
        this.$emit("hadnleMessage", message);
        this.$emit("handlePictureMessage", message);
      }
    });

    // 收到文件消息
    this.$ImEventBus.$onRewrite("onFileMessage", (message) => {
      if (message.toC === this.conversationId) {
        console.log(message, "this.$ImEventBus?.onFileMessage 收到文件消息");
        this.onMessagePushList(message);
        this.$emit("hadnleMessage", message);
        this.$emit("handleFileMessage", message);
      }
    });

    // 收到自定义消息  && message.from === this.lawyerInfo.token
    this.$ImEventBus.$onRewrite("onCustomMessage", (message) => {
      if (message.toC === this.conversationInfo.id) {
        console.log(message, "this.$ImEventBus?.$onRewrite 收到自定义消息");
        // const data = { ...message, lawyerInfo: this.lawyerInfo.info }
        this.onMessagePushList(message);
        this.$emit("handleCustomMessage", message);
      }
    });

    // 收到系统消息
    this.$ImEventBus.$onRewrite("onSystemMessage", (message) => {
      if (message.toC === this.conversationId) {
        console.log(message, "this.$ImEventBus?.$onRewrite 收到系统消息");
        this.onMessagePushList(message);
        this.$emit("handleSystemMessage", message);
      }
    });

    // 收到消息已读
    this.$ImEventBus.$onRewrite("onMessage", (message) => {
      console.log("收到消息重新更新好友列表onMessage", message);
      /* 判断收到的消息 是否是当前打开的会话 清除当前会话的未读消息*/
      if (message.toC === this.conversationId) {
        http.clearConverRead({ cId: this.conversationId });
      }
    });

    // 收到系统消息
    this.$ImEventBus.$onRewrite("onOtherCommandMessage", ({ data: message }) => {
      if (message.toC === this.conversationInfo.id) {
        const data = { ...message };
        let msg = {};
        try {
          msg = JSON.parse(data.msg);
        } catch (e) {
          msg = {};
        }
        /* 收到这些自定自动刷新案源信息*/
        if (imDirectivesFlushedCodes.indexOf(msg.code) > -1) {
          this.getCaseInfo();
        }
        /* 收到这些消息要更新对应的卡片 */
        if(imDirectivesFlushedCodesNeedUpdate.indexOf(msg.code) > -1){
          this.updateMessageList[msg.msgId] = msg;
          this.updateMessageCard();
        }

        console.log(data, "this.$ImEventBus?.$onRewrite 收到指令消息");
        this.$emit("handleOtherCommandMessage", msg);
      }
    });
  },
  onLoad({ id }) {
    this.conversationId = Number(id);
    this.initLoadChatRoom();
  },
  async onPullDownRefresh() {
    // 如果滚动到顶部，触发加载更多聊天记录
    try {
      const msgId = this.messageHistoryList[0]?.msgId;
      await this.nextPage();
      uni.stopPullDownRefresh();
      if(isNull(msgId)) return;
      await this.$nextTick();
      this.scrollToBottom({
        timer: 100,
        duration: 20,
        selector: "#msg_" + msgId
      });
    }catch (e) {
      uni.stopPullDownRefresh();
      console.log(e, "onPullDownRefresh");
    }
  },
  onUnload() {
    console.log("onUnload");
    this.$ImEventBus.$offRewrite();
    this.$store.dispatch("im/unloadOpened");
    clearTimeout(timer);
  },
  methods: {
    /* 设置页面title*/
    setTitle() {
      const userInfos = Object.values(this.conversationInfo.userInfos || {});
      for(let x of userInfos) {
        if (x.token !== this.getCurrentToken) {
          uni.setNavigationBarTitle({
            title: x.name
          });
          return;
        }
      }
    },
    /* 获取案源信息*/
    getCaseInfo() {
      if (!this.businessId || this.isLawyerHelp) {
        this.caseInfo = {};
        return Promise.reject();
      }
      return getServerInfo(this.businessId).then(({ data = {} }) => {
        this.caseInfo = data;
        return data;
      });
    },

    initLoadChatRoom(){
      if (!this.conversationId) return false;
      /* im加载成功*/
      this.$store.dispatch("im/onOpened", () => {
        setTimeout(() => {
          this.loadChatRoom();
        }, 500);
      });
    },
    /* 加载聊天室*/
    loadChatRoom() {
      /* 点击后清除未读消息*/
      http.clearConverRead({ cId: this.conversationId });
      this.messageHistoryList = [];
      this.updateMessageList = {};
      console.log("im加载成功");
      /* 创建会话*/
      this.$WebIM.createSession(this.conversationId).then(async (wsTools) => {
        this.imTools = wsTools;
        this.conversationInfo = parseJSON(wsTools.getConversationInfo());
        console.log(this.conversationInfo, "this.conversationInfo");
        this.setTitle();
        this.getCaseInfo();
        this.getMessageHistoryList();
      });
    },
    /* 获取消息*/
    getMessageHistoryList() {
      return http.getConversationMessageHistory({
        cId: this.conversationId
      }).then((res) => {
        const data = Object.freeze(res.data);
        console.log("全部消息数量", data);
        this.paginationTool = setPaginationTool(res.data, 20);
        this.nextPage();
        this.scrollToBottom({
          timer: 200,
          duration: 10
        });
        return res;
      });
    },
    /* 下一页*/
    nextPage() {
      let list = [];
      try {
        list = parseJSON([...this.paginationTool.next()].reverse()).filter(item => this.filterMessageList(item));
        list = list.map((item, index) => ({
          ...this.setCustomMessage({ data: item, prevData: list[index - 1] }),
          ...item
        }));
      } catch (e) {
        console.log(e);
        return Promise.resolve();
      }
      console.log("最后一页状态", this.paginationTool.lastPageState);
      if (isArrNull(list)) {
        return Promise.resolve();
      }
      this.messageHistoryList = [...list, ...this.messageHistoryList];
      this.updateMessageCard();
      console.log(this.messageHistoryList);
      console.log("已经加载消息数量", this.messageHistoryList.length);
      return Promise.resolve();
    },
    scrollToBottom(data = {}) {
      timer = setTimeout(() => {
        scrollToTarget(data);
      }, data.timer || 800);
    },
    /* 新增消息追加*/
    onMessagePushList(message) {
      const pushState = this.filterMessageList(parseJSON(message));
      if (pushState) {
        const data = {
          ...message,
          ...this.setCustomMessage({ data: message, prevData: this.messageHistoryList[this.messageHistoryList.length - 1] })
        };
        this.messageHistoryList.push(data);
      }
    },
    /* 过滤下不需要的list 系统消息（msgType = 6）新增"showType"字段，跟customExts节点的"showType"显示规则一致（0->两端显示，1->C端显示，2->B端显示）*/
    filterMessageList(data) {
      if (data.msgType === WS_MESSAGE_TYPE.CUSTOM) {
        if (data.msg && data.msg.customExts && Number(data.msg.customExts.showType) === 1) {
          console.log("自定义消息过滤数据===========", data);
          return false;
        }
        // showImToken 需要哪方显示的imToken
        if (data.msg && data.msg.customExts.showImToken && data.msg.customExts.showImToken !== this.getCurrentToken) {
          console.log("自定义消息过滤数据===========imToken", data);
          return false;
        }
      } else if (data.msgType === WS_MESSAGE_TYPE.SYSTEM) {
        try {
          const currentData = data.msg;
          if (Number(currentData.showType) === 1) {
            console.log("系统消息过滤数据=========", data);
            return false;
          }
        } catch (e) {
          return true;
        }
      }
      return true;
    },
    /* 处理消息*/
    setCustomMessage({ data, prevData }) {
      return {
        noAvatar: this.personLessMsg(data),
        /* 当前比对上一个数据的时间*/
        intervalTime: this.getDate(data, prevData) ? parseTimeDiffToday(data.time) : ""
      };
    },
    // 不属于任何双方消息 -无头像
    personLessMsg(item) {
      if (item.msgType === WS_MESSAGE_TYPE.SYSTEM) return true;
      if (item.msgType === WS_MESSAGE_TYPE.CUSTOM) {
        const subType = item.msg.customExts.subType;
        const style = item.msg.customExts.style;
        if (subType === "speed_order") return true;
        return personLessMsgCardMap.indexOf(style) > -1;
      }
      return item.sendType === WS_SEND_INSTRUCTION.COMMAND;
    },
    /* 发送状态统一处理*/
    commonSendState(callbacks) {
      return {
        size: 20 * 1024 * 1024,
        onFileUploadSizeOverrun: () => {
          this.$message.error("发送文件或者图片最大20M");
        },
        onFileTypeNotAllow: () => {
          this.$message.error("文件类型不允许");
        },
        beforeSend: (data) => {
          const customData = { ...data };
          if (customData.msgType === WS_MESSAGE_TYPE.CUSTOM) {
            customData.msg = parseJSON(customData.msg);
          }
          customData._sendState = "sending";
          this.onMessagePushList(customData);
          callbacks &&
          callbacks.beforeSend &&
          callbacks.beforeSend(customData);
        },
        success: (data) => {
          this.$ImEventBus.$emit("onSendSuccess", data);
          callbacks && callbacks.success && callbacks.success(data);
          this.scrollToBottom({
            duration: 100,
            timer: 0
          });
        },
        fail: (data) => {
          callbacks && callbacks.fail && callbacks.fail(data);
          /* 冲数组最后一个找失败的数据 最后面的是新数据*/
          for (let i = this.messageHistoryList.length - 1; i >= 0; i--) {
            const item = this.messageHistoryList[i];
            if (item.id === data.id) {
              this.$set(this.messageHistoryList, i, { ...item, _sendState: "fail" });
              break;
            }
          }
        }
      };
    },

    /* 发送敏感词 提示卡片*/
    sendSensitiveCard() {
      const cardData = {
        customEvent: "server",
        customExts: {
          showType: "2",
          style: imMessageType.SERVER_QA_AUTO_SEND_CARD,
          subType: imMessageType.SERVER_QA_AUTO_SEND_CARD
        }
      };
      this.send({
        msgType: WS_MESSAGE_TYPE.CUSTOM,
        msg: cardData,
        ...this.commonSendState()
      });
    },
    // 更新卡片消息
    updateMessageCard(){
      if(isObjNull(this.updateMessageList)) return;
      this.messageHistoryList.forEach(item => {
        if(isObj(item) && item.msgId){
          const key = item.msgId;
          if(this.updateMessageList[key]){
            const data = this.updateMessageList[key];
            try {
              const updateMsg = parseJSON(data.msg);
              this.$set(item, "msg", updateMsg);
              /* 删除更新列表的数据 */
              delete this.updateMessageList[key];
              console.log(item, "=========需要更新卡片");
              console.log(data, "=========更新卡片消息");
            }catch (e) {
              console.log(e);
            }
          }
        }
      });
    },
    /* 公共消息发送*/
    send(data) {
      if (this.$WebIM.getWsReadystate() === WS_READY_STATE.OPEN) {
        let ks = {};
        /** 未付费，并且是一对一咨询 匹配敏感词*/
        if (this.isCaseInfoNotPayOneToOne && data.msgType === WS_MESSAGE_TYPE.TEXT) {
          ks = keywordReplace(data.msg, sensitiveWordsMap);
          data.msg = ks.text;
        }
        this.imTools.send({
          ...data,
          success: () => {
            data.success && data.success();
            this.$nextTick(() => {
              if (ks.testReg) {
                this.sendSensitiveCard();
              }
            });
          }
        });
      } else {
        uni.showToast({
          title: "聊天未建立,请重新发送",
          icon: "none",
        });
        let token = getUserTokenStorage();
        if (token) {
          // 登录才连接IM
          this.$store.dispatch("im/userLogin");
        }
      }
    },

    /* 时间显示 前后消息间隔时间超过5分钟，需要显示时间，*/
    getDate(data, prevData) {
      if (prevData) {
        return (
          Math.abs(
            parseTime(data.time, "{i}") -
            parseTime(prevData.time, "{i}")
          ) > 5
        );
      }
      return false;
    }
  }
};
