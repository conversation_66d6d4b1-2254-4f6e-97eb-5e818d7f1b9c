<template>
  <div class="interactive-msg">
    <div class="pt-[12px]">
      <div
        v-for="i in list"
        :key="i.id"
        class="bg-white InteractiveMsg"
      >
        <notice-card
          :data="i"
          :showAvatar="false"
          :type="SYSTEM_MSG_TYPE.INTERACTION"
        />
      </div>
    </div>



    <div
      v-if="isArrNull(list) && isRequest"
      class="placeholder-container"
    >
      <img
        class="tip-img"
        src="../../../message-notice/imgs/no-data3.png"
        alt=""
      >
      <p class="tip">
        一条消息都没有哦
      </p>
    </div>

    <u-divider
      v-if="isLastPage && !isArrNull(list)"
      text="已经是最后一条了"
    />
    <u-safe-bottom />
  </div>
</template>

<script>

import { onReachBottomPage } from "@/libs/tools";
import { coreMessageAllRead, coreMessageList } from "@/api/im";
import { SYSTEM_MSG_TYPE } from "@/enum/imCardEnum";
import { isArrNull } from "@/libs/basics-tools";
import NoticeCard from "@/pages/sub/im/components/NoticeCard/index.vue";

export default {
  name: "InteractiveMsg",
  components: { NoticeCard },
  data() {
    return {
      list: [],
      /** 是否请求过 */
      isRequest: false,
      isLastPage: false,
      onReachBottomPage: {},
    };
  },
  computed: {
    SYSTEM_MSG_TYPE() {
      return SYSTEM_MSG_TYPE;
    }
  },
  mounted() {

    coreMessageAllRead({
      messageCenterEnum: SYSTEM_MSG_TYPE.INTERACTION,
    });
    this.resetData();
    this.getData();
  },
  onReachBottom() {
    this.getData();
  },
  onPullDownRefresh() {
    console.log("refresh");
    this.resetData();
    this.getData().finally(() => {
      uni.stopPullDownRefresh();
    });
  },
  methods: { 
    isArrNull, 
    /* 注册分页 */
    registerPagination() {
      this.onReachBottomPage = {};
      this.onReachBottomPage = onReachBottomPage((data) => {
        return coreMessageList({
          ...data,
          messageCenterEnum: SYSTEM_MSG_TYPE.INTERACTION,
        });
      });
    },
    /** 请求数据 */
    getData() {
      return this.onReachBottomPage()
        .then(({ list = [], paginationState }) => {
          this.isLastPage = paginationState.isEnd;
          this.list = [...this.list, ...list];
          return list;
        })
        .finally(() => {
          this.isRequest = true;
        });
    },
    /** 重置数据 */
    resetData() {
      this.list = [];
      this.isLastPage = false;
      this.isRequest = false;
      this.registerPagination();
    }
  },
};
</script>

<style scoped lang="scss">
.placeholder-container{
  padding-top: 114px;
  text-align: center;
  .tip-img{
    width: 240px;
    height: 180px;
  }
  .tip{
    padding-top: 16px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
  }
}
.InteractiveMsg{
  ::v-deep .MutualAidMessages{
    margin-top: 0;
  }
}
</style>
