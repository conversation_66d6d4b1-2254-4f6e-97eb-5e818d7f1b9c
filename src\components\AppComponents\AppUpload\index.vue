<template>
  <div class="upload">
    <u-upload
      :fileList="fileList1"
      :maxCount="limit"
      :previewImage="previewImage"
      multiple
      name="1"
      @afterRead="afterRead"
      @delete="deletePic"
    >
      <slot>
        <img
          v-if="fileList1.length <= limit"
          alt=""
          class="upload-btn"
          src="@/components/AppComponents/img/Frame631.png"
          srcset=""
        >
      </slot>
    </u-upload>
  </div>
</template>
<script>
import { getUserTokenStorage } from "@/libs/token.js";
import UUpload from "@/uview-ui/components/u-upload/u-upload.vue";
import { axiosBaseHeadersConfig } from "@/libs/config.js";

export default {
  name: "AppUpload",
  components: { UUpload },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    /** 图片上传限制 */
    limit: {
      type: Number,
      default: 8,
    },
    // 是否显示组件自带的图片预览功能
    previewImage: {
      type: Boolean,
      default: uni.$u.props.upload.previewImage
    },
  },
  data() {
    return {
      fileList1: [],
    };
  },
  watch: {
    fileList1: {
      handler(val) {
        this.$emit("input", val);
      },
      immediate: true,
    },
  },
  methods: {
    // 删除图片
    deletePic(event) {
      this[`fileList${event.name}`].splice(event.index, 1);
    },
    // 新增图片
    async afterRead(event) {
      // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
      this.uploadAll(event.file, (res) => {
        console.log("0000000000000:", res);
        if (res.filter((item) => item.code !== 0)?.length) {
          return this.$toast(
            res.filter((item) => item.code !== 0)[0]?.message || "上传失败！"
          );
        } else {
          let lists = [].concat(event.file);
          let fileListLen = this[`fileList${event.name}`]?.length || 0;
          lists.map((item) => {
            this[`fileList${event.name}`].push({
              ...item,
            });
          });
          for (let i = 0; i < lists?.length; i++) {
            let item = this[`fileList${event.name}`][fileListLen];
            this[`fileList${event.name}`].splice(
              fileListLen,
              1,
              Object.assign(item, {
                // status: 'success',
                message: "",
                url: res[i]?.data,
              })
            );
            fileListLen++;
          }
        }
      });
    },
    // 针对微信兼容 所以直接多个循环上传
    uploadAll(list, callBack) {
      let uploadPromiseTask = [];
      for (let i = 0; i < list.length; i++) {
        uploadPromiseTask.push(this.uploadFilePromise(list[i]));
      }
      Promise.all(uploadPromiseTask)
        .then((res) => {
          callBack && callBack(res);
        })
        .catch((error) => {
          console.log("error:", error);
          uni.showToast({
            title: "上传失败",
            icon: "none",
            duration: 2000,
          });
        });
    },
    // 上传api及接口调用
    uploadFilePromise(listObj) {
      uni.showLoading({
        title: "上传中...",
      });
      console.log("uploadFilePromise===========:", listObj);
      let uploadObj = {}; // uploadFile 参数
      let token = getUserTokenStorage();
      uploadObj = {
        filePath: listObj.url,
        name: "file",
      };
      // //#ifdef MP-WEIXIN
      // uploadObj = {
      //     filePath: listObj.url,
      //     name: 'file',
      // }
      // //#endif

      // //#ifdef MP-TOUTIAO || MP-BAIDU
      // uploadObj = {
      //     files: listObj,
      // }
      // //#endif
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: process.env.VUE_APP_ENV_BASE_URL + "/core/upload/image",
          Accept: "multipart/form-data",
          header: {
            "Content-Type": "multipart/form-data",
            token: token,
            osversion: axiosBaseHeadersConfig.osVersion,
          },
          ...uploadObj,
          formData: listObj,
          success: (res) => {
            setTimeout(() => {
              // #ifdef MP-TOUTIAO || MP-WEIXIN
              resolve(JSON.parse(res.data));
              // #endif
              // #ifdef MP-BAIDU
              resolve(res.data);
              // #endif
              uni.hideLoading();
            }, 1000);
          },
          fail: (error) => {
            uni.hideLoading();
            console.log("error:", error);
          },
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.upload {
  .upload-btn {
    display: block;
    width: 75px;
    height: 75px;
    border-radius: 7px 7px 7px 7px;
    order: 1;
    margin: 0 8px 8px 0;
  }
}
</style>
