<template>
  <div class="bg-[#FFFFFF] rounded-[8px] p-[16px] pb-[0]">
    <!--    审核状态    -->
    <div class="pb-[12px]">
      <community-record-item-state :data="data" />
    </div>


    <div class="flex">
      <img
        mode="aspectFill"
        class="w-[48px] h-[48px] rounded-[4px] overflow-hidden"
        :src="data.groupAvatar"
        alt=""
      >
      <div class="pl-[8px]">
        <p class="font-bold text-[16px] text-[#333333]">
          {{ data.groupName }}
        </p>
        <div class="flex pt-[8px]">
          <p class="px-[8px] py-[3px] bg-[#F7F6F6] rounded-[4px] text-[12px] text-[#666666] ml-[4px] first-of-type:ml-[0] flex items-center">
            <i class="iconfont icon-dingwei !text-[12px] !text-[#666666]" />
            {{ data.regionName }}
          </p>
          <p class="px-[8px] py-[3px] bg-[#F7F6F6] rounded-[4px] text-[12px] text-[#666666] ml-[4px] first-of-type:ml-[0] flex items-center">
            {{ data.currentMemberCount }}人
          </p>
        </div>
        <div class="flex pt-[12px]">
          <p
            v-for="(i,index) in groupTypesNames"
            :key="index"
            class="px-[4px] py-[3px] bg-[#F7F6F6] rounded-[4px] text-[12px] text-[#3887F5] ml-[4px] first-of-type:ml-[0]"
          >
            #{{ i }}
          </p>
        </div>
        <p class="text-[12px] text-[#999999] py-[8px]">
          有效期：{{ data.createTime }}-至-{{ data.expireTime }}
        </p>
      </div>
    </div>
    <div class="center border-0 border-solid border-t-[1px] border-[#EEEEEE]">
      <div
        class="text-[14px]  h-[44px] center flex-1 text-[#999999] flex items-center"
        @click="toPublishCompanionGroup"
      >
        <i class="iconfont icon-a-VectorStroke !text-[16px] !text-[#999999] pr-12px" />
        修改
      </div>
      <div
        v-if="data.auditStatus===2"
        class="text-[14px]  h-[44px] center flex-1 text-[#999999] flex items-center"
        @click="toPublishCompanionGroup"
      >
        <i class="iconfont icon-shuaxin !text-[16px] !text-[#999999] pr-12px" />
        重新提交
      </div>
    </div>
  </div>
</template>

<script>
import CommunityRecordItemState
  from "@/pages/sub/community/components/CommunityRecordItem/CommunityRecordItemState.vue";
import { toPublishCompanionGroup } from "@/libs/turnPages";

export default {
  name: "CommunityRecordItem",
  components: { CommunityRecordItemState },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    }
  },
  computed: {
    groupTypesNames(){
      try {
        return JSON.parse(this.data.groupTypesNames);
      }catch (e) {
        return [];
      }
    }
  },
  methods: {
    toPublishCompanionGroup() {
      toPublishCompanionGroup(this.data);
    }
  }

};
</script>

<style scoped lang="scss">

</style>