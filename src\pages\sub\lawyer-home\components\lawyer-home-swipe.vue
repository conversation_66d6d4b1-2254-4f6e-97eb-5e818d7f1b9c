<template>
  <div class="home-swipe">
    <div
      :style="{
        transform: `translateY(${-translateY}px)`,
      }"
      class="home-swipe-box"
    >
      <div
        v-for="item in commentList"
        :key="item.id"
        class="tips"
      >
        <img
          alt=""
          class="tips-icon"
          src="../img/tips-icon.png"
        >
        <span class="tips-text">{{ item.scrollInfo }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { fastConsultBuyLawyers } from "@/api/lawyer.js";
import { getClientRect } from "@/libs/tools.js";

export default {
  name: "LawyerHomeSwipe",
  data() {
    return {
      /** 滚动信息 */
      commentList: [],
      /** Y轴方向 */
      translateY: 0,
      /** 计时器 */
      timer: null,
    };
  },
  created() {
    this.getConsultList();
  },
  destroyed() {
    clearInterval(this.timer);
  },
  mounted() {
    this.initScroll();
  },
  methods: {
    /** 初始化滚动 */
    initScroll() {
      this.translateY = 0;
      this.timer = setInterval(async () => {
        let itemHeight = 0;

        try {
          // 动态获取每一条的元素高度
          itemHeight = (await getClientRect.call(this, ".tips")).height;
        } catch (e) {
          itemHeight = 0;
        }

        this.translateY += itemHeight;

        if (this.translateY >= (this.commentList.length - 2) * itemHeight) {
          this.commentList = this.commentList.concat(this.commentList);
        }
      }, 2000);
    },
    /** 获取滚动信息 */
    getConsultList() {
      fastConsultBuyLawyers().then((res) => {
        this.commentList = res.data || [
          { scrollInfo: "用户YD** 刚刚提交了咨询" },
          { scrollInfo: "用户65** 刚刚提交了咨询" },
          { scrollInfo: "用户19** 刚刚提交了咨询" },
          { scrollInfo: "用户we** 刚刚提交了咨询" },
        ];
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.home-swipe {
  margin: 0 auto;
  width: 343px;
  height: 60px;
  border-radius: 8px;
  box-sizing: border-box;
  overflow: hidden;
  background-color: #fff;
  padding: 8px 16px;
}

.home-swipe-box {
  transition: all 0.5s;
}

.tips {
  display: flex;
  align-items: center;
  height: 18px;
  padding-bottom: 8px;
}

.tips-icon {
  width: 8px;
  height: 8px;
}

.tips-text {
  margin-left: 6px;
  font-size: 13px;
  font-weight: 400;
  color: #333333;
}
</style>
