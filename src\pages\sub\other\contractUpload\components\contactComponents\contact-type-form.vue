<template>
  <div class="type-item">
    <p class="title">
      合同模板分类
    </p>
    <div
      class="input-content flex flex-align-center flex-space-between"
      @click="show = true"
    >
      <p
        v-show="contactType.label"
        class="label"
      >
        {{ contactType.label }}
      </p>
      <p
        v-show="!contactType.label"
        class="label text-cccccc"
      >
        请选择
      </p>
      <img
        alt=""
        class="select"
        src="../../img/right.png"
      >
    </div>
    <type-select
      :show.sync="show"
      @changeType="getType"
      @getType="getSelectList"
    />
  </div>
</template>

<script>
import TypeSelect from "@/pages/sub/other/contractUpload/components/contactComponents/typeSelect.vue";

export default {
  name: "ContactTypeForm",
  components: { TypeSelect },
  props: {
    value: {
      type: [Number, String],
      required: true,
    },
  },
  data() {
    return {
      show: false,
      selectList: [],
    };
  },
  computed: {
    contactType() {
      return this.selectList.find((item) => item.value === this.value) || {};
    },
  },
  methods: {
    getSelectList(data) {
      this.selectList = data;
    },
    getType(val) {
      this.show = false;
      this.$emit("input", val.value);
      this.$emit("selected", val);
    },
  },
};
</script>

<style lang="scss" scoped>
.type-item {
  .title {
    font-size: 16px;
    font-weight: 600;
    color: #333333;
    padding: 16px 0;
  }

  .text-cccccc {
    color: #cccccc !important;
  }

  .input-content {
    width: 343px;
    height: 45px;
    background: #f5f5f7;
    border-radius: 8px 8px 8px 8px;
    font-size: 15px;
    box-sizing: border-box;
    padding: 0 12px;

    .label {
      font-size: 15px;
      color: #333333;
    }

    .select {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
