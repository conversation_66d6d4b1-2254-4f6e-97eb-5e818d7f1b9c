<template>
  <div>
    <app-popup
      :safeAreaInsetBottom="false"
      :show="show"
      closeable
      mode="center"
      round="16"
      @cancel="$emit('cancel')"
    >
      <img
        alt=""
        class="image"
        src="../img/tc.png"
      >
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";

export default {
  name: "ProfileInputExample",
  components: { AppPopup },
  props: {
    show: {
      type: Boolean,
      default: false
    }
  }
};
</script>

<style lang="scss" scoped>
.image {
  display: block;
  width: 327px;
  height: 596px;
}
</style>
