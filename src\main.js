import Vue from "vue";
import App from "./App";
import "./uni.promisify.adaptor";
import store from "./store";
import uView from "@/uview-ui";
import "./styles/main.scss";
import * as filter from "@/libs/filter";
import basicsTools from "@/libs/basics-tools";
import "@/styles/iconfont/iconfont.css";
import LayoutView from "@/components/LayoutView/LayoutView.vue";
Vue.component("LayoutView", LayoutView);

Vue.config.productionTip = false;

App.mpType = "app";

Vue.prototype.$store = store;

Vue.prototype.$basicsTools = basicsTools;

Vue.prototype.$toast = (data, { duration = 1000 } = {}) => {
  return new Promise((resolve, reject) => {
    uni.showToast(basicsTools.isString(data) ? {
      icon: "none",
      title: data,
      duration,
      fail() {
        reject();
      }
    } : data);

    setTimeout(() => {
      resolve();
    }, duration);
  });

};
Vue.use(uView);

Object.keys(filter).forEach((key) => {
  Vue.filter(key, filter[key]);
});

const app = new Vue({
  ...App,
});
app.$mount();
