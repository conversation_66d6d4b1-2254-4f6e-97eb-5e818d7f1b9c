<template>
  <error-tip>
    {{ customExts.content||'用户已申请退款，服务已关闭' }}
  </error-tip>
</template>

<script>
import ErrorTip from "@/pages/sub/im/components/ErrorTip/index.vue";
import cardProps, { customComputed } from "@/pages/sub/im/mixins/card-props.js";

export default {
  name: "RefundTipCard",
  components: { ErrorTip },
  mixins: [cardProps, customComputed]
};
</script>

<style scoped lang="scss">

</style>
