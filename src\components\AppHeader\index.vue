<template>
  <div>
    <app-placeholder
      :height="statusBarHeight"
      :showSafe="false"
    />
    <div
      class="relative"
      :style="[
        {
          height: `${navigationBarHeight}px`
        }
      ]"
    >
      <i
        v-show="back"
        class="iconfont icon-er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pl-[12px] !text-[24px] absolute left-0 top-50% absolute-y-center z-[1]"
        @click="backClick"
      />
      <slot />
    </div>
  </div>
</template>

<script>
import AppPlaceholder from "@/components/AppPlaceholder/index.vue";
import AppHeaderMixin from "@/components/AppHeader/AppHeaderMixin";
import { turnToCaseSourceSquare } from "@/libs/turnPages";

export default {
  name: "AppHeader",
  components: { AppPlaceholder },
  mixins: [AppHeaderMixin],
  props: {
    /* 是否需要返回按钮 */
    back: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    backClick(){
      const pages = getCurrentPages();
      if(pages.length <= 1){
        turnToCaseSourceSquare();
      }else{
        /* 判断有没有 历史记录 没有直接取首页 */
        uni.navigateBack();
      }
    }
  },
};
</script>
