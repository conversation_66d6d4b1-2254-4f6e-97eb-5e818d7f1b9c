<template>
  <div class="title-item">
    <p class="title">
      合同模版自定义价格
    </p>
    <p class="desc font12 text-999999">
      基于平台统一定价规范，您可以输入￥{{ priceInterval.min }}-￥{{ priceInterval.max }}区间的金额
    </p>
    <div class="input-content flex flex-align-center">
      <u--input
        v-model="title"
        :cursorSpacing="50"
        :disabled="disabled"
        :maxlength="20"
        :placeholder="placeholder"
        :showConfirmBar="false"
        autoHeight
        border="none"
        disabledColor="#ffffff"
        fixed
        placeholderStyle="font-size: 15px;color: #cccccc;"
        suffixIcon="元"
        type="digit"
      />
    </div>
  </div>
</template>

<script>


export default {
  name: "ContactPriceForm",
  props: {
    value: {
      type: [String, Number],
      default: ""
    },
    priceInterval: {
      type: Object,
      default: () => ({}),
      desc: "取的价格区间"
    }
  },
  data() {
    return {
      disabled: false,
      placeholder: "请输入"
    };
  },
  computed: {
    title: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit("input", value);
      },
    },
  },
};
</script>

<style lang="scss" scoped>
.title-item{
  .title{
    font-size: 16px;
    font-weight: 600;
    color: #333333;
    padding: 16px 0 0;
  }
  .desc{
    margin-bottom: 12px;
    margin-top: 4px;
  }
  .input-content{
    width: 343px;
    height: 45px;
    background: #F5F5F7;
    border-radius: 8px 8px 8px 8px;
    font-size: 15px;
    box-sizing: border-box;
    padding: 0 12px;
  }
  //::v-deep .u-input {
  //  height: 45px !important;
  //  width: auto !important;
  //  border-radius: 8px !important;
  //}
}

</style>
