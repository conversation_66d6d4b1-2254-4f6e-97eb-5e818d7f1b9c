<template>
  <app-popup
    :safeAreaInsetBottom="false"
    :show="value"
    mode="center"
  >
    <div class="w-[311px] bg-[#FFFFFF] rounded-[16px]">
      <p class="font-bold text-[16px] text-[#333333] pt-[24px] text-align-center">
        异地找律师服务说明
      </p>
      <p class="w-[263px] mx-auto  text-[15px] text-[#323233] pt-[12px] pb-[24px]">
        为保障平台律师的隐私及使用体验，每日在平台上主动电话联系的次数已做上限，当前<span class="text-[#3887F5]">每日最多可电话联系{{ getTotalContactLawyer }}人</span> ，请知悉
      </p>
      <div class="px-[24px] pb-[16px]">
        <div
          class="h-[36px] bg-[#3887F5] rounded-[68px] font-bold text-[14px] text-[#FFFFFF] flex items-center justify-center"
          @click="value=false"
        >
          知道了
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { mapGetters } from "vuex";

export default {
  name: "TipPopup",
  components: { AppPopup },
  data(){
    return{
      value: false
    };
  },
  computed: {
    ...mapGetters(
      "user",
      ["getTotalContactLawyer"]
    )
  },
  mounted() {
    /* 只弹一次  存入缓存 */
    if (!uni.getStorageSync("tipPopup")) {
      this.value = true;
      uni.setStorageSync("tipPopup", true);
    }
  }
};
</script>

<style scoped lang="scss">

</style>