const fs = require("fs");
const path = require("path");
const crypto = require("crypto");

const rootPath = findRootPath();
/** image 图片缓存地址 */
const stateLockUrl = path.resolve(rootPath, "./state-lock.json");

const config = {};

const fileMemo = loadingMemo();

function setConfig(newConfig) {
  Object.assign(config, newConfig);
}

function getConfig() {
  return config;
}


// 如果不存在则创建文件
try {
  fs.statSync(stateLockUrl);
} catch (e) {
  fs.writeFileSync(stateLockUrl, JSON.stringify({}));
}


function getHash(content) {
  return crypto.createHash("sha256").update(content).digest("hex");
}

/**
 * 验证图片类型
 * 判断是否未上传的图片
 */
function isNotUploadImage(filePath) {
  if (filePath.startsWith("http")) return false;

  return /\.(png|jpg|jpeg|gif)$/.test(filePath);
}

/**
 * 寻找项目的跟目录
 */
function findRootPath() {
  let rootPath = process.cwd();
  let isExist = false;

  while (!isExist) {
    try {
      fs.statSync(path.resolve(rootPath, "package.json"));
      isExist = true;
    } catch (e) {
      rootPath = path.resolve(rootPath, "../");
    }
  }

  return rootPath;
}


/** 读取缓存文件 */
function loadingMemo() {
  let memo = {};

  try {
    memo = JSON.parse(fs.readFileSync(stateLockUrl, "utf-8"));
  } catch (e) {
    memo = {};
  }

  return memo;
}


/**
 * 验证文件是否已经上传
 * @param filePath
 */
function verifyFileUpload(filePath) {
  let fileHash;

  try {
    const readerStream = fs.readFileSync(filePath);

    fileHash = getHash(readerStream);
  } catch {
    console.error("文件读取失败");
    return [""];
  }

  return [fileMemo[fileHash] || "", fileHash];
}

/**
 * 读取文件再本地的真实地址
 * 将 @ ~ 替换成真实地址
 */
function getRealPath(filePath, dirPath) {
  let allPath;
  // 判断 filePath 第一个字符是否是 @ ~
  if (filePath[0] === "@" || filePath[0] === "~") {
    // 去掉第一个字符
    const localPath = filePath.slice(1);
    // 拼接成真实地址
    allPath = path.resolve(rootPath, `src/${localPath}`);
  } else {
    allPath = path.resolve(dirPath, filePath);
  }

  return allPath;
}

function memoUploadFile(fileHash, remoteUrl) {
  fileMemo[fileHash] = remoteUrl;

  // 将 allMemo 按照 key 排序 每一个键值对单独为一行 写入文件
  let str = Object.keys(fileMemo)
    .sort()
    .map((key) => `"${key}": "${fileMemo[key]}",\n`)
    .join("");

  // 去掉最后一个逗号
  str = str.slice(0, -2);
  // 使用大括号包裹
  str = `{\n${str}\n}`;
  // 写入文件
  fs.writeFileSync(stateLockUrl, str);
}

function uploadFile(filePath, fileHash) {
  // 抽离核心上传方法，以便于外部能够进行配置，适合不同的项目
  const requestFn = config.requestFn;

  if (!requestFn) {
    const err = "需要配置requestFn";

    console.error("上传失败", err);
    return Promise.reject(err);
  }

  return requestFn(filePath).then(url => {
    memoUploadFile(fileHash, url);
    console.log("上传成功", url);
    return url;
  }).catch(err => {
    console.error("上传失败", err);
    return err;
  });
}

module.exports = {
  uploadFile,
  verifyFileUpload,
  getRealPath,
  isNotUploadImage,
  setConfig,
  getConfig
};
