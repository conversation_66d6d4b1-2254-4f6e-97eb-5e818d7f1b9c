<template>
  <div
    v-if="!notHaveService"
    class="bg-[#FFFFFF] rounded-[8px] px-[16px] py-[16px]"
  >
    <div class="flex justify-between items-center">
      <div class="font-bold text-[16px] text-[#333333]">
        精选服务
      </div>
      <div
        class="text-[13px] text-[#666666]"
        @click="handleClick"
      >
        <p class="flex items-center">
          全部服务
          <img
            alt=""
            class="w-[16px] h-[16px] block"
            src="../img/arrow.png"
          >
        </p>
      </div>
    </div>
    <div
      v-if="!isArrNull(lawyerConsultationList)"
      class="mt-[14px]"
    >
      <div
        v-for="item in lawyerConsultationList"
        :key="item.serviceCode"
        class="pb-[12px] pt-[12px] first:pt-0 last:pb-0 border-0 border-b-[0.5px] last:border-b-0 border-solid border-[#EEEEEE]"
      >
        <lawyer-home-servers-item-service-old
          :data="item"
          @click="toShare"
        />
      </div>
    </div>
    <app-popup
      :round="16"
      :safeAreaInsetBottom="false"
      :show="showDialog"
      :zIndex="9991"
    >
      <div
        class="rounded-tl-[16px] rounded-tr-[16px] bg-[#F5F5F7] px-[16px] py-[16px] flex justify-between items-center text-[18px] font-bold text-[#333333]"
      >
        <div>全部服务</div>
        <img
          alt=""
          class="w-[24px] h-[24px] block"
          src="../img/close.png"
          @click="handleClick"
        >
      </div>
      <div
        class="max-h-[463px] px-[12px] pb-[12px] overflow-auto bg-[#F5F5F7] space-y-[12px]"
      >
        <div
          v-for="item in lawyerConsultationList"
          :key="item.serviceCode"
          class="pb-[12px] pt-[12px] rounded-[8px] bg-[#FFFFFF] px-[12px]"
        >
          <lawyer-home-servers-item-service
            :data="item"
            @click="toShare"
          />
        </div>
        <div
          v-for="item in lawyerCaseList"
          :key="item.serviceCode"
          class="pb-[12px] pt-[12px] rounded-[8px] bg-[#FFFFFF] px-[12px]"
        >
          <lawyer-home-servers-item-service
            :data="item"
            @click="toShare"
          />
        </div>
      </div>
      <u-safe-bottom
        :customStyle="{
          backgroundColor: '#F5F5F7',
        }"
      />
    </app-popup>
    <lawyer-home-share-popup
      v-model="showSharePopup"
      @cancel="showSharePopup = false"
      @confirm="confirmShare"
    />
  </div>
</template>
<script>
import LawyerHomeServersItemService from "@/pages/sub/lawyer-home/components/lawyer-home-servers-item-service.vue";
import { isArrNull } from "@/libs/basics-tools";
import { serviceManegeGetLawyerAllServiceList } from "@/api/im";
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import LawyerHomeServersItemServiceOld
  from "@/pages/sub/lawyer-home/components/lawyer-home-servers-item-service-old.vue";
import LawyerHomeSharePopup from "@/pages/sub/lawyer-home/components/LawyerHomeSharePopup.vue";
import { turnToLawyerAuthResultPage } from "@/libs/turnPages";
import { getLawyerShareCode } from "@/api/user";
import { generateToWeChat } from "@/api";

export default {
  name: "LawyerHomeServers",
  components: {
    LawyerHomeSharePopup,
    LawyerHomeServersItemServiceOld,
    USafeBottom,
    AppPopup,
    LawyerHomeServersItemService,
  },
  props: {
    serviceManageV2VoList: {
      type: Array,
      default: () => [],
    },
    lawyerInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      showDialog: false,
      /** ! 律师服务 这里主要是用来判断是否有对应的电话或图文服务 */
      lawyerService: {
        1: [],
        2: [],
      },
      /** 分享弹窗 */
      showSharePopup: false,
    };
  },
  computed: {
    /** 律师咨询服务 */
    lawyerConsultationList() {
      // 是否有电话咨询
      const isPhoneConsultation = !isArrNull(this.lawyerService[1]);

      // 是否有图文咨询
      const isTextConsultation = !isArrNull(this.lawyerService[2]);

      const obj1 = {
        ...this.lawyerService?.[1]?.[0],
        itemClassType: 1,
      };

      const obj2 = {
        ...this.lawyerService?.[2]?.[0],
        itemClassType: 2,
      };

      // 有电话咨询和图文咨询
      if (isPhoneConsultation && isTextConsultation) {
        return [obj1, obj2];
      }

      // 有电话咨询
      if (isPhoneConsultation) {
        return [obj1];
      }

      // 有图文咨询
      if (isTextConsultation) {
        return [obj2];
      }

      // 没有电话咨询和图文咨询
      return [];
    },
    /** 案件委托服务 */
    lawyerCaseList() {
      return this.serviceManageV2VoList.filter(
        (item) => Number(item.serviceType) === 1002
      );
    },
    /** 是否没有服务 */
    notHaveService() {
      return (
        isArrNull(this.lawyerConsultationList) && isArrNull(this.lawyerCaseList)
      );
    },
  },
  watch: {
    lawyerInfo: {
      handler() {
        if (!this.lawyerInfo.id) return;

        this.getLawyerService();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    isArrNull,
    handleClick() {
      this.showDialog = !this.showDialog;
    },
    getLawyerService() {
      const requestArr = [
        serviceManegeGetLawyerAllServiceList({
          lawyerId: this.lawyerInfo.id,
          itemClassType: 1,
        }),
        serviceManegeGetLawyerAllServiceList({
          lawyerId: this.lawyerInfo.id,
          itemClassType: 2,
        }),
      ];

      Promise.all(requestArr).then((res) => {
        console.log(res, "SET_PHONE_AND_GRAPHIC_SERVICE_INFO");
        this.lawyerService = {
          1: res[0]?.data || [],
          2: res[1]?.data || [],
        };

        this.$store.commit(
          "lawyerHome/SET_PHONE_AND_GRAPHIC_SERVICE_INFO",
          this.lawyerService
        );
      });
    },
    toShare(data) {
      console.log(data);
      // 如果没有认证，则先去认证后才能分享
      turnToLawyerAuthResultPage({
        callback: () => {
          this.showSharePopup = true;
        },
      });
    },
    async confirmShare() {
      try {
        const res = await getLawyerShareCode();

        const lawyerShareCode = res.data?.code;

        const { data: url } = await generateToWeChat(
          {
            path: "/pages/sub/lawyer-home/index",
            query: `id=${this.lawyerInfo.id}&lawyerShareCode=${lawyerShareCode}`,
          },
          {
            type: "scheme",
          }
        );

        uni.setClipboardData({
          data: url,
          success: () => {
            this.showSharePopup = false;

            uni.showToast({
              title: "复制商品链接成功，可以直接发送给用户",
              icon: "none",
            });
          },
          fail: (err) => {
            console.log(err);

            uni.showToast({
              title: "复制失败，请检查权限",
              icon: "none",
            });
          },
        });
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>
