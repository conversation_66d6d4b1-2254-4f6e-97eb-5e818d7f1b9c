<template>
  <div>
    <div
      class="fixed-buttons"
      :class="{'no-bg': isSpecial}"
    >
      <div class="btn-wrapper flex flex-space-between">
        <template v-if="!isSpecial">
          <div
            v-if="!type || type==='undefined'"
            class="btn"
            @click="freeGet"
          >
            {{ remainClueNum <= 0 ? '立即抢单' : '免费抢' }}
          </div>

          <template v-if="type==='1'">
            <div
              v-if="detail.fixedAmount"
              class="btn success"
              @click="getOnlyOne"
            >
              独享 <img
                class="tip tip-1"
                src="@/pages/caseSource/img/<EMAIL>"
                alt=""
              >
            </div>
            <div
              class="btn"
              @click="freeGet"
            >
              {{ remainClueNum <= 0 ? '立即抢单' : '免费抢' }}
            </div>
          </template>

          <div
            v-if="type==='2'"
            class="btn success"
            @click="getOnlyOne"
          >
            独享 <img
              class="tip tip-2"
              src="@/pages/caseSource/img/tip2.png"
              alt=""
            >
          </div>
        </template>
        <template v-else>
          <template v-if="detail.status === CaseSourceStatus.CASE_SOURCE_STATUS_SERVICING">
            <div
              class="btn plain"
              @click="stopServer"
            >
              结束跟进
            </div>
            <div
              class="btn"
              @click="turnToImPage({id: detail.imSessionId})"
            >
              查看会话
            </div>
          </template>
          <template v-if="detail.status === CaseSourceStatus.CASE_SOURCE_STATUS_FINISHED">
            <div class="flex-1">
              <div
                class="btn"
                @click="turnToImPage({id: detail.imSessionId})"
              >
                查看会话
              </div>
              <div
                v-if="detail.isFeedBack===0"
                class="btn2"
                @click="refundShow=true"
              >
                申请退单
              </div>
            </div>
          </template>
        </template>
      </div>
      <div
        v-if="!isSpecial"
        class="text-line"
      >
        锁定后才可以开始服务，同时会扣除案源底价的哦~
      </div>
      <!--    <div class="btn">立即锁定</div>-->
      <u-safe-bottom />
    </div>
    <app-popup
      :show="show"
      mode="bottom"
      round="16"
      @close="show=false"
    >
      <div class="popup-wrapper">
        <div class="title">
          确认结束跟进吗？
          <span
            class="iconfont icon-a-cuowuguanbi"
            @click="show=false"
          />
        </div>
        <div class="content">
          结束案源跟进后，案源状态将会变成已完成，可到我的案源-已完成查看详情哦
          <div
            class="btn"
            @click="completeCaseServer"
          >
            确认
          </div>
        </div>
      </div>
    </app-popup>
    <case-prompt
      :show.sync="casePromptShow"
      :currentCase="currentCaseText"
      :detail="detail"
    />
    <app-popup
      :show="refundShow"
      mode="center"
      :safeAreaInsetBottom="false"
      round="16"
      @close="refundShow=false"
    >
      <div class="refund-popup-wrapper">
        <div class="title">
          法临律师退款标准
        </div>
        <div class="content">
          <div class="line">
            请如实填写退单申请，提交后平台将对退单进行审查核实，审查通过后将全额返还消耗法临币，
            若审查不通过则不予以退还法临币，并触发平台风控条例，影响律师的接案派单权益
          </div>

          <div class="line">
            特别注意：申请退单后
            <span class="red">赠送法临币不退</span>，详见
            <span
              class="color-text"
              @click="clickXy"
            >《法临律师退款标准》</span>
          </div>
          <div
            class="btn"
            @click="IKnow"
          >
            我知道了
          </div>
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import CasePrompt from "@/pages/caseSource/components/casePrompt.vue";
import {
  turnToCaseSourceDetails,
  turnToCaseSourcePayPage,
  turnToImPage,
  turnToRefundPage,
  turnToWebViewPage
} from "@/libs/turnPages";
import { completeCaseServer } from "@/api";
import { CaseSourceStatus, WebAddress } from "@/enum";
import { caseSourceGrabOrder } from "@/api/order";
export default {
  computed: {
    CaseSourceStatus() {
      return CaseSourceStatus;
    }
  },
  components: { CasePrompt, AppPopup, USafeBottom },
  props: {
    type: {
      type: String,
      default: ""
    },
    isSpecial: {
      type: Boolean,
      default: false
    },
    currentCaseText: {
      type: String,
      default: ""
    },
    detail: {
      type: Object,
      default: () => ({})
    },
    remainClueNum: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      show: false,
      casePromptShow: false,
      refundShow: false,
    };
  },
  methods: {
    turnToRefundPage,
    turnToImPage,
    stopServer(){
      this.show = true;
    },
    async freeGet(){
      await this.$parent.checkAuth();
      if(this.remainClueNum <= 0){
        this.casePromptShow = true;
        return;
      }
      caseSourceGrabOrder({
        walletAccountType: 6, // 6.线索包账户(免费抢指定扣除线索包必传)
        caseSourceV2Id: this.detail.id,
      })
          .then(({data = {}}) => {
            turnToImPage({id: data.imSessionId})
          })
          .catch(() => {
            // this.errorShow = true;
          })
    },
    async getOnlyOne() {
      await this.$parent.checkAuth();
      turnToCaseSourcePayPage({
        id: this.detail.id,
        way: 1,
      });
    },
    completeCaseServer() {
      completeCaseServer(this.detail.serverId).then(res => {
        this.show = false;
        this.$emit("refreshCaseServer");
      });
    },
    clickXy(){
      turnToWebViewPage({ src: WebAddress.lawyer_reimburse });
    },
    IKnow(){
      this.refundShow = false;
      turnToRefundPage({id: this.detail.serverId})
    }
  }

};
</script>

<style lang="scss" scoped>
.fixed-buttons{
  background: #FFFFFF;
  box-shadow: 0px -3px 10px 0px rgba(0,0,0,0.08);
  border-radius: 16px 16px 0px 0px;
  padding: 8px 16px;
  text-align: center;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
  &.no-bg{
    background: #F5F5F7;
    box-shadow: none;
  }
  .btn{
    flex: 1;
    line-height: 44px;
    border-radius: 22px;
    font-size: 16px;
    font-weight: 500;
    color: #FFFFFF;
    background: #3887F5;
    position: relative;
    &.success{
      background: #22BF7E;
    }
    &.plain{
      border: 1px solid #3887F5;
      color:#3887F5;
      background: #F5F5F7;
    }
    .tip{
      position: absolute;

    }
    .tip-1{
      width: 70px;
      height: 16px;
      right: 16px;
      top: -8px;
    }
    .tip-2{
      width: 180px;
      height: 30px;
      right: 24px;
      top: -20px;
    }
    & + .btn{
      margin-left: 12px;
    }
  }
  .btn2{
    line-height: 44px;
    font-size: 16px;
    font-weight: 400;
    color: #999999;
    margin-top: 8px;
  }
  .text-line{
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    margin-top: 16px;
  }
}
.popup-wrapper{
  .title{
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    line-height: 50px;
    border-bottom: 1px solid #EEEEEE;
    padding: 0 16px;
    text-align: center;
    position: relative;
    .iconfont{
      position: absolute;
      right: 16px;
      font-size: 24px;
    }
  }
  .content{
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    padding: 12px 16px 0;
  }
  .btn{
    width: 100%;
    line-height: 44px;
    background: #3887F5;
    border-radius: 22px 22px 22px 22px;
    font-size: 16px;
    font-weight: 500;
    color: #FFFFFF;
    text-align: center;
    margin-top: 20px;
  }
}
.refund-popup-wrapper{
  width: 311px;
  padding: 24px 16px 24px;
  box-sizing: border-box;
  .title{
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    margin-bottom: 12px;
    text-align: center;
  }
  .line{
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    & + .line{
      margin-top: 16px;
    }
    .red{
      color: #EB4738;
    }
  }
  .btn{
    width: 100%;
    line-height: 32px;
    background: #3887F5;
    border-radius: 22px 22px 22px 22px;
    font-size: 14px;
    font-weight: 400;
    color: #FFFFFF;
    text-align: center;
    margin-top: 24px;
  }
}
</style>
