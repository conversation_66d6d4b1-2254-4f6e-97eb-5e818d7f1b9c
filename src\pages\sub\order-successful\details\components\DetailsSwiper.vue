<template>
  <swiper
    :autoplay="true"
    :interval="2000"
    circular
    class="h-[24px] bg-[rgba(0,0,0,0.6)] rounded-[49px] w-[280px]"
    vertical
  >
    <swiper-item
      v-for="item in list"
      :key="item.lawyerId"
      class="h-[24px] flex items-center box-border"
    >
      <div class="pl-[2px] pr-[8px] flex items-center box-border">
        <img
          :src="item.avatar"
          alt=""
          class="block w-[20px] h-[20px] rounded-full mr-[4px] shrink-0"
        >
        <div class="text-[11px] text-[#FFFFFF] text-ellipsis">
          刚刚{{ item.lawyerName }}律师通过沾沾喜气，获得{{ flbAmount(item.amount) }}个法临币
        </div>
      </div>
    </swiper-item>
  </swiper>
</template>

<script>
import { redPacketMoment } from "@/api";
import { flbAmount } from "@/libs/tools";

export default {
  name: "DetailsSwiper",
  data(){
    return {
      list: [],
    };
  },
  mounted() {
    this.getRedPacketMoment();
  },
  methods: {
    flbAmount,
    getRedPacketMoment(){
      redPacketMoment().then(res => {
        this.list = res.data;
      });
    },
  }
};
</script>
