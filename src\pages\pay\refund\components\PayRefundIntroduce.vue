<template>
  <div>
    <div class="title">
      <div>退单说明：</div>
      <div v-if="showRefundInfo">
        查看<span
          class="text-3887f5"
          @click="turnToRefundInfoPage"
        >退单标准和退单示例</span>
      </div>
    </div>
    <ul class="intro">
      <li
        v-for="(item, index) in textList"
        :key="index"
        class="intro__item"
      >
        {{ item }}
      </li>
    </ul>
  </div>
</template>

<script>
import { turnToRefundInfoPage } from "@/libs/turnPages";
import { getCommonConfigKey } from "@/api";

export default {
  name: "PayRefundIntroduce",
  props: {
    /** 显示标准示例 */
    showRefundInfo: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      /** 退款申请时间 */
      time: "",
    };
  },
  created() {
    this.getRefundApplyTime();
  },
  computed: {
    /** 退单说明文案 */
    textList() {
      return [`只能在锁定案源成功后${this.time}小时内可以申请退单，且只有一次退单的机会`,
        "退单流程：申请-平台审核-退单，申请退单后申请后需要平台审核情况，一般会在1个工作日内处理完毕",
        "使用平台赠送法临币锁定的案源以及使用会员免费赠送次数锁定的案源退单后不退款或不退锁定次数",
        "用户付费咨询订单不支持律师退单",
      ];
    }
  },
  methods: {
    turnToRefundInfoPage,
    /** 获取退款申请时间 */
    getRefundApplyTime() {
      getCommonConfigKey({
        paramName: "lawyer_can_do_feedback",
      }).then(({ data }) => {
        // 转化为小时
        this.time = Number(data.paramValue) / 60 / 60;
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  color: #999999;
}

.intro {
  margin-top: 8px;
  font-size: 12px;
  font-weight: 400;
  color: #999999;

  &__item {
    margin-top: 20px;
    display: flex;

    // 左边的小圆点
    &::before {
      flex-shrink: 0;
      content: "";
      display: inline-block;
      width: 4px;
      height: 4px;
      margin: 6px 4px 0 4px;
      border-radius: 50%;
      background-color: #999999;
    }

    &:first-child {
      margin-top: 0;
    }
  }
}
</style>
