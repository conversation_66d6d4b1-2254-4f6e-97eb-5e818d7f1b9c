<template>
  <div>
    <div
      class="relative bg-[#FFFFFF] rounded-[8px] px-[16px] pt-[53px] pb-[16px]"
    >
      <div
        v-if="isSelf"
        class="absolute top-[19px] right-[16px]"
      >
        <div class="flex items-center">
          <div class="w-[24px] h-[24px]">
            <img
              v-if="isVerify"
              alt=""
              class="block w-[24px] h-[24px]"
              src="../img/123.png"
              @click="handleShare"
            >
          </div>
          <img
            alt=""
            class="ml-[16px] w-[24px] h-[24px] block"
            src="../img/122.png"
            @click="handleEdit"
          >
        </div>
      </div>
      <!-- 律师信息 -->
      <div class="flex">
        <img
          :alt="data.realName"
          :src="data.imgUrl"
          mode="aspectFill"
          class="absolute -top-[19px] left-[16px] w-[64px] h-[64px] bg-[#C4C4C4] rounded-full border-[2px] border-solid border-[#FFFFFF]"
        >
      </div>
      <div class="flex items-center">
        <div class="text-[20px] font-bold text-[#222222] mr-[8px]">
          {{ data.realName }}
        </div>
        <div class="text-[13px] text-[#999999]">
          {{ data.workCityName }}
        </div>
        <div class="opacity-100 text-[13px] px-[4px] text-[#EEEEEE]">
          |
        </div>
        <div class="text-[13px] text-[#999999]">
          执业{{ data.workTime }}年
        </div>
      </div>
      <div class="text-[13px] text-[#444444] mt-[2px]">
        {{ data.lawyerOffice || "未填写" }}
      </div>
      <!-- 咨询类型 -->
      <div class="flex items-center mt-[16px]">
        <img
          alt=""
          class="w-[32px] h-[15px] mr-[6px] block shrink-0"
          src="../img/<EMAIL>"
        >
        <span class="text-[13px] text-[#444444] text-ellipsis">{{
          workField
        }}</span>
      </div>
      <!-- 律师简介 -->
      <div
        class="text-[13px] text-[#666666] relative mt-[13px] leading-[20px] text-ellipsis-2 z-10"
      >
        <div>
          <div class="w-[56px] h-[20px] relative inline-block">
            <img
              alt=""
              class="absolute w-[56px] h-[18px] left-0 top-[6px]"
              src="../img/Frame1321315520.png"
            >
          </div>
          {{ data.lawyerProfile }}
        </div>
        <div
          class="absolute z-10 bottom-0 right-0 w-[92px] h-[20px]"
          @click="handleShow"
        >
          <img
            alt=""
            class="background-image"
            src="../img/Frame1321315500.png"
          >
        </div>
      </div>
      <!-- 律师评价 -->
      <div class="text-[13px] text-[#999999] flex items-center mt-[23px]">
        <div class="flex items-center">
          <img
            alt=""
            class="mr-[2px] w-[16px] h-[16px] block"
            src="../img/star.png"
          >
          <div class="text-[16px] font-bold text-[#F2AF30]">
            {{ score }}
          </div>
        </div>
        <div class="text-[#EEEEEE] mx-[8px]">
          |
        </div>
        <div class="flex items-center">
          <div>服务人数</div>
          <div class="text-[#3887F5] ml-[4px] text-[16px] font-bold">
            {{ data.serviceNum }}
          </div>
        </div>
        <div class="text-[#EEEEEE] mx-[8px]">
          |
        </div>
        <div class="flex items-center">
          <div>响应速度</div>
          <div class="text-[#3887F5] ml-[4px] text-[16px] font-bold">
            {{ timeData.time }}{{ timeData.unit }}
          </div>
        </div>
      </div>
    </div>
    <!-- 律师档案 -->
    <lawyer-home-info
      v-model="show"
      :lawyerInfo="data"
    />
    <lawyer-home-share-card
      v-model="share"
      :lawyerInfo="data"
    />
  </div>
</template>

<script>
import { formatTimeTwoUnit } from "@/libs/filter.js";
import LawyerHomeInfo from "@/pages/sub/lawyer-home/components/lawyer-home-info.vue";
import LawyerHomeShareCard from "@/pages/sub/lawyer-home/components/lawyer-home-share-card.vue";
import { turnToProfileInfoPage } from "@/libs/turnPages";

export default {
  name: "LawyerHomeCard",
  components: { LawyerHomeShareCard, LawyerHomeInfo },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    isSelf: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      /** 控制律师档案 */
      show: false,
      /** 控制分享显示 */
      share: false,
    };
  },
  computed: {
    timeData() {
      return formatTimeTwoUnit(this.data.responseTime);
    },
    workField() {
      return this.data?.workField?.slice(0, 3)?.join?.("、");
    },
    score() {
      return this.data.score?.toFixed?.(1) || 0;
    },
    isVerify() {
      return this.$store.getters["user/lawyerCertStatus"];
    },
  },
  methods: {
    handleShare() {
      this.share = true;
      console.log("分享");
    },
    /** 查看档案 */
    handleShow() {
      this.show = true;
    },
    /** 点击进入到修改资料页面 */
    handleEdit() {
      turnToProfileInfoPage();
    },
  },
};
</script>
