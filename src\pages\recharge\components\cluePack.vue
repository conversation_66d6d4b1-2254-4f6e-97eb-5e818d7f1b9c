<template>
  <div class="clue-pack">
    <div class="wrapper">
      <top-card
        :info="cardInfo"
        @numberClick="turnToRightsDetailPage"
        @tipClick="turnToMyWallet"
      />
      <div class="tab-bar flex">
        <div
          v-for="item in tabs"
          :key="item.value"
          class="tab-item"
          :class="{active:activeTab===item.value}"
          @click="tabClick(item)"
        >
          {{ item.label }}
        </div>
      </div>
      <div
        v-if="countDownTime"
        class="time-wr"
      >
        <div class="box">
          <img
            class="img"
            src="@/pages/recharge/img/<EMAIL>"
            alt=""
          >
          <img class="img-bg" src="@/pages/recharge/img/<EMAIL>" alt="">
          <div class="relative flex">
            距活动结束还剩
            <u-count-down
              :time="countDownTime"
              format="DD:HH:mm"
              autoStart
              @change="onChange"
              @finish="countDownTime=0"
            >
              <view class="time">

                <text class="time__item">
                  {{ timeData.hours>10?timeData.hours:'0'+timeData.hours }}
                </text><text class="semi">
                  :
                </text>
                <text class="time__item">
                  {{ timeData.minutes>10?timeData.minutes:'0'+timeData.minutes }}
                </text><text class="semi">
                  :
                </text>
                <text class="time__item">
                  {{ timeData.seconds>10?timeData.seconds:'0'+timeData.seconds }}
                </text>
              </view>
            </u-count-down>
          </div>
        </div>
      </div>
      <div class="type-list flex">
        <div
          v-for="item in rechargedGoods"
          :key="item.goodsId"
          class="type-card"
          :class="{active:goodsId===item.goodsId}"
          @click="typeCardClick(item)"
        >
          <div class="flex-1">
            <div class="name">
              {{ item.goodsName }}
            </div>
            <div class="price">
              <span class="unit">¥</span>{{ priceNumber(item.goodsPresentPrice) }}
            </div>
            <div class="old-price">
              ¥{{ priceNumber(item.goodsOriginalPrice) }}
            </div>
          </div>
          <img
            class="check img"
            src="@/pages/recharge/img/<EMAIL>"
            alt=""
          >
          <div class="check tip">
            {{ item.activityTag }}
          </div>
        </div>
        <!--占位-->
        <div />
      </div>
      <img
        class="info-img"
        mode="widthFix"
        :src="currentGoods.goodsDetailPic"
        alt=""
      >
    </div>

    <div class="fixed-btn">
      <div class="text-line">
        *该产品属于数字化商品，不支持七天无理由退款
      </div>
      <div class="btn-wrapper flex flex-space-between flex-align-center">
        <div @click="turnToServiceCenterPage">
          <img
            class="call-img"
            src="@/pages/recharge/img/<EMAIL>"
            alt=""
          >
          <div class="call-text">
            客服
          </div>
        </div>
        <div
          class="btn"
          @click="toPay"
        >
          ¥{{ priceNumber(currentGoods.goodsPresentPrice) }} 立即开通
        </div>
      </div>
      <u-safe-bottom />
    </div>
  </div>
</template>

<script>
import TopCard from "@/pages/recharge/components/topCard.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { dataDetailList } from "@/api";
import { canGrabClueNum, caseSourceGoodsGetByType } from "@/api/recharge";
import { priceNumber } from "../../../libs/tool";
import {
  turnToCluePackConfirmOrderPage,
  turnToMyWallet,
  turnToRightsDetailPage,
  turnToServiceCenterPage
} from "@/libs/turnPages";

export default {
  components: { USafeBottom, TopCard },
  data() {
    return {
      cardInfo: {
        title: "当前可抢案源（次）",
        number: 0,
      },
      tabs: [],
      activeTab: 1,
      goodsId: 1,
      countDownTime: 0,
      rechargedGoods: [],
      currentGoods: {
        goodsDetailPic: "",
      },
      timeData: {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
      },
      canGrabClueNum: 0,
    };
  },
  mounted() {
    dataDetailList({
      groupCode: "LAWYER_CLUE_PACK_TYPE",
    }).then(({ data = [] }) => {
      this.tabs = data.map(item => ({
        ...item,
        value: Number(item.value),
      }));
      // remark: "selected" 选中的tab
      const active = this.tabs.find(item => item.remark === "selected").value || this.tabs[0].value;
      this.activeTab = active;
      this.caseSourceGoodsGetByType(active);
    });
    canGrabClueNum().then(({ data = {} }) => {
      this.cardInfo.number = data.canGrabClueNum || 0;
    });
  },
  methods: {
    turnToServiceCenterPage,
    turnToMyWallet,
    turnToRightsDetailPage,
    priceNumber,
    tabClick(item) {
      this.activeTab = item.value;
      this.caseSourceGoodsGetByType(item.value);
    },
    typeCardClick(item) {
      this.goodsId = item.goodsId;
      this.currentGoods = item;
    },
    caseSourceGoodsGetByType(goodsType) {
      caseSourceGoodsGetByType({
        goodsType,
      }).then(({ data = [] }) => {
        this.countDownTime = data.countDownTime * 1000;
        this.rechargedGoods = data.rechargedGoods;
        this.goodsId = data.rechargedGoods[0].goodsId;
        this.currentGoods = data.rechargedGoods[0];
      });
    },
    onChange(e) {
      e.hours = e.hours + e.days * 24;
      this.timeData = e;
    },
    toPay() {
      turnToCluePackConfirmOrderPage({ goodsId: this.goodsId });
    },
  },
};
</script>

<style lang="scss" scoped>
.clue-pack{
  padding-bottom: 160px;
  .wrapper{
    padding: 0 16px;
    .tab-bar{
      height: 44px;
      line-height: 44px;
      text-align: center;
      font-size: 12px;
      font-weight: 400;
      color: #333;
      background: #fff;
      border-radius: 12px 12px 0px 0px;
      margin-top: 16px;
      .tab-item{
        flex: 1;
        position: relative;
        border-radius: 12px 12px 0px 0px;
        &.active{
          font-size: 14px;
          font-weight: 600;
          color: #9F6310;
          background: linear-gradient(180deg, #FFF4E2 0%, #FFFFFF 100%);
          &::after{
            content: '';
            position: absolute;
            bottom: 0;
            left: calc(50% - 8px);
            width: 16px;
            height: 3px;
            background: #9F6310;
            border-radius: 8px;
          }
        }
      }
    }
    .time-wr{
       background: #fff;
      padding: 16px 16px 0;
      .box{
        position: relative;
        padding-left: 115px;
        line-height: 24px;
        border-radius: 12px;
        box-sizing: border-box;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        .time{
          margin-left: 8px;
        }
        .time__item{
          color: #F34747;
          font-weight: bold;
        }
        .semi{
          display: inline-block;
          width: 12px;
          text-align: center;
          color: #F34747;
        }
      }

      .img{
        width: 108px;
        height: 24px;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 2;
      }
      .img-bg{
        position: absolute;
        right: 0;
        top: 0;
        z-index: 1;
        width: 240px;
        height: 24px;
      }
      .relative{
        position: relative;
        z-index: 3;
      }
    }
    .type-list{
      background: #fff;
      white-space: nowrap;
      overflow-x: auto;
      padding: 14px 0 24px 16px;
      box-sizing: border-box;
      .type-card{
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 108px;
        height: 120px;
        flex-shrink: 0;
        background: #F5F5F7;
        border-radius: 8px ;
        border: 1px solid #EEEEEE;
        margin-right: 12px;
        position: relative;
        box-sizing: border-box;
        text-align: center;
        .check{
          display: none;
        }
        &.active{
          border: 2px solid #CC974E;
          background: #FCF8F0;
          box-shadow: 0px 4px 12px 0px rgba(204,151,78,0.2);

          .check{
            display: block;
          }
          .img{
            width: 32px;
            height: 32px;
            position: absolute;
            bottom: -1px;
            right: -1px;
          }
          .tip{
            position: absolute;
            top: -2px;
            left: -2px;
            padding: 0 8px;
            line-height: 18px;
            background: linear-gradient(131deg, #78D6AF 0%, #22BF7E 100%);
            border-radius: 8px 2px 8px 2px;
            font-size: 12px;
            font-weight: 400;
            color: #FFFFFF;
            text-align: center;
          }
        }
        .name{
          font-size: 14px;
          font-weight: 400;
          color: #73470B;
          white-space: normal;
        }
        .price{
          font-size: 18px;
          font-weight: 500;
          color: #EB4738;
          margin-top: 8px;
          line-height: 23px;
          .unit{
            font-size: 11px;
          }
        }
        .old-price{
          margin-top: 4px;
          font-size: 11px;
          font-weight: 400;
          color: #C2A67F;
          text-decoration: line-through;
        }
      }
    }
    .info-img{
      width: 100%;
    }
  }
  .fixed-btn{
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding-top: 2px;
    background: #fff;
    .text-line{
      font-size: 12px;
      font-weight: 400;
      color: #999999;
      line-height: 16px;
      margin-bottom: 8px;
      text-align: center;
    }
    .btn-wrapper{
      box-shadow: 0px -10px 10px 0px rgba(0,0,0,0.08);
      border-radius: 16px 16px 0px 0px;
      padding:8px 16px 8px 29px;
      .call-img{
        width: 24px;
        height: 24px;
      }
      .call-text{
        font-weight: 400;
        margin-top: 2px;
        text-align: center;
        font-size: 11px;
        color: #333333;
        line-height: 13px;
      }
      .btn{
        text-align: center;
        width: 280px;
        line-height: 44px;
        background: linear-gradient(109deg, #FF913E 0%, #F54A3A 100%);
        border-radius: 22px 22px 22px 22px;
        font-size: 16px;
        font-weight: 500;
        color: #FFFFFF;
      }
    }

  }
}
</style>
