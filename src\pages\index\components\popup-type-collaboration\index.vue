<template>
  <div>
    <float-popup
      :contentWrapperStyle="contentWrapperStyle"
      :show="show"
      @maskClick="$emit('update:show', false)"
    >
      <view class="geo-wrapper">
        <div class="geo-wrapper-con flex">
          <div class="left-province">
            <scroll-view
              class="scroll-view "
              scrollY="true"
            >
              <div
                v-for="item in parent"
                :key="item.value"
                :class="[
                  {'!text-[#3887F5]':item.value === parentActive.value}
                ]"
                class="geo-line fontStyle flex flex-align-center"
                @click="parentClick(item)"
              >
                {{ item.label }}
                <div
                  v-if="item.value === parentActive.value"
                  class="active"
                />
              </div>
            </scroll-view>
          </div>
          <div class="right-city">
            <scroll-view
              class="scroll-view "
              scrollY="true"
            >
              <div
                v-for="item in child"
                :key="item.value"
                :class="[
                  {'!text-[#3887F5]':item.value === childActive.value}
                ]"
                class="geo-line fontStyle flex flex-align-center flex-space-between"
                @click="childClick(item)"
              >
                {{ item.label }}
                <span
                  v-if="item.value === childActive.value"
                  class="active iconfont icon-a-zhengquewancheng"
                />
              </div>
            </scroll-view>
          </div>
        </div>
      </view>
    </float-popup>
  </div>
</template>

<script>
import FloatPopup from "@/pages/index/components/floatPopup.vue";
import { dataDetailList } from "@/api";

export default {
  name: "PopupTypeCollaboration",
  components: { FloatPopup },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    contentWrapperStyle: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      parent: [{
        label: "异地查档",
        value: "1",
        key: "LAWYER_COLLABORATION_CD_TYPE"
      }, {
        label: "案件协作",
        value: "2",
        key: "LAWYER_COLLABORATION_XZ_TYPE"
      }],
      child: [],
      parentActive: {},
      childActive: {},
    };
  },
  mounted() {
    this.parentClick(this.parent[0]);
  },
  methods: {
    parentClick(item){
      this.parentActive = item;
      this.getChildRequest(item.key);
    },
    getChildRequest(key){
      /* 获取协作类型列表*/
      dataDetailList({
        groupCode: key
      }).then(({ data = [] }) => {
        this.child = data;
      });
    },
    childClick(data){
      this.childActive = data;
      this.$emit("update:show", false);
      this.$emit("setType", {
        parentActive: this.parentActive,
        childActive: this.childActive
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.geo-wrapper {
  border-radius: 0 0 16px 16px;

  &-title {
    height: 44px;
    padding: 0 16px;
    border-bottom: 1px solid #eee;

    .geo-current {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
    }

    .geo-reset {
      font-size: 14px;
      font-weight: 400;
      color: #3887f5;

      .location-icon {
        width: 16px;
        height: 16px;
        margin-right: 2px;
      }
    }
  }

  &-con {
    .scroll-view {
      height: 232px;
    }

    .fontStyle {
      font-size: 14px;
      font-weight: 400;
      color: #333333;
    }

    .left-province {
      flex: 0 0 130px;

      .geo-line {
        height: 44px;
        position: relative;
        padding-left: 16px;

        .active {
          position: absolute;
          width: 4px;
          height: 20px;
          background: #3887f5;
          border-radius: 20px 20px 20px 20px;
          left: 0;
        }
      }
    }

    .right-city {
      flex: 1;
      border-left: 1px solid #eee;
      padding-right: 16px;
      .geo-line {
        padding: 12px 0 16px 12px;
        box-sizing: border-box;
        height: 44px;

        .iconfont {
          font-size: 20px;
          color: #3887f5;
        }
      }
    }
  }
}
</style>
