<template>
  <div class="card">
    <div class="card-title">
      请填写{{ LAWYER_SERVICE_ENUM_TO_CHINESE[scene] }}信息
    </div>
    <div class="card-info flex flex-align-center">
      <find-lawyer-card class="mg-r-4" />
      <div><span class="text-3887f5">30</span>位律师正在等待为您服务</div>
    </div>
    <div>
      <service-introduction-text-area v-model="saveAppointParams.info" />

      <div
        class="card-button"
        @click="payClick"
      >
        发起委托 ¥{{ serviceData.servicePrice | amountFilter }}
      </div>
    </div>
  </div>
</template>
<script>
import {
  consultationAliContent,
  serviceManegeInfoCommon,
} from "@/api";
import { whetherToLogIn } from "@/libs/tools.js";
import { getParalegalDataId, saveParalegalData } from "@/libs/paralegalData.js";
import { toConfirmOrder } from "@/libs/turnPages.js";
import Store from "@/store";
import ServiceIntroductionTextArea from "@/pages/sub/lawyer-home/lawyer-services/components/ServiceIntroductionTextArea.vue";
import { LAWYER_SERVICE_ENUM_TO_CHINESE } from "../js";
import FindLawyerCard from "@/pages/findlawyer/components/findlawyer-card/index.vue";

export default {
  name: "ServiceIntroduction",
  computed: {
    LAWYER_SERVICE_ENUM_TO_CHINESE() {
      return LAWYER_SERVICE_ENUM_TO_CHINESE;
    },
  },
  components: {
    FindLawyerCard,
    ServiceIntroductionTextArea,
  },
  data() {
    return {
      /** 公共付费信息 */
      serviceData: {},
      /** 点击按钮后需要传递给后端的信息 */
      saveAppointParams: {
        /** 问题描述 */
        info: "",
        /** 问题类型 */
        typeLabel: "",
        /** 问题描述 */
        typeValue: 0,
        /** 输入场景 1 问律师假im引导页 2 下单页传入 3服务器下单 */
        type: 2,
      },
    };
  },
  props: {
    /** 服务code */
    scene: {
      type: String,
      required: true,
    },
  },
  methods: {
    /** 获取公共付费信息 */
    getPayInfo() {
      // 约定 1v1_ + 服务code = 场景值
      serviceManegeInfoCommon("gg_" + this.scene).then(({ data = {} }) => {
        this.serviceData = data;
      });
    },
    /** 选择咨询类型触发的回调 */
    selectType(val) {
      this.$set(this.saveAppointParams, "typeValue", val.typeValue);
      this.$set(this.saveAppointParams, "typeLabel", val.typeLabel);
    },
    /** 验证参数是否合理 */
    async validateParam() {
      const info = this.saveAppointParams.info.trim();

      // 验证问题描述长度
      if (info.length === 0) {
        return uni.showToast({
          title: "请输入问题描述",
          duration: 2000,
          icon: "none",
        });
      } else if (info.length < 10) {
        return uni.showToast({
          title: "最少10个字哦~",
          duration: 2000,
          icon: "none",
        });
      }

      const res = await consultationAliContent({
        description: info,
      });

      this.selectType(res.data);

      // 验证是否选择了问题类型
      if (!this.saveAppointParams.typeValue) {
        const res = await consultationAliContent({
          description: info,
        });

        this.selectType(res.data);
      }

      return false;
    },
    /** 点击支付后触发的操作 */
    async payClick() {
      if (await this.validateParam()) return;

      const payFn = () => {
        /* 先判断是否登录 */
        whetherToLogIn(async () => {
          const params = { ...this.saveAppointParams };

          await saveParalegalData(params);

          toConfirmOrder({
            serviceCode: this.serviceData.serviceCode,
            synHistoryId: getParalegalDataId(),
            // type: this.serviceData.type
          });
        });
      };

      // 弹出登陆
      whetherToLogIn(async () => {
        payFn();

        // 将付费方法存入vuex
        Store.commit("payState/SET_PAY_FAIL_POPUP_PAY", payFn);
      });
    },
  },
  watch: {
    scene: {
      handler(value) {
        if (value) {
          this.getPayInfo();
        }
      },
      immediate: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  width: 351px;
  box-sizing: border-box;
  margin: 0 auto;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;

  &-title {
    font-size: 18px;
    font-weight: bold;
    color: #222222;
    padding: 16px 0 0 16px;
  }

  &-info {
    font-size: 14px;
    font-weight: 400;
    padding: 12px;
  }
}

.card-button {
  width: 327px;
  height: 44px;
  line-height: 44px;
  background: #3887f5;
  border-radius: 68px 68px 68px 68px;
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
  margin: 16px 12px 20px 12px;
}
</style>
