<template>
  <div>
    <div class="px-[12px] py-[16px]">
      <!--  统计数据    -->
      <div class="bg-[linear-gradient(_270deg,_#FFECE5_0%,_#FEFBFA_36%,_#FFFFFF_100%)] rounded-[8px]">
        <div class="mx-[16px] border-0 border-b-[1px] border-solid border-[#EEEEEE] h-[36px] items-center flex justify-between">
          <div class="flex items-center">
            <img
              alt=""
              class="w-[16px] h-[16px]"
              src="@/pages/sub/imgs/<EMAIL>"
            >
            <p class="text-[14px] text-[#333333] px-[4px]">
              问答解答曝光数据
            </p>
            <i
              class="iconfont icon-yiwen !text-[16px] text-[#666666]"
              @click.stop="changeProtocolState"
            />
          </div>
          <div
            class="flex items-center"
            @click.stop="changeExposureDiagramPopUp(true)"
          >
            <p class="text-[12px] text-[#666666]">
              曝光示例图
            </p>
            <i class="iconfont icon-erjiyoujiantou !text-[16px] text-[#666666]" />
          </div>
        </div>
        <div class="flex">
          <div
            v-for="i in statisticsList"
            :key="i.key"
            class="flex-1 flex flex-column items-center justify-center h-[70px]"
          >
            <p class="font-bold text-[20px] text-[#EB4738]">
              {{ statisticsInfo[i.key]?statisticsInfo[i.key]:0 }}<span class="text-[10px] text-[#EB4738] font-normal">{{ i.unit }}</span>
            </p>
            <p class="text-[11px] text-[#666666] pt-[4px]">
              {{ i.label }}
            </p>
          </div>
        </div>
      </div>
      <!-- 我的问答列表     -->
      <div class="overflow-hidden">
        <!-- 没有数据的时候        -->
        <div
          v-if="isArrNull(list)&&isRequest"
          class="pt-[24px] flex-column flex items-center justify-center"
        >
          <img
            alt=""
            class="w-[240px]"
            mode="widthFix"
            src="@/pages/sub/imgs/<EMAIL>"
          >
          <div
            class="flex items-center justify-center mt-[24px] w-[160px] h-[32px] bg-[#3887F5] rounded-[68px] font-bold text-[14px] text-[#FFFFFF]"
            @click.stop="toAnswerSquare(true)"
          >
            去问答广场看看
          </div>
        </div>
        <div
          v-for="i in list"
          :key="i.id"
          class="mt-[12px] bg-[#FFFFFF] rounded-[8px] p-[16px] relative"
          @click.stop="toDetail(i)"
        >
          <img
            v-if="i.checkStatus===2"
            alt=""
            class="absolute w-[56px] h-[56px] left-[71px] top-0"
            src="@/pages/sub/imgs/<EMAIL>"
          >
          <img
            v-if="i.hasAccepted===1"
            alt=""
            class="absolute w-[56px] h-[56px] left-[71px] top-0"
            src="@/pages/sub/imgs/<EMAIL>"
          >
          <div class="flex pb-[12px] justify-between items-center text-[12px] text-[#999999]">
            <p>{{ i.userName }}</p>
            <p>{{ i.createTimeDesc }}</p>
          </div>
          <div class="text-[15px] text-[#333333] text-ellipsis-2">
            <span class="mr-[4px] bg-[#EBF3FE] rounded-[4px] text-[12px] text-[#3887F5] px-[4px] py-[2px]">{{ i.typeLabel }}</span>{{ i.detail }}
          </div>
          <!--  额外奖励  -->
          <div
            v-if="i.replyProfitAmount"
            class="pt-[12px]"
          >
            <div class="flex px-[12px] items-center h-[32px] bg-[#FFFAF0] rounded-[8px] border-[1px] border-solid border-[#FFECC8]">
              <img
                alt=""
                class="w-[20px] h-[20px]"
                src="@/pages/sub/imgs/<EMAIL>"
              >
              <p class="flex-1 pl-[8px] text-[13px] text-[#333333]">
                奖励法临币 +{{ priceNumber(i.replyProfitAmount) }}
              </p>
              <p
                v-if="i.acceptedProfitAmount"
                class="text-[13px] text-[#FA700D]"
              >
                采纳额外 +{{ priceNumber(i.acceptedProfitAmount) }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <sub-tab-bar
      :list="QA_TAB_BAR_LIST"
      activeValue="itSMine"
    />
    <!--曝光说明    -->
    <protocol-pop-up
      v-model="protocolState"
      :protocolContent="protocolContent"
      btnText="我了解了"
      height="182"
      protocolTitle="问答解答曝光数据说明"
    >
      <div>
        <div style="white-space: pre-wrap; line-height: 20px">
          {{ protocolContent }}
        </div>
      </div>
    </protocol-pop-up>

    <!-- 曝光示意图   -->
    <app-popup
      :safeAreaInsetBottom="false"
      :show="exposureDiagramPopUpStatus"
      :zIndex="99999"
      closeable
      mode="center"
      @cancel="changeExposureDiagramPopUp(false)"
    >
      <div class="w-[311px] flex flex-column items-center pb-[24px]">
        <p class="py-[24px] font-bold text-[16px] text-[#333333] text-align-center">
          曝光示意图
        </p>
        <img
          alt=""
          class="w-[180px]"
          mode="widthFix"
          src="@/pages/sub/imgs/<EMAIL>"
        >
      </div>
    </app-popup>
  </div>
</template>

<script>
import SubTabBar from "@/pages/sub/components/TabBar/index.vue";
import { QA_TAB_BAR_LIST } from "@/enum/qa";
import ProtocolPopUp from "@/components/protocolPopUp/index.vue";
import { EXPOSURE_DESCRIPTION } from "@/protocol/qa";
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { qaMessagePageList } from "@/api";
import { toAnswerDetails, toAnswerSquare } from "@/libs/turnPages";
import { lawyerNewExposure } from "@/api/qa";
import { priceNumber } from "@/libs/tools";
import { isArrNull } from "@/libs/basics-tools";

export default {
  name: "MyQa",
  components: { AppPopup, ProtocolPopUp, SubTabBar },
  data() {
    return {
      QA_TAB_BAR_LIST,
      /* 协议弹窗状态 */
      protocolState: false,
      /* 曝光示意图弹窗状态 */
      exposureDiagramPopUpStatus: false,
      protocolContent: EXPOSURE_DESCRIPTION,
      statisticsList: [{
        label: "今日曝光数",
        key: "exposureCount",
        unit: "次",
      }, {
        label: "累计曝光数",
        key: "totalExposureCount",
        unit: "次",
      }, {
        label: "今日1v1咨询数",
        key: "ffzxExposureCount",
        unit: "次",
      }, {
        label: "累计1v1咨询数",
        key: "totalFfzxExposureCount",
        unit: "次",
      }],
      /* 统计数据 */
      statisticsInfo: {},
      query: {
        qaMessageRequestType: 5,
        payStatus: 0,
        currentPage: 1,
        pageSize: 10
      },
      isRequest: false,
      isLastPage: false,
      list: []
    };
  },
  onReachBottom() {
    this.query.currentPage++;
    this.getList();
  },
  onPullDownRefresh() {
    console.log("refresh");
    this.resetData();
    this.getStatisticsData();
    this.getList().finally(() => {
      uni.stopPullDownRefresh();
    });
  },
  onShow(){
    this.resetData();
    this.getStatisticsData();
    this.getList();
  },
  methods: {
    isArrNull,
    toAnswerSquare,
    priceNumber,
    changeProtocolState(){
      this.protocolState = true;
    },
    changeExposureDiagramPopUp(state = false){
      this.exposureDiagramPopUpStatus = state;
    },
    /* 获取统计数据 */
    getStatisticsData(){
      return lawyerNewExposure({
        exposureType: 1
      }).then(({ data = {} }) => {
        this.statisticsInfo = data;
        return data;
      });
    },
    /** 重置数据 */
    resetData() {
      this.list = [];
      this.query.currentPage = 1;
      this.isLastPage = false;
      this.isRequest = false;
    },
    getList(){
      if (this.isLastPage) {
        return Promise.resolve();
      }
      return qaMessagePageList(this.query).then(res => {
        this.list = [...this.list, ...res.data.records];
        if (this.list.length >= res.data.total) {
          this.isLastPage = true;
        }
        return this.list;
      })
        .finally(() => {
          this.isRequest = true;
        });
    },
    toDetail(data){
      toAnswerDetails({
        id: data.id
      });
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
