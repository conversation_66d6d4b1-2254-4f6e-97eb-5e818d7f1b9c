<template>
  <div class="float-popup">
    <div
      v-show="show"
      class="mask"
      @click="maskClick"
    />
    <div
      v-show="show"
      :style="[contentStyle]"
      class="content-wrapper"
    >
      <slot />
    </div>
  </div>
</template>

<script>

export default {
  name: "FloatPopup",
  components: {},
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    contentWrapperStyle: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    contentStyle() {
      return uni.$u.addStyle(this.contentWrapperStyle);
    }
  },
  methods: {
    maskClick() {
      this.$emit("maskClick");
    },
  },
};
</script>

<style lang="scss" scoped>
.float-popup {
  position: relative;

  .mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    bottom: 0;
    z-index: 1;
    background: rgba(0, 0, 0, .5);
  }

  .content-wrapper {
    border-top: 1px solid #eee;
    position: fixed;
    background: #F5F5F7;
    border-radius: 0 0 16px 16px;
    width: 100%;
    z-index: 99;

  }
}

</style>
