<template>
  <div>
    <div class="charts-box">
      <qiun-data-charts
        :canvas2d="true"
        :chartData="chartData"
        :opts="opts"
        canvasId="xOpDoV9RXudavlQW"
        type="area"
      />
    </div>
  </div>
</template>

<script>
import QiunDataCharts from "@/pages/sub/lawyer-home/ucharts/components/qiun-data-charts/qiun-data-charts.vue";

/**
 * 近期案件数
 */
export default {
  name: "NumberRecentCases",
  components: { QiunDataCharts },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      chartData: {},
      // 您可以通过修改 config-ucharts.js 文件中下标为 ['mount'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
      opts: {
        color: ["#3887F5"],
        legend: {
          show: false,
        },
        dataLabel: false,
        fontSize: 9,
        xAxis: {
          disableGrid: true,
          fontSize: 10,
          fontColor: "#999999",
          boundaryGap: "justify",
        },
        yAxis: {
          disableGrid: true,
          data: [
            {
              min: 0,
            },
          ],
        },
        extra: {
          area: {
            type: "curve",
            opacity: 0.2,
            addLine: true,
            width: 2,
            gradient: true,
          },
          tooltip: {
            showBox: false,
            splitLine: false,
          },
        },
      },
    };
  },
  watch: {
    data: {
      handler() {
        this.getServerData();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    getServerData() {
      console.log("重新绘制");

      const data =
        this.data.map((item) => [item.label, Number(item.count)]) || [];

      let res = {
        series: [
          {
            name: "时间轴1",
            data,
          },
        ],
      };
      this.chartData = JSON.parse(JSON.stringify(res));
    },
  },
};
</script>

<style lang="scss" scoped></style>
