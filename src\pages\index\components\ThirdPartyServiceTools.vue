<template>
  <div
    v-if="!isArrNull(list)"
    class="mx-[12px] bg-white mt-[8px]  rounded-[8px]"
  >
    <p
      class="font-bold text-[16px] text-[#333333] pl-[16px] py-[16px]"
    >
      第三方服务
    </p>
    <div class="grid grid-cols-4">
      <div
        v-for="i in list"
        :key="i.value"
        class="pt-[8px] pb-[8px] box-border"
        @click.stop="goMiniProgram(i)"
      >
        <img
          :src="i.ext.icon"
          alt=""
          class="w-[44px] h-[44px] rounded-full block mx-auto"
        >
        <p
          class="mt-[6px] w-full text-center text-[13px] text-[#333333]"
        >
          {{ i.label }}
        </p>
      </div>
    </div>
  </div>
</template>

<script>

import { dataDictionary } from "@/api";
import { isArrNull } from "@/libs/basics-tools";

export default {
  name: "ThirdPartyServiceTools",
  data() {
    return {
      list: []
    };
  },
  mounted() {
    /* 获取第三方工具字典 */
    dataDictionary({
      groupCode: "B_SIDE_APPLET_THIRD_PARTY_TOOL",
    }).then(({ data = [] }) => {
      this.list = data.map((item) => {
        try {
          return {
            ...item,
            ext: JSON.parse(item.remark) || {},
          };
        } catch (e) {
          return {
            ...item,
            ext: {},
          };
        }
      });
      console.log(this.list, "this.list ");
    });
  },
  methods: {
    isArrNull,
    /* 去小程序 */
    goMiniProgram(data) {
      const {
        path = "",
        openEmbeddedMiniProgram = false,
        extraData = {},
        appId = data.value,
      } = data.ext || {};
      const openToMiniProgram = openEmbeddedMiniProgram
        ? uni.openEmbeddedMiniProgram
        : wx.navigateToMiniProgram;
      openToMiniProgram({
        appId,
        path,
        extraData,
        success(res) {
          // 打开小程序成功
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">

</style>