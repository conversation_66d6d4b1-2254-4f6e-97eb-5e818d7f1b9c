<template>
  <app-popup
    :safeAreaInsetBottom="false"
    :show="getShow"
    :zIndex="99999"
    mode="center"
  >
    <div class="protocol-modal">
      <div class="protocol-content">
        <div class="protocol-title">
          {{ protocolTitle }}
        </div>
        <slot>
          <scroll-view
            :style="scrollCustomStyle"
            scrollY="true"
          >
            <div style="white-space: pre-wrap; line-height: 20px">
              {{ protocolContent }}
            </div>
          </scroll-view>
        </slot>
      </div>
      <div
        class="protocol-btn"
        @click="close"
      >
        {{ btnText }}
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { pxToRpx } from "@/libs/tools";

export default {
  name: "ProtocolPopUp",
  components: { AppPopup },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    protocolTitle: {
      type: String,
      default: "协议标题"
    },
    protocolContent: {
      type: String,
      default: "协议内容"
    },
    height: {
      type: [String, Number],
      default: "300"
    },
    btnText: {
      type: String,
      default: "我知道了"
    }
  },
  computed: {
    getShow: {
      get(){
        return this.value;
      },
      set(val){
        this.$emit("input", val);
      }
    },
    /* 自定义style*/
    scrollCustomStyle() {
      return this.$u.addStyle({
        height: pxToRpx(Number(this.height))
      }, "string");
    }
  },
  methods: {
    close(){
      this.getShow = false;
    }
  }
};
</script>

<style lang="scss" scoped>


.protocol-modal {
  width: 311px;
  background: #fff;
  border-radius: 16px;

  .protocol-content {
    padding: 24px;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 16px;

    .protocol-title {
      font-size: 16px;
      font-weight: bold;
      color: #333333;
      margin-bottom: 12px;
      text-align: center;
    }

    .protocol-nr {
      height: 300px;
    }
  }

  .protocol-btn {
    height: 46px;
    line-height: 46px;
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    color: #3887f5;
    border-top: 1px solid #eeeeee;
  }
}
</style>
