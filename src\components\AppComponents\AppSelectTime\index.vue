<template>
  <div class="timer">
    <datetime-picker
      v-model="value1"
      :minDate="0"
      :show="show"
      mode="year-month"
      round="16"
      title="时间选择"
      @cancel="show = false"
      @confirm="confirm"
    />
    <div @click="show = true">
      <app-field
        :value="showTime"
        align="left"
        arrow
        color="#333333"
        placeholder="请选择"
        placeholderColor="#CCCCCC"
      />
    </div>
  </div>
</template>

<script>
import DatetimePicker from "@/uview-ui/components/u-datetime-picker/u-datetime-picker.vue";
import AppField from "@/components/AppComponents/AppField/index.vue";
import dayjs from "dayjs";

export default {
  name: "AppSelectTime",
  components: { AppField, DatetimePicker },
  props: {
    value: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      show: false,
      // "YYYY-MM" 时间戳
      value1: dayjs(dayjs().format("YYYY-MM")).valueOf(),
    };
  },
  computed: {
    /** 展示时间 */
    showTime() {
      return this.value ? dayjs(this.value).format("YYYY-MM") : null;
    },
  },
  methods: {
    confirm({ value }) {
      this.$emit("input", value);
      this.show = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.timer {
  ::v-deep .u-popup__content {
    border-radius: 16px 16px 0 0;
  }
}
</style>
