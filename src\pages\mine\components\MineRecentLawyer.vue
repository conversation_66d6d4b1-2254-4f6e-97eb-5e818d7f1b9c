<template>
  <div
    v-if="!isArrNull(lawyerList)"
    class="mt-[12px]"
  >
    <div class="w-[351px] box-border bg-[#FFFFFF] rounded-[12px] pb-[16px]">
      <div class="font-bold text-[16px] text-[#333333] px-[16px] py-[12px]">
        最近联系的律师
      </div>
      <div
        class="mx-[12px] position-relative"
      >
        <img
          alt=""
          class="block w-[30px] h-full absolute right-0 top-0"
          src="@/pages/mine/img/<EMAIL>"
        >
        <div class="flex items-center space-x-[8px] overflow-x-auto">
          <div
            v-for="item in lawyerList"
            :key="item.id"
            class="shrink-0 pb-[10px] bg-[#F5F5F7] rounded-[6px] p-[10px] box-border"
          >
            <mine-recent-lawyer-card :data="item" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { isArrNull } from "@/libs/basics-tools";
import MineRecentLawyerCard from "@/pages/mine/components/MineRecentLawyerCard.vue";

export default {
  name: "MineRecentLawyer",
  components: { MineRecentLawyerCard },
  data(){
    return {
      popup: false
    };
  },
  computed: {
    lawyerList(){
      return this.$store.state.recentLawyer.recentLawyerList;
    }
  },
  methods: {
    isArrNull,
  }
};
</script>
