<template>
  <!-- <div> -->
  <app-popup
    mode="bottom"
    round="16"
    :show="getShow"
    @close="close"
  >
    <div class="header flex flex-space-between flex-align-center">
      <p
        class="close"
        @click="$emit('update:show', false)"
      >
        取消
      </p>
      <p class="title flex-1 text-align-center">
        选择现金券
      </p>
      <p
        class="determine"
        @click="determine"
      >
        确定
      </p>
    </div>
    <div class="coupon-list">
      <div
        v-for="(i) in list"
        :key="i.id"
      >
        <div class="coupon-content">
          <div
            class="coupon-item flex flex-align-center"
            @click="selectedCoupon(i)"
          >
            <div>
              <p class="money flex flex-space-center">
                <span class="unit">￥</span>{{ priceNumber(i.spec) }}
              </p>
<!--              <div class="rule-content">-->
<!--                {{ i.useRule }}-->
<!--              </div>-->
            </div>
            <div class="content flex-1">
              <div class="top flex flex-space-between flex-align-center">
                <p>{{ i.couponName }}</p>
                <img
                  v-if="couponInfo.id===i.id"
                  src="@/components/couponPopup/img/xiyichecked.png"
                  alt=""
                  class="img"
                >
                <img
                  v-else
                  src="@/components/couponPopup/img/zx_nocheck.png"
                  alt=""
                  class="img"
                >
              </div>
              <div class="bottom flex flex-space-between">
                <p class="p">
                  {{ i.endValidTime ? `有效期截至：${i.endValidTime}` : '无时间限制' }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </app-popup>
  <!-- </div> -->
</template>

<script>
import { isNull } from "@/libs/basics-tools";
import AppPopup from "@/components/AppComponents/AppPopup/index.vue";
import { priceNumber } from "@/libs/tool";

export default {
  name: "CouponPopup",
  components: { AppPopup },
  data () {
    return {
      activeIndex: 0,
      expanded: "",
      couponInfo: {}
    };
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => []
    },
    couponInfoProp: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    getShow: {
      get () {
        return this.show;
      },
      set (value) {
        this.$emit("update:show", value);
      }
    }
  },
  methods: {
    priceNumber,
    amountFilter (value) {
      if (!isNull(value)) {
        const num = (value / 100);
        if (/^\d+$/.test(String(num))) {
          return num;
        }
        return num.toFixed(2);
      }
      return 0;
    },
    determine () {
      this.$emit("selectedCoupon", this.couponInfo);
      this.getShow = false;
    },
    selectedCoupon (data) {
      this.couponInfo = data;
    },
    close () {
      this.getShow = false;
    }
  },
  watch: {
    getShow () {
      if (this.getShow) {
        this.couponInfo = this.couponInfoProp;
      }
    }
  }
};
</script>

<style scoped lang="scss">
.header{
  padding: 0 16px;
  height: 48px;
  .close{
    font-size: 14px;
    font-weight: 400;
    color: #999999;
  }
  .determine{
    font-size: 14px;
    font-weight: 400;
    color: #4A539E;
  }
  .title{
    font-size: 16px;
    font-weight: 500;
    color: #000000;
  }
}
.coupon-list{
  padding: 16px 16px 0;
  height: 305px;
  box-sizing: border-box;
  overflow-y: auto;
  .coupon-content{
    padding-bottom: 12px;
  }
  .coupon-item{
    position: relative;
    background: white;
    z-index: 2;
    border-radius: 8px 8px 8px 8px;
    opacity: 1;
    border: 1px solid #EEEEEE;
      .money{
        width: 104px;
        font-size: 24px;
        font-weight: 500;
        color: #EB4738;
        align-items: baseline;
        .unit{
          font-size: 12px;
          color: #EB4738;
          font-weight: 500;
        }
      }
      .content{
        padding: 16px 16px 16px 0;

        .top{
          font-size: 16px;
          font-weight: 500;
          color: #333333;
          .img{
            width: 16px;
            height: 16px;
          }
        }
        .bottom{
          padding-top: 12px;
          .p{
            font-size: 12px;
            font-weight: 400;
            color: #999999;
          }
          .rule-text{
            font-size: 10px;
            font-weight: 400;
            color: #999999;
            .img{
              margin-left: 4px;
              width: 12px;
              height: 12px;
              transform-origin:center;
              transform: rotate(0);
              transition: transform  0.2s;
            }
            .rotate-180{
              transform: rotate(180deg);
            }
          }
        }
      }
  }
  .rule-content{
    font-size: 12px;
    font-weight: 400;
    color: #333333;
    text-align: center;
  }
}
</style>
