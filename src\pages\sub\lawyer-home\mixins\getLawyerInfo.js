import { getCurrentPageRoute } from "@/libs/turnPages.js";
import { oneLawyer } from "@/api/lawyer.js";

/**
 * 获取律师信息
 */
export default {
  data() {
    return {
      lawyerInfo: {},
      /** 是否是自身 */
      isSelf: false,
    };
  },
  mounted() {
    this.getLawyerInfo();
  },
  methods: {
    /** 获取律师信息 */
    getLawyerInfo() {
      console.log(getCurrentPageRoute().query, "getCurrentPageRoute().query");
      const userId = this.$store.getters["user/getId"];

      const { id = userId } = getCurrentPageRoute().query;

      this.isSelf = id === userId;

      if (!id) return;

      oneLawyer({ id }).then((obj) => {
        if (obj.data.careerPic) {
          obj.data.careerPic = obj.data.careerPic
            ? obj.data.careerPic.split(",")
            : [];
        }

        if (obj.data.otherPic) {
          obj.data.otherPic = obj.data.otherPic?.split(",") || [];
        }

        if (obj.data.serviceManageV2VoList) {
          obj.data.serviceManageV2VoList?.forEach(
            (item) => (item.lawyerId = obj.data.id)
          );
        }

        this.lawyerInfo = obj.data;
      });
    },
  },
};
