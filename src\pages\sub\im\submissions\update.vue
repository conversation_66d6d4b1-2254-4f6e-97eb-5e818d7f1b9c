<template>
  <div class="submissions">
    <div
      v-for="(i,index) in list"
      :key="index"
      class="form-item"
    >
      <div class="title">
        {{ i.title }} <span
          v-if="i.required"
          class="required"
        >*</span>
      </div>
      <div class="textarea-container">
        <u--textarea
          v-model="i.value"
          placeholder="请输入内容"
        />
        <p
          v-if="i.error"
          class="error-tip"
        >
          {{ i.errorTip }}
        </p>
      </div>
    </div>
    <div class="btns flex flex-space-around">
      <div
        class="btn btn-border"
        @click="sendAfterReview"
      >
        评价后发送
      </div>
      <div
        class="btn btn-c"
        @click="sendDirectly"
      >
        直接发送
      </div>
    </div>
  </div>
</template>

<script>

import UTextarea from "@/uview-ui/components/u-textarea/u-textarea.vue";
import { lawWrittenOpinionGetLawWrittenOpinion, lawWrittenOpinionSend } from "@/api/im.js";
import { isObjNull } from "@/libs/basics-tools.js";
export default {
  name: "SubmissionsUpdate",
  components: {
    UTextarea,
  },
  onLoad(option) {
    this.caseSourceServerV2Id = option.id;
    /* 获取意见书*/
    lawWrittenOpinionGetLawWrittenOpinion({
      caseSourceServerId: this.caseSourceServerV2Id
    }).then(({ data = {} }) => {
      if(!isObjNull(data)){
        this.list.forEach(item => {
          item.value = data[item.key];
        });
      }
    });
  },
  data() {
    return {
      caseSourceServerV2Id: "",
      list: [{
        title: "一、问题描述",
        required: true,
        value: "",
        key: "problemDesc",
        errorTip: "请输入问题描述"
      }, {
        title: "二、咨询建议",
        required: true,
        value: "",
        key: "consultSuggest",
        errorTip: "请输入咨询建议"
      }, {
        title: "三、参考法条",
        value: "",
        key: "referenceLaw"
      }]
    };
  },
  methods: {
    /* sendType （1：直接发送；2；评价后发送）*/
    submitForm(sendType) {
      const data = {};
      /* 查看是否是必填  为空就阻止 并提示*/
      for (const item of this.list) {
        const value = item.value.trim();
        if (item.required && !value) {
          uni.showToast({
            title: item.errorTip,
            icon: "none"
          });
          this.$set(item, "error", true);
          return Promise.reject();
        }
        this.$set(item, "error", false);
        data[item.key] = value;
      }
      /* 保存*/
      return lawWrittenOpinionSend({
        ...data,
        caseSourceServerId: this.caseSourceServerV2Id,
        /* 发送类型（1：直接发送；2；评价后发送）*/
        sendType
      }).then(data => {
        setTimeout(() => {
          uni.navigateBack();
        }, 800);
        return data;
      });
    },
    /* 直接发送  发送类型（1：直接发送；2；评价后发送）*/
    sendDirectly() {
      this.submitForm(1).then(() => {
        uni.showToast({
          title: "发送成功",
          icon: "none"
        });
      });
    },
    /* 评价后发送  发送类型（1：直接发送；2；评价后发送）*/
    sendAfterReview() {
      this.submitForm(2).then(() => {
        uni.showToast({
          title: "保存成功",
          icon: "none"
        });
      });
    }
  }

};
</script>

<style scoped lang="scss">
.submissions{
  height: 100vh;
  background: white;
  padding: 0 16px;
  .form-item{
    padding-bottom: 16px;
    .title{
      line-height: 50px;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      .required{
        color: #EB4738;
        padding-left: 4px;
      }
    }
    .textarea-container{
      padding: 8px;
      background: #F5F5F7;
      border-radius: 4px 4px 4px 4px;
      ::v-deep .u-textarea{
        padding: 0;
        border: none;
        background: #F5F5F7;
      }
      .error-tip{
        font-size: 12px;
        font-weight: 400;
        color: #EB4738;
      }
    }
  }
  .btns{
    padding-top: 12px;
    background: #F5F5F7;
    position: fixed;
    bottom: 0;
    padding-bottom: 0;
    padding-bottom: constant(safe-area-inset-bottom, 0);
    padding-bottom: env(safe-area-inset-bottom, 0);
    left: 0;
    right: 0;
    .btn{
      font-size: 16px;
      font-weight: 500;
      width: 165px;
      text-align: center;
      line-height: 44px;
      border-radius: 68px 68px 68px 68px;
      opacity: 1;
    }
    .btn-border{
      border: 1px solid #3887F5;
      color: #3887F5;
    }
    .btn-c{
      background: #3887F5;
      color: white;
    }
  }
}
</style>
