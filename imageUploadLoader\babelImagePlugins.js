const { verifyFileUpload, uploadFile, getRealPath, isNotUploadImage } = require("./core");

function uploadImage({ filePath, state }) {
  if (!isNotUploadImage(filePath)) {
    return;
  }

  // 拿到当前文件的路径
  const resourcePath = state.file.opts.filename
    .split("\\")
    .slice(0, -1)
    .join("/");

  const allPath = getRealPath(filePath, resourcePath);

  const [imageUrl, fileHash]  = verifyFileUpload(allPath);

  if(!imageUrl) {
    uploadFile(allPath, fileHash);
  }

  return imageUrl;
}

module.exports = (babel) => {
  return {
    visitor: {
      // 遍历对象 里面包含 CallExpression 的节点
      ObjectProperty(path, state) {
        if (path.node.value.type === "CallExpression") {
          if (
            path.node.value.callee.name === "require" &&
            path.node.value.arguments
          ) {
            const filePath = path.node.value.arguments[0].value;

            const imgUrl = uploadImage({ filePath, state });

            if (imgUrl) path.node.value = babel.types.stringLiteral(imgUrl);
          }
        }
      },
      // 遍历数组
      ArrayExpression(path, state) {
        path.node?.elements?.forEach((item, index) => {
          if (item?.type === "CallExpression") {
            if (
              item.callee.name === "require" &&
              item.arguments &&
              item.arguments[0]
            ) {
              const filePath = item.arguments[0].value;

              const imgUrl = uploadImage({ filePath, state });

              if (imgUrl)
                path.node.elements[index] = babel.types.stringLiteral(imgUrl);
            }
          }
        });
      },
    },
  };
};
