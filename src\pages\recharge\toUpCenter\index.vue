<template>
  <div class="to-up-center">
    <div class="tab-bar flex">
      <div
        :class="{active:active==='1'}"
        class="tab-item"
        @click="active='1'"
      >
        线索包充值
      </div>
      <div
        :class="{active:active==='2'}"
        class="tab-item"
        @click="active='2'"
      >
        法临币充值
      </div>
    </div>
    <div class="float-bg" />
    <div class="content-wrapper">
      <clue-pack v-if="active==='1'" />
      <currency v-else />
    </div>
  </div>
</template>

<script>
import CluePack from "@/pages/recharge/components/cluePack.vue";
import Currency from "@/pages/recharge/components/currency.vue";
import { shareAppMessage, shareTimeline } from "@/libs/tools";

export default {
  components: { Currency, CluePack },
  data() {
    return {
      active: '1'
    };
  },
  onLoad(query) {
    this.active = query.active || '1'
  },
  onShareAppMessage() {
    return shareAppMessage({
      title: "充值案源线索包，案源单价低至￥2.99/条；海量本地案…",
      imageUrl: require("@/img/share/<EMAIL>"),
    });
  },
  onShareTimeline(){
    return shareTimeline({
      title: "充值案源线索包，案源单价低至￥2.99/条；海量本地案…",
      imageUrl: require("@/img/share/<EMAIL>"), });
  }
};
</script>

<style lang="scss" scoped>
.to-up-center{
  min-height: 100vh;
  background: #fff;
  .tab-bar{
    padding: 0 16px;
    height: 44px;
    line-height: 44px;
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    color: #FFFFFF;
    background: #3887F5;
    .tab-item{
      flex: 1;
      position: relative;
      &.active{
        font-weight: 500;
        &::after{
          content: '';
          position: absolute;
          bottom: 0;
          left: calc(50% - 7px);
          width: 14px;
          height: 2px;
          background: #FFFFFF;
          border-radius: 70px 70px 70px 70px;
        }
      }
    }
  }
  .float-bg {
    position: absolute;
    width: 100%;
    height: 200px;
    background: linear-gradient(180deg, #3887F5 0%, rgba(56,135,245,0) 100%);
    z-index: 1;
  }
  .content-wrapper{
    position: relative;
    z-index: 2;
  }
}
</style>
