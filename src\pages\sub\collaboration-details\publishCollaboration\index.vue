<template>
  <login-layout>
    <div class="px-[16px]">
      <div class="bg-white rounded-[12px] mt-[12px] px-[16px]">
        <collaboration-label label="发布类型">
          <app-radio
            v-model="data.type"
            :list="theTypeOfReleaseList"
            @handleChange="handleTypeChange"
          />
        </collaboration-label>
        <collaboration-label :label="typeToForm.label1">
          <app-area
            :space="16"
            :hasDefault="hasDefault"
            :defaultValue="cityDefaultValue"
            :value="data.workCityInfo.workCity"
            @confirm="areaChange"
          />
        </collaboration-label>
        <collaboration-label
          :border="false"
          :label="typeToForm.label2"
        >
          <popup-select-type
            v-model="data.bizTypeValue"
            :list="typeToForm.typeList"
            :space="16"
          />
        </collaboration-label>
      </div>
      <div class="mt-[12px] bg-white rounded-[12px] px-[16px] pb-[12px]">
        <collaboration-label label="预算金额（元）">
          <u--input
            v-model="data.amount"
            border="none"
            clearable
            fontSize="28rpx"
            inputAlign="right"
            maxlength="7"
            placeholder="请输入"
            type="digit"
          />
        </collaboration-label>
        <collaboration-label
          :border="false"
          label="详细需求"
          position="top"
        >
          <u--textarea
            v-model="data.info"
            :customBoxStyle="{
              fontSize: '28rpx',
            }"
            :customStyle="{
              backgroundColor: '#F5F5F7',
              padding: '16rpx',
            }"
            :placeholderStyle="{
              fontSize: '28rpx',
            }"
            border="none"
            count
            countBackgroundColor="#F5F5F7"
            height="104"
            maxlength="200"
            placeholder="请输入您本次需求的具体内容和地点 该内容公开可见，请勿泄露敏感信息"
          />
        </collaboration-label>
      </div>
      <div class="rounded-[12px] px-[16px] py-[12px] bg-[#FFFFFF] my-[12px]">
        <div
          class="font-bold text-[16px] text-[#333333] pb-[10px] border-0 border-solid border-b border-[#EEEEEE]"
        >
          其他特殊要求
        </div>
        <collaboration-label
          label="发布紧急程度"
          position="top"
        >
          <u-radio-group v-model="data.urgency">
            <u-radio
              v-for="(item, index) in URGENCY_TYPE"
              :key="index"
              :customStyle="{ marginRight: '48rpx' }"
              :label="item.label"
              :name="item.value"
              labelColor="#333333"
              labelSize="28rpx"
            />
          </u-radio-group>
          <div class="mt-[10px]">
            <collaboration-tip :label="tips.urgency" />
          </div>
        </collaboration-label>
        <collaboration-label
          label="接单律师地域要求"
          position="top"
        >
          <u-radio-group v-model="data.jdAreaLimit">
            <u-radio
              v-for="(item, index) in JD_AREA_LIMIT_TYPE"
              :key="index"
              :customStyle="{ marginRight: '48rpx' }"
              :label="item.label"
              :name="item.value"
              labelColor="#333333"
              labelSize="28rpx"
            />
          </u-radio-group>
          <div class="mt-[10px]">
            <collaboration-tip :label="tips.jdAreaLimit" />
          </div>
        </collaboration-label>
        <collaboration-label
          :border="false"
          label="接单响应人数"
          position="top"
        >
          <u-radio-group v-model="data.maxJdNum">
            <u-radio
              v-for="(item, index) in MAX_JD_NUM_TYPE"
              :key="index"
              :customStyle="{ marginRight: '48rpx' }"
              :label="item.label"
              :name="item.value"
              labelColor="#333333"
              labelSize="28rpx"
            />
          </u-radio-group>
          <div class="mt-[10px]">
            <collaboration-tip :label="tips.maxJdNum" />
          </div>
        </collaboration-label>
      </div>
      <app-bottom>
        <div>
          <img
            v-if="!hasPublishedCollaboration"
            class="block w-[375px] h-[36px] mt-[12px]"
            alt=""
            src="@/pages/sub/collaboration-details/imgs/banner.png"
            @click="firstSubmitState = true"
          >
          <div
            class="px-[16px] pt-[16px] pb-[8px] flex items-center justify-between"
          >
            <div
              class="w-[96px] h-[44px] rounded-[46px] border-[1px] border-solid border-[#CCCCCC] flex items-center justify-center position-relative"
              @click="handleHistoryClick"
            >
              <img
                class="block w-[24px] h-[16px] absolute right-[8px] -top-[12px]"
                alt=""
                src="@/pages/sub/collaboration-details/imgs/Frame1634.png"
              >
              <div class="text-[16px] text-[#666666]">
                历史发布
              </div>
            </div>
            <div
              class="font-bold text-[16px] text-center text-[#FFFFFF] leading-[44px] bg-[#3887F5] rounded-[22px] ml-[12px] flex-1"
              @click="handleSubmit"
            >
              发布
            </div>
          </div>
          <p
            class="flex items-center justify-center text-[12px] h-[32px] text-[#666666]"
            @click="protocolState = true"
          >
            点击「发布」即代表您已阅读同意
            <span class="text-[#3887F5]">《律师协作服务协议》</span>
          </p>
        </div>
      </app-bottom>
      <protocol-pop-up
        v-model="protocolState"
        :protocolContent="protocolC()"
        height="380"
        protocolTitle="律师端协作广场服务说明"
      />
      <first-submit-popup v-model="firstSubmitState" />
      <history-submit-popup
        v-model="historySubmitState"
        @handleClick="handleHistorySubmitClick"
      />
      <submit-share-popup v-model="submitShareState" />
    </div>
  </login-layout>
</template>

<script>
import {
  dataDetailList,
  dataDictionary,
  lawyerCollaborationQueryPubCount,
} from "@/api";
import {
  lawyerCollaborationDetailById,
  lawyerCollaborationMyPublishPage,
  lawyerCollaborationPublish,
} from "@/api/collaboration";
import AppArea from "@/components/AppComponents/AppArea/index.vue";
import AppBottom from "@/components/AppComponents/AppBottom/index.vue";
import AppRadio from "@/components/AppComponents/AppRadio/index.vue";
import { protocolC } from "@/components/login/js/protocol";
import LoginLayout from "@/components/login/LoginLayout.vue";
import ProtocolPopUp from "@/components/protocolPopUp/index.vue";
import {
  COOPERATION_TYPE,
  JD_AREA_LIMIT_TYPE,
  SHARE_TYPE,
  URGENCY_TYPE,
} from "@/enum";
import { isNull, isObjNull, isString } from "@/libs/basics-tools";
import { flbAmount, shareAppMessage, whetherToBindWechat } from "@/libs/tools";
import { appValidator } from "@/libs/validator";
import FirstSubmitPopup from "@/pages/sub/collaboration-details/components/FirstSubmitPopup.vue";
import HistorySubmitPopup from "@/pages/sub/collaboration-details/components/HistorySubmitPopup.vue";
import PopupSelectType from "@/pages/sub/collaboration-details/components/popupSelectType.vue";
import SubmitSharePopup from "@/pages/sub/collaboration-details/components/SubmitSharePopup.vue";
import CollaborationLabel from "@/pages/sub/collaboration-details/publishCollaboration/CollaborationLabel.vue";
import CollaborationTip from "@/pages/sub/collaboration-details/publishCollaboration/CollaborationTip.vue";
import URadioGroup from "@/uview-ui/components/u-radio-group/u-radio-group.vue";
import URadio from "@/uview-ui/components/u-radio/u-radio.vue";
import { contentShare } from "@/api/user";
import { toCollaborationDetailsPath } from "@/libs/turnPages";

export default {
  name: "PublishCollaboration",
  components: {
    LoginLayout,
    URadio,
    URadioGroup,
    CollaborationTip,
    CollaborationLabel,
    AppBottom,
    ProtocolPopUp,
    PopupSelectType,
    AppArea,
    AppRadio,
    FirstSubmitPopup,
    HistorySubmitPopup,
    SubmitSharePopup,
  },
  data() {
    return {
      protocolState: false,
      firstSubmitState: false,
      historySubmitState: false,
      submitShareState: false,
      data: {
        /* 发布类型*/
        type: "1",
        /* 城市信息*/
        workCityInfo: {
          cityName: undefined,
          workCity: undefined,
        },
        /* 类型*/
        bizTypeValue: "",
        /* 预算金额(分)*/
        amount: "",
        /* 详细需求*/
        info: "",
        /** 发布紧急程度（1.普通发布，2.加急发布） */
        urgency: 1,
        /** 接单律师地域要求（1.全国律师，2.仅限本地律师） */
        jdAreaLimit: 1,
        /** 最大接单律师人数 */
        maxJdNum: "1",
      },
      /* 发布类型*/
      theTypeOfReleaseList: COOPERATION_TYPE,
      /* 查档类型列表*/
      aListOfFileSearchTypes: [],
      /* 协作类型列表*/
      aListOfCollaborationTypes: [],
      URGENCY_TYPE,
      JD_AREA_LIMIT_TYPE,
      MAX_JD_NUM_TYPE: [],
      cityDefaultValue: {},
      hasDefault: false,
      /** 用户是否发布过协作 */
      hasPublishedCollaboration: false,
      /** 用户当天是否发布过协作 */
      hasPublishedCollaborationToday: false,
    };
  },
  onLoad({ id, showHistoryPopup }) {
    if (id) {
      this.getDetail(id);
    }

    this.checkUserPublishedCollaboration().then(() => {
      if (showHistoryPopup) {
        // https://lanhuapp.com/web/#/item/project/product?pid=a5456a1a-f92e-4584-a941-12bac5769f74&teamId=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=b9f933ab-891b-4191-b09a-11d0262144eb&docId=0c78db2c-1bc7-4e96-b6f0-ca2c0cb896f7&docType=axure&pageId=b5b9e17549434916814cc9a5126b7352&image_id=0c78db2c-1bc7-4e96-b6f0-ca2c0cb896f7&parentId=f0ceebf1-b9cc-4f5a-9c88-0e5cbc89ba61
        this.hasPublishedCollaboration &&
          !this.hasPublishedCollaborationToday &&
          (this.historySubmitState = true);
      }
    });

    /* 获取查档类型列表*/
    dataDetailList({
      groupCode: "LAWYER_COLLABORATION_CD_TYPE",
    }).then(({ data = [] }) => {
      console.log(data);
      this.aListOfFileSearchTypes = data;
    });

    /* 获取协作类型列表*/
    dataDetailList({
      groupCode: "LAWYER_COLLABORATION_XZ_TYPE",
    }).then(({ data = [] }) => {
      console.log(data);
      this.aListOfCollaborationTypes = data;
    });

    // 获取最大接单律师人数
    dataDictionary({ groupCode: "LAWYER_COLLABORATION_MAX_JD_NUM" }).then(
      ({ data = [] }) => {
        this.MAX_JD_NUM_TYPE = data;
        if (!id) this.data.maxJdNum = this.MAX_JD_NUM_TYPE[0].value;
      }
    );
  },
  computed: {
    // https://lanhuapp.com/web/#/item/project/product?tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&pid=19de9c23-261f-40cc-91de-ac18de7ad7be&versionId=eeae073d-acf5-47e9-a7d7-d1de535daad1&docId=807ae283-cdd5-4e5d-b85e-e593a5ebe71f&docType=axure&pageId=ec057836c88d4affbd7da9a53db718b8&image_id=807ae283-cdd5-4e5d-b85e-e593a5ebe71f&parentId=50c0cf21-5365-436f-ab21-22f3bbe3c892
    tips() {
      const urgencyTip = URGENCY_TYPE.find(
        (item) => item.value === this.data.urgency
      ).tip;
      const jdAreaLimitTip = JD_AREA_LIMIT_TYPE.find(
        (item) => item.value === this.data.jdAreaLimit
      ).tip;
      const maxJdNumTip =
        this.MAX_JD_NUM_TYPE[0]?.value === this.data.maxJdNum
          ? `提示：平台默认${this.data.maxJdNum}人可响应，您的需求最多${this.data.maxJdNum}个律师可接单`
          : `提示：您已选择${this.data.maxJdNum}人响应，您的需求最多${this.data.maxJdNum}个律师可接单`;

      return {
        urgency: urgencyTip,
        jdAreaLimit: jdAreaLimitTip,
        maxJdNum: maxJdNumTip,
      };
    },
    /* 发布类型 关联的form*/
    typeToForm() {
      return (
        {
          1: {
            label1: "查档地区",
            label2: "查档类型",
            typeList: this.aListOfFileSearchTypes,
          },
          2: {
            label1: "协作地区",
            label2: "协作类型",
            typeList: this.aListOfCollaborationTypes,
          },
        }[this.data.type] || {}
      );
    },
    rules() {
      return {
        /* 发布类型*/
        type: [{ required: true, message: "请选择发布类型" }],
        /* 城市*/
        workCityInfo: [
          {
            type: "object",
            required: true,
            fields: {
              workCity: [
                { type: "number", required: true, message: "请选择城市" },
              ],
            },
          },
        ],
        /* 发布类型*/
        bizTypeValue: [
          { required: true, message: "请选择" + this.typeToForm.label2 },
        ],
        /* 预算金额*/
        amount: [
          { required: true, message: "请输入预算金额" },
          /* 正则判断数字 最多两位小数*/
          {
            pattern: /^\d+(\.\d{1,2})?$/,
            message: "预算金额只能整数或者最多两位小数",
          },
        ],
        /* 预算金额*/
        info: [{ required: true, message: "请输入详细需求" }],
        urgency: [{ required: true, message: "请选择发布紧急程度" }],
        jdAreaLimit: [{ required: true, message: "请选择接单律师地域要求" }],
        maxJdNum: [{ required: true, message: "请选择接单响应人数" }],
      };
    },
  },
  async onShareAppMessage() {
    // 查询自己发布的最新一条协作
    const res = await lawyerCollaborationMyPublishPage({
      page: 1,
      pageSize: 1,
    });

    const data = res.data.records[0] || {};

    this.$store.commit("share/SET_SHARE_CALLBACK", () => {
      contentShare({
        id: data.id,
        type: SHARE_TYPE.collaboration,
      });
    });

    const typeLabel = data.type === 1 ? "异地查档" : "案件协作";
    const cityName = data.regionName || "全国";

    return shareAppMessage({
      title: `【${cityName}】${typeLabel}需求发布，预算${
        isNull(data.amount) ? "面议" : flbAmount(data.amount)
      }元，欢迎律师朋友接单合作！`,
      imageUrl: require("@/img/share/<EMAIL>"),
      path: isObjNull(data) ? "/pages/index/index" : toCollaborationDetailsPath(data)
    });
  },
  methods: {
    /** 检查用户是否发布过协作 */
    checkUserPublishedCollaboration() {
      return lawyerCollaborationQueryPubCount()
        .then(({ data }) => {
          this.hasPublishedCollaboration = data.count > 0;
          this.hasPublishedCollaborationToday = data.todayCount > 0;
        })
        .catch(() => {
          this.hasPublishedCollaboration = false;
          this.hasPublishedCollaborationToday = false;
        });
    },
    handleHistoryClick() {
      if (!this.hasPublishedCollaboration) {
        uni.showToast({
          title: "暂无历史发布内容",
          icon: "none",
        });

        return;
      }

      this.historySubmitState = true;
    },
    /** 如果在携带 id 到该界面的情况下，则获取详情，然后填入 */
    getDetail(id) {
      lawyerCollaborationDetailById({ id }).then(({ data }) => {
        this.handleHistorySubmitClick(data);
      });
    },
    protocolC() {
      return protocolC;
    },
    handleSubmit() {
      whetherToBindWechat(() => {
        /* 去除空格*/
        this.data = {
          ...this.data,
          info: this.data.info.trim(),
        };
        appValidator(this.data, this.rules).then(() => {
          console.log(this.data);
          /* 后端接受的是分 转一次*/
          const amount = Number(this.data.amount) * 100;
          lawyerCollaborationPublish({
            ...this.data,
            regionCode: this.data.workCityInfo.workCity,
            amount,
            workCityInfo: undefined,
          }).then(() => {
            uni.showToast({
              title: "发布成功",
              icon: "success",
              duration: 2000,
            });

            this.submitShareState = true;
          });
        });
      });
    },
    /* 发布类型切换 需要改变列表值*/
    handleTypeChange() {
      const { bizTypeValue } = this.$options.data().data;
      this.data.bizTypeValue = bizTypeValue;
    },
    /** 地区选择 */
    areaChange(data) {
      this.data.workCityInfo = data;
      console.log(data);
    },
    handleHistorySubmitClick(data) {
      const deepData = { ...data, amount: data.amount / 100 };
      const needData = {};
      /* 统一一下类型 this.data里面声明的什么类型 这里就转换成什么类型 */
      Object.keys(this.data).forEach((key) => {
        const dataItem = this.data[key];
        const deepDataItem = deepData[key];
        if (!isNull(deepDataItem) && isString(dataItem)) {
          needData[key] = String(deepDataItem);
        } else {
          needData[key] = deepDataItem;
        }
      });
      this.data = {
        ...this.data,
        ...needData,
      };

      console.log(this.data, "this.data");

      this.hasDefault = true;
      this.cityDefaultValue = {
        provinceCode: data.provinceCode,
        provinceName: data.provinceName,
        cityCode: data.regionCode,
        cityName: data.regionName,
      };
      this.data.workCityInfo = {
        workCity: data.regionCode,
        cityName: data.regionName,
      };
    },
  },
};
</script>

<style lang="scss" scoped></style>
